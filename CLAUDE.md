# Email Unsubscription Chrome Extension

## Project Overview
A privacy-focused Chrome extension that helps users manage email subscriptions directly within Gmail. All data processing happens locally on the user's device with no external data transmission.

## Technical Architecture

### Technology Stack (Stable & Proven)
- **Chrome Extension**: Manifest V3 (stable since 2021)
- **JavaScript**: Vanilla ES6+ (no frameworks for maximum reliability)
- **Storage**: Chrome Storage API (built-in, stable)
- **UI**: Pure HTML5 + CSS3 with Flexbox/Grid
- **Security**: DOMPurify for XSS protection
- **Build**: Webpack 5.x (stable bundler)
- **Testing**: Jest 29.x (mature testing framework)
- **Linting**: ESLint 8.x (stable)

### Core Components
1. **Content Script** (`content.js`) - Gmail DOM integration and email detection
2. **Background Script** (`background.js`) - Service worker for extension lifecycle
3. **Popup Interface** (`popup.html/js`) - User interface for extension controls
4. **Manifest** (`manifest.json`) - Extension configuration and permissions

## Development Commands

```bash
# Install dependencies
npm install

# Development build with watch
npm run dev

# Production build
npm run build

# Run tests
npm test

# Lint code
npm run lint

# Load extension in Chrome
# 1. Open chrome://extensions/
# 2. Enable Developer mode
# 3. Click "Load unpacked" and select the dist/ folder
```

## Progress Tracking

### Task Reference
All tasks are defined in `email_extension_tasks.md` (single source of truth - never modify)

### Current Development Focus
**Phase 1: Core MVP + Subscription System (COMPLETED)**
- ✅ **Task #1**: Gmail Integration & Email Detection (COMPLETED)
- ✅ **Task #2**: Basic Unsubscribe Link Extraction (COMPLETED)
- ✅ **Task #3**: One-Click Unsubscribe Interface (COMPLETED)
- ✅ **Premium Tier System**: Dynamic subscription tiers (COMPLETED)

### Task Status Tracking
- ✅ **Task #18**: Extension Manifest & Permissions (COMPLETED)
- ✅ **Task #1**: Gmail Integration & Email Detection (COMPLETED)
- ✅ **Task #2**: Basic Unsubscribe Link Extraction (COMPLETED)
- ✅ **Task #3**: One-Click Unsubscribe Interface (COMPLETED)
- ✅ **Subscription System**: Premium tier implementation (COMPLETED)

## Security & Privacy Principles

### Data Privacy
- All email analysis happens locally in the browser
- No data transmission to external servers
- Chrome Storage API used for local data persistence
- Sensitive data encrypted before storage

### Security Measures
- Minimal permissions (activeTab, storage, scripting only)
- Content Security Policy properly configured
- DOMPurify for XSS protection
- URL validation for unsubscribe links
- Domain reputation checks

### Permission Justification
- **activeTab**: Required to access Gmail interface
- **storage**: Required for local data persistence
- **scripting**: Required to inject content scripts into Gmail

## Navigation & Development Workflow

### Session Startup Protocol
**EVERY NEW SESSION**: Always read these documents in order:
1. `llm_implementation_instructions.md` - Task integration guide
2. `updated_implementation_plan.md` - Tasks 6-11 specifications  
3. `email_extension_tasks.md` - Tasks 12-25 reference
4. `CLAUDE.md` - This file for project context

### Implementation Flow Understanding
- **Tasks 1-5**: COMPLETED (foundation work done)
- **Tasks 6-11**: Use UPDATED implementation plan (dashboard approach)
- **Tasks 12-25**: Use ORIGINAL task list
- **Architecture Pivot**: From one-click buttons to comprehensive dashboard

### Before Searching Codebase
**IMPORTANT**: Always refer to Serena MCP and memory MCP first:
- Use `mcp__serena__read_memory` to check existing memories
- Key memories: `implementation_flow_guide`, `session_startup_checklist`, `architecture_pivot_details`
- Use `mcp__serena__find_symbol` for symbol-based searches
- Use `mcp__serena__search_for_pattern` for text-based searches
- Use `mcp__serena__get_symbols_overview` for file structure understanding

### Memory Management
- **implementation_flow_guide**: Complete task flow and document hierarchy
- **session_startup_checklist**: Required reading and workflow
- **architecture_pivot_details**: Dashboard approach technical details
- **complete_folder_structure**: Project structure and file descriptions
- **project_structure**: Overall project architecture
- **project_overview**: High-level project information
- **tech_stack**: Technology stack details
- **chrome_extension_specifics**: Chrome extension specific information
- **code_style_conventions**: Code style and conventions
- **suggested_commands**: Recommended commands and workflows
- **task_completion_workflow**: Task completion guidelines

This saves tokens and provides efficient navigation without redundant file searches.

## Development Guidelines

### Code Standards
- Use stable, proven technologies over latest versions
- Vanilla JavaScript preferred over frameworks
- Comprehensive error handling with try-catch blocks
- Local logging only (no external analytics)
- 90%+ test coverage required

### Performance Requirements
- Gmail load time increase <200ms
- Memory usage <50MB
- CPU usage <5% during normal operation
- Extension popup loads within 1 second

## Project Structure

```
unsubscriber/
├── src/
│   ├── content.js          # Gmail integration
│   ├── background.js       # Service worker
│   ├── popup.html          # Extension UI
│   ├── popup.js            # UI logic
│   └── styles.css          # Styling
├── manifest.json           # Extension manifest
├── package.json           # Dependencies
├── webpack.config.js      # Build configuration
├── email_extension_tasks.md # Task definitions (READ ONLY)
└── CLAUDE.md             # This file
```

## Premium Subscription System (IMPLEMENTED)

### Subscription Tiers
The extension now supports dynamic subscription tiers with different monthly limits:

#### **Free Tier** (Default)
- **Monthly Limit**: 20 unsubscribes
- **Price**: $0
- **Features**: 
  - Basic email scanning
  - Basic unsubscribe functionality
  - Security warnings
  - Usage tracking

#### **Premium Tier**
- **Monthly Limit**: 500 unsubscribes  
- **Price**: $4.99/month
- **Features**:
  - All Free features
  - Advanced analytics
  - Bulk operations
  - AI categorization
  - Priority support

#### **Unlimited Tier**
- **Monthly Limit**: ∞ (unlimited)
- **Price**: $9.99/month
- **Features**:
  - All Premium features
  - Unlimited unsubscribes
  - Multi-account support
  - Team features
  - Enterprise support

### Technical Implementation

#### **Backend (background.js)**
- `SUBSCRIPTION_TIERS` configuration object
- Dynamic limit checking in `getStats()` and `incrementUsage()`
- `upgradeSubscription()` method for tier changes
- `getSubscriptionTiers()` for available options
- Storage schema includes subscription data

#### **Frontend (popup.js/html)**
- Dynamic UI updates based on subscription tier
- Tier badges with color coding (Free=Green, Premium=Blue, Unlimited=Gold)
- Progress bars with special styling for unlimited users
- Subscription status display in footer
- Real-time limit calculations (20 → 500 → ∞)

### Testing Features (DEBUG MODE ONLY)

#### **Testing Buttons in Popup**
When `debugMode = true` in popup.js, testing controls appear:
- 🆓 **Free Tier** button (sets to 20/month limit)
- ⭐ **Premium Tier** button (sets to 500/month limit) 
- 🚀 **Unlimited Tier** button (sets to ∞ limit)
- **+1 Usage** button (increment counter)
- **Reset Count** button (reset to 0)
- **Show Stats** button (display current stats)

#### **Testing Workflow**
1. Click tier buttons to switch subscription levels
2. Watch UI update in real-time (limits, badges, progress bars)
3. Test usage increment and limit behavior
4. Verify all tiers work correctly

## 🚨 PRODUCTION DEPLOYMENT CHECKLIST

### **CRITICAL: Remove Testing Features**
Before production deployment, you MUST:

1. **Disable Debug Mode**:
   ```javascript
   // In popup.js constructor
   this.debugMode = false  // Change from true to false
   ```

2. **Remove Testing Section**:
   - Testing buttons will automatically hide when `debugMode = false`
   - Or manually remove the testing section from `popup.html`

3. **Clean Up Console Logs**:
   - Review and remove excessive console.log statements
   - Keep only essential error logging

4. **Update Version Number**:
   - Increment version in `manifest.json`
   - Update version in `popup.html` header

### **Production Features Ready**
✅ Core unsubscribe functionality
✅ Dynamic subscription tiers
✅ Secure local storage
✅ UI updates based on subscription
✅ Usage tracking and limits
✅ Error handling and validation

### **Next Development Tasks**
1. **Payment Integration**: Add Stripe/PayPal for actual payments
2. **Dashboard Enhancement**: Advanced analytics for premium users
3. **Bulk Operations**: Enhanced bulk unsubscribe for premium tiers
4. **Multi-Account**: Support multiple Gmail accounts (unlimited tier)
5. **Team Features**: Shared subscription management (unlimited tier)

## Notes
- Focus on reliability and user privacy
- Use stable technologies with strong community support
- Test extensively across different Gmail layouts
- Maintain backward compatibility with older Chrome versions
- **Always test subscription tiers before production deployment**

## Session Management
- Premium tier system is fully implemented and functional
- Testing buttons provide easy tier switching during development
- Remember to disable debug mode for production builds