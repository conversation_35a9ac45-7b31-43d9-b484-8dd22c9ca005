// Import logger for debugging
const ExtensionLogger = {
  log: console.log,
  error: console.error,
  warn: console.warn,
  info: console.info,
  debug: console.debug,
  COMPONENTS: { BACKGROUND: 'Background', STORAGE: 'Storage', RUNTIME: 'Runtime', TABS: 'Tabs' }
}

// Subscription tier system
const SUBSCRIPTION_TIERS = {
  free: {
    name: 'Free',
    monthlyLimit: 20,
    price: 0,
    features: [
      'Basic email scanning',
      'Basic unsubscribe',
      'Security warnings',
      'Usage tracking'
    ]
  },
  premium: {
    name: 'Premium',
    monthlyLimit: 500,
    price: 4.99,
    features: [
      'All Free features',
      'Advanced analytics',
      'Bulk operations',
      'AI categorization',
      'Priority support'
    ]
  },
  unlimited: {
    name: 'Unlimited',
    monthlyLimit: -1, // -1 means unlimited
    price: 9.99,
    features: [
      'All Premium features',
      'Unlimited unsubscribes',
      'Multi-account support',
      'Team features',
      'Enterprise support'
    ]
  }
}

class EmailUnsubscriberBackground {
  constructor () {
    this.debugMode = true
    this.init()
  }

  init () {
    ExtensionLogger.info(ExtensionLogger.COMPONENTS.BACKGROUND, 'Background service worker initializing')

    this.setupEventListeners()
    this.initializeErrorTracking()

    if (this.debugMode) {
      this.setupDebugTools()
    }

    ExtensionLogger.info(ExtensionLogger.COMPONENTS.BACKGROUND, 'Background service worker loaded successfully')
  }

  setupEventListeners () {
    // Handle extension installation
    chrome.runtime.onInstalled.addListener((details) => {
      this.handleInstallation(details)
    })

    // Handle extension startup
    chrome.runtime.onStartup.addListener(() => {
      this.handleStartup()
    })

    // Handle messages from content script and popup
    chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
      this.handleMessage(request, sender, sendResponse)
    })

    // Handle tab updates to inject content script if needed
    chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
      this.handleTabUpdate(tabId, changeInfo, tab)
    })
  }

  async handleInstallation (details) {
    console.log('Extension installed:', details)

    try {
      // Initialize storage with default values
      await this.initializeStorage()

      if (details.reason === 'install') {
        // Show welcome message for new installations
        this.showWelcomeNotification()
      } else if (details.reason === 'update') {
        // Handle updates
        this.handleUpdate(details)
      }
    } catch (error) {
      console.error('Error during installation:', error)
    }
  }

  async initializeStorage () {
    const defaultData = {
      monthlyCount: 0,
      lastResetDate: Date.now(),
      totalUnsubscribes: 0,
      subscription: {
        tier: 'free',
        validUntil: null,
        purchaseDate: null,
        autoRenew: false,
        trialUsed: false
      },
      settings: {
        notifications: true,
        autoScan: true,
        securityWarnings: true
      },
      statistics: {
        detectedEmails: 0,
        successfulUnsubscribes: 0,
        blockedSuspiciousLinks: 0
      }
    }

    // Only set defaults if they don't exist
    const existingData = await chrome.storage.local.get(Object.keys(defaultData))

    const dataToSet = {}
    for (const [key, value] of Object.entries(defaultData)) {
      if (!(key in existingData)) {
        dataToSet[key] = value
      }
    }

    if (Object.keys(dataToSet).length > 0) {
      await chrome.storage.local.set(dataToSet)
      console.log('Initialized storage with defaults:', dataToSet)
    }
  }

  handleStartup () {
    console.log('Extension started')
    this.checkMonthlyReset()
  }

  async checkMonthlyReset () {
    try {
      const result = await chrome.storage.local.get(['lastResetDate', 'monthlyCount'])
      const lastReset = result.lastResetDate || 0
      const currentMonth = new Date().getMonth()
      const currentYear = new Date().getFullYear()
      const lastResetDate = new Date(lastReset)

      if (lastResetDate.getMonth() !== currentMonth ||
                lastResetDate.getFullYear() !== currentYear) {
        await chrome.storage.local.set({
          monthlyCount: 0,
          lastResetDate: Date.now()
        })

        console.log('Monthly count reset')
      }
    } catch (error) {
      console.error('Error checking monthly reset:', error)
    }
  }

  handleMessage (request, sender, sendResponse) {
    switch (request.action) {
      case 'getStats':
        this.getStats().then(stats => {
          sendResponse({ success: true, stats })
        }).catch(error => {
          sendResponse({ success: false, error: error.message })
        })
        return true

      case 'incrementUsage':
        console.log('🔄 Background: incrementUsage message received')
        this.incrementUsage().then(result => {
          console.log('✅ Background: incrementUsage result:', result)
          sendResponse(result)
        }).catch(error => {
          console.error('❌ Background: incrementUsage error:', error)
          sendResponse({ success: false, error: error.message })
        })
        return true

      case 'logUnsubscribe':
        this.logUnsubscribe(request.data).then(result => {
          sendResponse(result)
        }).catch(error => {
          sendResponse({ success: false, error: error.message })
        })
        return true

      case 'updateSettings':
        this.updateSettings(request.settings).then(result => {
          sendResponse(result)
        }).catch(error => {
          sendResponse({ success: false, error: error.message })
        })
        return true

      case 'reportFalsePositive':
        this.reportFalsePositive(request.domain, request.url).then(result => {
          sendResponse(result)
        }).catch(error => {
          sendResponse({ success: false, error: error.message })
        })
        return true

      case 'setManualOverride':
        this.setManualOverride(request.domain, request.trustLevel).then(result => {
          sendResponse(result)
        }).catch(error => {
          sendResponse({ success: false, error: error.message })
        })
        return true

      case 'upgradeSubscription':
        this.upgradeSubscription(request.tier, request.duration).then(result => {
          sendResponse(result)
        }).catch(error => {
          sendResponse({ success: false, error: error.message })
        })
        return true

      case 'getSubscriptionTiers':
        sendResponse({ success: true, tiers: SUBSCRIPTION_TIERS })
        return true

      default:
        sendResponse({ success: false, error: 'Unknown action' })
    }
  }

  async handleTabUpdate (tabId, changeInfo, tab) {
    // Check if the tab is Gmail and inject content script if needed
    if (changeInfo.status === 'complete' &&
            tab.url &&
            tab.url.includes('mail.google.com')) {
      try {
        // Check if content script is already injected
        const response = await chrome.tabs.sendMessage(tabId, { action: 'ping' })
        if (!response) {
          // Inject content script if not present
          await chrome.scripting.executeScript({
            target: { tabId },
            files: ['content.js']
          })
        }
      } catch (error) {
        // Content script not present, inject it
        try {
          await chrome.scripting.executeScript({
            target: { tabId },
            files: ['content.js']
          })
        } catch (injectionError) {
          console.error('Failed to inject content script:', injectionError)
        }
      }
    }
  }

  async getStats () {
    try {
      const data = await chrome.storage.local.get([
        'monthlyCount',
        'totalUnsubscribes',
        'statistics',
        'lastResetDate',
        'subscription'
      ])

      // Get user's subscription tier and corresponding limits
      const subscription = data.subscription || { tier: 'free' }
      const tierConfig = SUBSCRIPTION_TIERS[subscription.tier] || SUBSCRIPTION_TIERS.free
      const monthlyLimit = tierConfig.monthlyLimit
      const monthlyCount = data.monthlyCount || 0

      // Calculate remaining and limit status
      const remainingCount = monthlyLimit === -1 ? -1 : Math.max(0, monthlyLimit - monthlyCount)
      const limitReached = monthlyLimit !== -1 && monthlyCount >= monthlyLimit

      // Calculate days until reset
      const lastReset = new Date(data.lastResetDate || Date.now())
      const nextMonth = new Date(lastReset.getFullYear(), lastReset.getMonth() + 1, 1)
      const daysUntilReset = Math.ceil((nextMonth - Date.now()) / (1000 * 60 * 60 * 24))

      return {
        monthlyCount,
        monthlyLimit,
        remainingCount,
        limitReached,
        daysUntilReset,
        totalUnsubscribes: data.totalUnsubscribes || 0,
        subscription: {
          tier: subscription.tier,
          tierName: tierConfig.name,
          price: tierConfig.price,
          features: tierConfig.features,
          validUntil: subscription.validUntil,
          isExpired: subscription.validUntil ? new Date(subscription.validUntil) < new Date() : false
        },
        statistics: data.statistics || {
          detectedEmails: 0,
          successfulUnsubscribes: 0,
          blockedSuspiciousLinks: 0
        }
      }
    } catch (error) {
      console.error('Error getting stats:', error)
      throw error
    }
  }

  async incrementUsage () {
    try {
      const result = await chrome.storage.local.get(['monthlyCount', 'subscription'])
      const currentCount = result.monthlyCount || 0
      const subscription = result.subscription || { tier: 'free' }

      // Get user's subscription tier and limits
      const tierConfig = SUBSCRIPTION_TIERS[subscription.tier] || SUBSCRIPTION_TIERS.free
      const monthlyLimit = tierConfig.monthlyLimit

      // Check if user has reached their tier's limit (unless unlimited)
      if (monthlyLimit !== -1 && currentCount >= monthlyLimit) {
        return {
          success: false,
          error: 'Monthly limit reached',
          monthlyCount: currentCount,
          monthlyLimit,
          limitReached: true,
          subscription: {
            tier: subscription.tier,
            tierName: tierConfig.name
          }
        }
      }

      const newCount = currentCount + 1

      await chrome.storage.local.set({
        monthlyCount: newCount
      })

      return {
        success: true,
        monthlyCount: newCount,
        monthlyLimit,
        limitReached: false,
        subscription: {
          tier: subscription.tier,
          tierName: tierConfig.name
        }
      }
    } catch (error) {
      console.error('Error incrementing usage:', error)
      throw error
    }
  }

  async logUnsubscribe (data) {
    try {
      const result = await chrome.storage.local.get([
        'totalUnsubscribes',
        'statistics'
      ])

      const totalUnsubscribes = (result.totalUnsubscribes || 0) + 1
      const statistics = result.statistics || {
        detectedEmails: 0,
        successfulUnsubscribes: 0,
        blockedSuspiciousLinks: 0
      }

      statistics.successfulUnsubscribes += 1

      await chrome.storage.local.set({
        totalUnsubscribes,
        statistics
      })

      return { success: true, totalUnsubscribes, statistics }
    } catch (error) {
      console.error('Error logging unsubscribe:', error)
      throw error
    }
  }

  async updateSettings (newSettings) {
    try {
      const result = await chrome.storage.local.get(['settings'])
      const currentSettings = result.settings || {}

      const updatedSettings = {
        ...currentSettings,
        ...newSettings
      }

      await chrome.storage.local.set({
        settings: updatedSettings
      })

      return { success: true, settings: updatedSettings }
    } catch (error) {
      console.error('Error updating settings:', error)
      throw error
    }
  }

  showWelcomeNotification () {
    // Note: notifications require permission in manifest
    console.log('Welcome to Email Unsubscriber! Navigate to Gmail to start managing your subscriptions.')
  }

  handleUpdate (details) {
    console.log('Extension updated from version:', details.previousVersion)
    // Handle any migration logic here if needed
  }

  // User feedback system methods
  async reportFalsePositive (domain, url) {
    try {
      // Send message to content script to handle the false positive report
      const tabs = await chrome.tabs.query({ active: true, currentWindow: true })
      if (tabs.length > 0) {
        const response = await chrome.tabs.sendMessage(tabs[0].id, {
          action: 'reportFalsePositive',
          domain,
          url
        })
        return response
      }
      return { success: false, error: 'No active tab found' }
    } catch (error) {
      console.error('Error reporting false positive:', error)
      return { success: false, error: error.message }
    }
  }

  async setManualOverride (domain, trustLevel) {
    try {
      // Send message to content script to handle the manual override
      const tabs = await chrome.tabs.query({ active: true, currentWindow: true })
      if (tabs.length > 0) {
        const response = await chrome.tabs.sendMessage(tabs[0].id, {
          action: 'setManualOverride',
          domain,
          trustLevel
        })
        return response
      }
      return { success: false, error: 'No active tab found' }
    } catch (error) {
      console.error('Error setting manual override:', error)
      return { success: false, error: error.message }
    }
  }

  async logUserFeedback (domain, feedbackType, data) {
    try {
      // Store user feedback for analytics and improvement
      const feedbackData = {
        domain,
        feedbackType, // 'false_positive' or 'manual_override'
        data,
        timestamp: new Date().toISOString(),
        userAgent: navigator.userAgent,
        extensionVersion: chrome.runtime.getManifest().version
      }

      // Get existing feedback
      const result = await chrome.storage.local.get(['feedbackLog'])
      const feedbackLog = result.feedbackLog || []

      // Add new feedback (keep only last 100 entries)
      feedbackLog.push(feedbackData)
      if (feedbackLog.length > 100) {
        feedbackLog.shift()
      }

      // Save updated feedback log
      await chrome.storage.local.set({ feedbackLog })

      return { success: true, message: 'Feedback logged successfully' }
    } catch (error) {
      ExtensionLogger.error(ExtensionLogger.COMPONENTS.STORAGE, 'Error logging user feedback', error)
      return { success: false, error: error.message }
    }
  }

  async upgradeSubscription (tier, duration = 'monthly') {
    try {
      // Validate tier
      if (!SUBSCRIPTION_TIERS[tier]) {
        return { success: false, error: 'Invalid subscription tier' }
      }

      const tierConfig = SUBSCRIPTION_TIERS[tier]
      const currentDate = new Date()

      // Calculate subscription validity period
      let validUntil
      if (duration === 'monthly') {
        validUntil = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, currentDate.getDate())
      } else if (duration === 'yearly') {
        validUntil = new Date(currentDate.getFullYear() + 1, currentDate.getMonth(), currentDate.getDate())
      } else {
        return { success: false, error: 'Invalid duration. Use "monthly" or "yearly"' }
      }

      // Update subscription data
      const subscriptionData = {
        tier,
        validUntil: validUntil.getTime(),
        purchaseDate: currentDate.getTime(),
        autoRenew: true,
        trialUsed: true
      }

      await chrome.storage.local.set({ subscription: subscriptionData })

      return {
        success: true,
        subscription: {
          tier,
          tierName: tierConfig.name,
          validUntil: validUntil.getTime(),
          price: tierConfig.price,
          features: tierConfig.features
        },
        message: `Successfully upgraded to ${tierConfig.name} tier`
      }
    } catch (error) {
      console.error('Error upgrading subscription:', error)
      return { success: false, error: error.message }
    }
  }

  // ==================== ERROR TRACKING & DEBUGGING ====================

  /**
   * Initialize error tracking system
   */
  initializeErrorTracking () {
    ExtensionLogger.info(ExtensionLogger.COMPONENTS.BACKGROUND, 'Initializing error tracking')

    // Set up global error handlers
    self.addEventListener('error', (event) => {
      this.handleGlobalError(event)
    })

    self.addEventListener('unhandledrejection', (event) => {
      this.handleUnhandledRejection(event)
    })

    // Track extension lifecycle events
    chrome.runtime.onSuspend.addListener(() => {
      ExtensionLogger.info(ExtensionLogger.COMPONENTS.RUNTIME, 'Extension being suspended')
    })

    chrome.runtime.onSuspendCanceled.addListener(() => {
      ExtensionLogger.info(ExtensionLogger.COMPONENTS.RUNTIME, 'Extension suspension canceled')
    })

    ExtensionLogger.info(ExtensionLogger.COMPONENTS.BACKGROUND, 'Error tracking initialized')
  }

  /**
   * Handle global errors
   */
  handleGlobalError (event) {
    ExtensionLogger.error(ExtensionLogger.COMPONENTS.RUNTIME, 'Global error caught', {
      message: event.message,
      filename: event.filename,
      lineno: event.lineno,
      colno: event.colno,
      stack: event.error?.stack
    })
  }

  /**
   * Handle unhandled promise rejections
   */
  handleUnhandledRejection (event) {
    ExtensionLogger.error(ExtensionLogger.COMPONENTS.RUNTIME, 'Unhandled promise rejection', {
      reason: event.reason,
      promise: event.promise,
      stack: event.reason?.stack
    })
  }

  /**
   * Set up debugging tools
   */
  setupDebugTools () {
    ExtensionLogger.info(ExtensionLogger.COMPONENTS.BACKGROUND, 'Setting up debug tools')

    // Make debugging utilities available globally
    self.BackgroundDebugger = {
      getExtensionInfo: () => {
        const manifest = chrome.runtime.getManifest()
        const info = {
          version: manifest.version,
          name: manifest.name,
          id: chrome.runtime.id,
          installType: 'unknown',
          updateUrl: manifest.update_url
        }

        ExtensionLogger.info(ExtensionLogger.COMPONENTS.BACKGROUND, 'Extension info', info)
        return info
      },

      getStorageUsage: async () => {
        try {
          const usage = await chrome.storage.local.getBytesInUse()
          const info = {
            bytesInUse: usage,
            mbInUse: (usage / 1024 / 1024).toFixed(2),
            quota: chrome.storage.local.QUOTA_BYTES,
            percentUsed: ((usage / chrome.storage.local.QUOTA_BYTES) * 100).toFixed(2)
          }

          ExtensionLogger.info(ExtensionLogger.COMPONENTS.STORAGE, 'Storage usage', info)
          return info
        } catch (error) {
          ExtensionLogger.error(ExtensionLogger.COMPONENTS.STORAGE, 'Failed to get storage usage', error)
          return null
        }
      },

      clearAllStorage: async () => {
        try {
          await chrome.storage.local.clear()
          ExtensionLogger.info(ExtensionLogger.COMPONENTS.STORAGE, 'All storage cleared')
          return { success: true }
        } catch (error) {
          ExtensionLogger.error(ExtensionLogger.COMPONENTS.STORAGE, 'Failed to clear storage', error)
          return { success: false, error: error.message }
        }
      },

      getActiveConnections: () => {
        // This would track active message ports, etc.
        const connections = {
          contentScripts: 0,
          popups: 0,
          devtools: 0
        }

        ExtensionLogger.info(ExtensionLogger.COMPONENTS.RUNTIME, 'Active connections', connections)
        return connections
      },

      simulateError: (type = 'error') => {
        if (type === 'error') {
          throw new Error('Simulated error for testing')
        } else if (type === 'rejection') {
          Promise.reject(new Error('Simulated promise rejection'))
        }
      },

      getSystemInfo: async () => {
        try {
          const info = {
            platform: 'unknown',
            arch: 'unknown',
            nacl_arch: 'unknown'
          }

          if (chrome.runtime.getPlatformInfo) {
            const platformInfo = await chrome.runtime.getPlatformInfo()
            info.platform = platformInfo.os
            info.arch = platformInfo.arch
            info.nacl_arch = platformInfo.nacl_arch
          }

          ExtensionLogger.info(ExtensionLogger.COMPONENTS.RUNTIME, 'System info', info)
          return info
        } catch (error) {
          ExtensionLogger.error(ExtensionLogger.COMPONENTS.RUNTIME, 'Failed to get system info', error)
          return null
        }
      }
    }

    // Add ping handler for health checks
    chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
      if (request.action === 'ping') {
        ExtensionLogger.debug(ExtensionLogger.COMPONENTS.RUNTIME, 'Ping received', { sender: sender.id })
        sendResponse({ success: true, timestamp: Date.now() })
        return true
      }
    })

    ExtensionLogger.info(ExtensionLogger.COMPONENTS.BACKGROUND, 'Debug tools ready')
  }

  /**
   * Enhanced error reporting with context
   */
  reportError (error, context = {}) {
    const errorReport = {
      timestamp: new Date().toISOString(),
      message: error.message,
      stack: error.stack,
      name: error.name,
      context,
      extensionId: chrome.runtime.id,
      version: chrome.runtime.getManifest().version,
      userAgent: navigator.userAgent
    }

    ExtensionLogger.error(ExtensionLogger.COMPONENTS.RUNTIME, 'Error reported', errorReport)

    // Store error report for debugging
    chrome.storage.local.get(['errorReports'], (result) => {
      const reports = result.errorReports || []
      reports.push(errorReport)

      // Keep only last 50 error reports
      const recentReports = reports.slice(-50)

      chrome.storage.local.set({ errorReports: recentReports })
    })

    return errorReport
  }

  /**
   * Get error reports for debugging
   */
  getErrorReports () {
    return new Promise((resolve) => {
      chrome.storage.local.get(['errorReports'], (result) => {
        resolve(result.errorReports || [])
      })
    })
  }

  /**
   * Clear error reports
   */
  clearErrorReports () {
    return new Promise((resolve) => {
      chrome.storage.local.set({ errorReports: [] }, () => {
        ExtensionLogger.info(ExtensionLogger.COMPONENTS.STORAGE, 'Error reports cleared')
        resolve()
      })
    })
  }
}

// Initialize background service worker
const emailUnsubscriberBackground = new EmailUnsubscriberBackground()
self.emailUnsubscriberBackground = emailUnsubscriberBackground
