/**
 * Gmail Unsubscriber Content Script Entry Point
 * Initializes the modular content script components
 */

// Import modular components (these will be bundled by webpack)
import { GmailUnsubscriberContent } from './src/content/gmail-unsubscriber.js'

// Import logger for debugging
const ExtensionLogger = window.ExtensionLogger || {
  log: console.log,
  error: console.error,
  warn: console.warn,
  info: console.info,
  debug: console.debug,
  COMPONENTS: { GMAIL: 'Gmail', CONTENT: 'Content', EXTRACTION: 'Extraction' }
}

// Cleanup function for when extension context is invalidated
function cleanupContentScript () {
  if (window.gmailUnsubscriber) {
    // Remove any event listeners or observers
    ExtensionLogger.info(ExtensionLogger.COMPONENTS.CONTENT, 'Cleaning up content script due to context invalidation')
    window.gmailUnsubscriber.cleanup()
    window.gmailUnsubscriber = null
  }
}

// Listen for context invalidation
if (chrome && chrome.runtime) {
  chrome.runtime.onConnect.addListener(() => {
    // This will throw an error if context is invalidated
    chrome.runtime.onDisconnect.addListener(cleanupContentScript)
  })
}

// Initialize the content script when DOM is ready
function initializeContentScript () {
  try {
    // Clean up any existing instance
    if (window.gmailUnsubscriber) {
      cleanupContentScript()
    }

    const unsubscriber = new GmailUnsubscriberContent()
    window.gmailUnsubscriber = unsubscriber
  } catch (error) {
    ExtensionLogger.error(ExtensionLogger.COMPONENTS.CONTENT, 'Error initializing content script', error)
  }
}

if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initializeContentScript)
} else {
  initializeContentScript()
}
