/**
 * Gmail Unsubscriber Content Script Entry Point
 * Initializes the modular content script components
 */

import { GmailUnsubscriberContent } from './src/content/gmail-unsubscriber.js';
import { ExtensionLogger } from './src/shared/logger.js';
import { COMPONENTS } from './src/shared/constants.js';
  constructor () {
    ExtensionLogger.info(ExtensionLogger.COMPONENTS.CONTENT, 'Content script initializing')

    this.debugMode = true // Enable debug features
    this.unsubscribePatterns = [
      /unsubscribe/i,
      /opt[- ]?out/i,
      /remove[- ]?me/i,
      /manage[- ]?preferences/i,
      /email[- ]?preferences/i,
      /subscription[- ]?preferences/i,
      /update[- ]?preferences/i,
      /list[- ]?unsubscribe/i,
      /stop[- ]?emails/i,
      /cancel[- ]?subscription/i,
      /leave[- ]?list/i,
      /remove[- ]?from[- ]?list/i,
      /stop[- ]?receiving/i,
      /email[- ]?settings/i,
      /communication[- ]?preferences/i,
      /notification[- ]?settings/i
    ]

    this.dangerousPatterns = [
      /confirm[- ]?subscription/i,
      /verify[- ]?email/i,
      /click[- ]?here[- ]?to[- ]?continue/i,
      /suspicious[- ]?activity/i,
      /verify[- ]?account/i,
      /update[- ]?payment/i,
      /billing[- ]?issue/i,
      /account[- ]?suspended/i,
      /security[- ]?alert/i,
      /immediate[- ]?action/i
    ]

    this.maliciousDomains = [
      'bit.ly', 'tinyurl.com', 'goo.gl', 't.co', 'ow.ly', 'short.link', 'tiny.cc',
      'rebrand.ly', 'is.gd', 'buff.ly', 'x.co', 'lnkd.in', 'amzn.to', 'fb.me',
      'youtu.be', 'ur1.ca', 'v.gd', 'cli.gs', 'qr.net', 'snipurl.com'
    ]

    this.trustedDomains = [
      // Major Email Service Providers
      'gmail.com', 'google.com', 'youtube.com', 'facebook.com', 'twitter.com',
      'linkedin.com', 'instagram.com', 'amazon.com', 'microsoft.com', 'apple.com',
      'github.com', 'stackoverflow.com', 'reddit.com', 'medium.com', 'wordpress.com',

      // Email Marketing Platforms
      'mailchimp.com', 'constantcontact.com', 'aweber.com', 'getresponse.com',
      'campaignmonitor.com', 'sendinblue.com', 'mailgun.com', 'sendgrid.com',
      'hubspot.com', 'pardot.com', 'marketo.com', 'eloqua.com', 'salesforce.com',
      'intercom.com', 'zendesk.com', 'freshdesk.com', 'help.com', 'drift.com',

      // Major Indian Companies & Services
      'nobroker.in', 'netcoredelivery.nobroker.in', 'zomato.com', 'swiggy.com',
      'flipkart.com', 'myntra.com', 'paytm.com', 'phonepe.com', 'razorpay.com',
      'ola.com', 'uber.com', 'makemytrip.com', 'goibibo.com', 'yatra.com', 'naukri.com',
      'www.naukri.com', 'linkedin.com', 'indeed.com', 'monster.com', 'shine.com',
      'bigbasket.com', 'grofers.com', 'dunzo.com', 'urbancompany.com',
      'policybazaar.com', 'bankbazaar.com', 'paisabazaar.com',

      // Transactional Email Services
      'mandrill.com', 'postmark.com', 'ses.amazonaws.com', 'smtp.gmail.com',
      'sparkpost.com', 'mailjet.com', 'pepipost.com', 'netcore.co.in',
      'msg91.com', 'textlocal.in', 'kaleyra.com', 'twilio.com',

      // E-commerce & Retail
      'shopify.com', 'woocommerce.com', 'magento.com', 'bigcommerce.com',
      'squarespace.com', 'wix.com', 'etsy.com', 'ebay.com', 'alibaba.com',
      'aliexpress.com', 'wish.com', 'overstock.com', 'wayfair.com',

      // Financial Services
      'paypal.com', 'stripe.com', 'square.com', 'visa.com', 'mastercard.com',
      'americanexpress.com', 'discover.com', 'chase.com', 'bankofamerica.com',
      'wellsfargo.com', 'citibank.com', 'hsbc.com', 'icicibank.com',
      'hdfcbank.com', 'sbi.co.in', 'axisbank.com', 'kotakbank.com',

      // Major Global Brands
      'nike.com', 'adidas.com', 'coca-cola.com', 'pepsi.com', 'mcdonalds.com',
      'starbucks.com', 'walmart.com', 'target.com', 'costco.com', 'bestbuy.com',
      'homedepot.com', 'lowes.com', 'macys.com', 'nordstrom.com',

      // Travel & Hospitality
      'booking.com', 'expedia.com', 'hotels.com', 'airbnb.com', 'trivago.com',
      'kayak.com', 'priceline.com', 'orbitz.com', 'travelocity.com',
      'marriott.com', 'hilton.com', 'ihg.com', 'hyatt.com', 'radisson.com',

      // Media & Entertainment
      'netflix.com', 'hulu.com', 'disney.com', 'hbo.com', 'showtime.com',
      'spotify.com', 'pandora.com', 'soundcloud.com', 'twitch.tv',
      'cnn.com', 'bbc.com', 'reuters.com', 'bloomberg.com', 'wsj.com',
      'nytimes.com', 'washingtonpost.com', 'theguardian.com', 'forbes.com',
      'foxnews.com',

      // Cloud & Software Services
      'aws.amazon.com', 'azure.microsoft.com', 'cloud.google.com',
      'digitalocean.com', 'heroku.com', 'netlify.com', 'vercel.com',
      'slack.com', 'zoom.us', 'teams.microsoft.com', 'webex.com',
      'dropbox.com', 'box.com', 'onedrive.live.com', 'icloud.com',

      // Education & Learning
      'coursera.org', 'udemy.com', 'edx.org', 'khanacademy.org',
      'skillshare.com', 'lynda.com', 'pluralsight.com', 'codecademy.com',

      // Health & Wellness
      'webmd.com', 'mayoclinic.org', 'healthline.com', 'medicalnewstoday.com',
      'cvs.com', 'walgreens.com', 'rite-aid.com', 'express-scripts.com'
    ]

    // Dynamic trusted domains patterns (subdomains of trusted companies)
    this.trustedDomainPatterns = [
      /.*\.amazon\.(com|in|co\.uk|de|fr|es|it|ca|au|jp|cn)/,
      /.*\.google\.(com|co\.in|co\.uk|de|fr|es|it|ca|au|jp|cn)/,
      /.*\.microsoft\.(com|co\.in|co\.uk|de|fr|es|it|ca|au|jp|cn)/,
      /.*\.apple\.(com|co\.in|co\.uk|de|fr|es|it|ca|au|jp|cn)/,
      /.*\.facebook\.(com|co\.in|co\.uk|de|fr|es|it|ca|au|jp|cn)/,
      /.*\.twitter\.(com|co\.in|co\.uk|de|fr|es|it|ca|au|jp|cn)/,
      /.*\.linkedin\.(com|co\.in|co\.uk|de|fr|es|it|ca|au|jp|cn)/,
      /.*\.github\.(com|io)/,
      /.*\.salesforce\.(com|co\.in|co\.uk|de|fr|es|it|ca|au|jp|cn)/,
      /.*\.hubspot\.(com|co\.in|co\.uk|de|fr|es|it|ca|au|jp|cn)/,
      /.*\.mailchimp\.(com|co\.in|co\.uk|de|fr|es|it|ca|au|jp|cn)/,
      /.*\.sendgrid\.(com|net)/,
      /.*\.mailgun\.(com|net)/,
      /.*\.netcore\.(co\.in|com)/,
      /.*\.nobroker\.(in|com)/,
      /.*\.zomato\.(com|in)/,
      /.*\.swiggy\.(com|in)/,
      /.*\.flipkart\.(com|in)/,
      /.*\.paytm\.(com|in)/,
      /.*\.razorpay\.(com|in)/,
      /.*\.twilio\.(com|net)/,
      /.*\.stripe\.(com|net)/,
      /.*\.paypal\.(com|co\.in|co\.uk|de|fr|es|it|ca|au|jp|cn)/
    ]

    // User feedback and override system
    this.userFeedback = new Map() // Store user feedback on domains
    this.manualOverrides = new Map() // Store manual trust overrides
    this.loadUserPreferences()

    // Bulk scan progress tracking
    this.bulkScanProgress = {
      processed: 0,
      total: 0,
      withUnsubscribe: 0,
      currentEmail: '',
      isRunning: false
    }

    this.init()
  }

  init () {
    // Check if extension context is valid
    if (!this.isExtensionContextValid()) {
      ExtensionLogger.error(ExtensionLogger.COMPONENTS.CONTENT, 'Extension context invalidated, not initializing')
      return
    }

    if (!this.isGmailPage()) {
      ExtensionLogger.warn(ExtensionLogger.COMPONENTS.CONTENT, 'Not on Gmail page, content script will not initialize')
      return
    }

    this.setupMessageListener()
    this.startEmailMonitoring()

    // Initialize debugging tools
    this.initializeDebuggingTools()

    ExtensionLogger.info(ExtensionLogger.COMPONENTS.CONTENT, 'Email Unsubscriber content script loaded on Gmail')
  }

  isExtensionContextValid () {
    try {
      // Try to access chrome.runtime to check if context is valid
      return !!(chrome && chrome.runtime && chrome.runtime.id)
    } catch (error) {
      return false
    }
  }

  isGmailPage () {
    try {
      const hostname = window.location.hostname
      const pathname = window.location.pathname
      const href = window.location.href

      // Check if we're on Gmail domain
      const isGmailDomain = hostname === 'mail.google.com' ||
                           hostname.endsWith('.mail.google.com')

      // More comprehensive Gmail app detection - if we're on the Gmail domain, we're likely in Gmail
      const isGmailApp = isGmailDomain && (
        pathname.startsWith('/mail/') ||
        pathname === '/mail' ||
        pathname === '/' || // Root path on mail.google.com
        href.includes('#inbox') ||
        href.includes('#sent') ||
        href.includes('#drafts') ||
        href.includes('#spam') ||
        href.includes('#trash') ||
        href.includes('#important') ||
        href.includes('#starred') ||
        href.includes('#all') ||
        href.includes('#label/') ||
        href.includes('#category/') ||
        href.includes('#search/') ||
        document.title.toLowerCase().includes('gmail') ||
        document.body.classList.contains('gmail_default') ||
        // Gmail UI elements that indicate we're in Gmail
        document.querySelector('.nH[role="main"]') !== null || // Gmail main content
        document.querySelector('.zA') !== null || // Gmail email list
        document.querySelector('.ii.gt') !== null || // Gmail email content
        document.querySelector('[data-thread-id]') !== null || // Gmail threads
        document.querySelector('[role="main"]') !== null || // Main Gmail interface
        document.querySelector('.gb_9c') !== null || // Gmail header
        document.querySelector('.aeN') !== null || // Gmail navigation
        document.querySelector('.TK .nZ') !== null || // Gmail inbox
        document.querySelector('.aAy') !== null // Gmail compose
      )

      // If we're on Gmail domain, assume we're in Gmail (fallback)
      const result = isGmailDomain && (isGmailApp || true) // Always true if on Gmail domain

      console.log('Gmail page detection:', {
        hostname,
        pathname,
        href: href.substring(0, 100) + (href.length > 100 ? '...' : ''),
        isGmailDomain,
        isGmailApp,
        result,
        title: document.title.substring(0, 50),
        hasMainRole: document.querySelector('[role="main"]') !== null,
        hasZA: document.querySelector('.zA') !== null,
        hasII: document.querySelector('.ii.gt') !== null
      })
      return result
    } catch (error) {
      console.error('Error checking Gmail page:', error)
      return false
    }
  }

  setupMessageListener () {
    chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
      if (request.action === 'ping') {
        sendResponse({ success: true, status: 'content script loaded' })
        return true
      } else if (request.action === 'scanCurrentEmail') {
        console.log('📨 Content script received scanCurrentEmail message')
        this.scanCurrentEmail().then(result => {
          console.log('📤 Content script sending response:', result)
          sendResponse(result)
        }).catch(error => {
          console.error('Error scanning email:', error)
          const errorResponse = { success: false, error: error.message }
          console.log('📤 Content script sending error response:', errorResponse)
          sendResponse(errorResponse)
        })
        return true // Keep message channel open for async response
      } else if (request.action === 'reportFalsePositive') {
        this.reportFalsePositive(request.domain, request.url).then(result => {
          sendResponse(result)
        })
        return true
      } else if (request.action === 'setManualOverride') {
        this.setManualOverride(request.domain, request.trustLevel).then(result => {
          sendResponse(result)
        })
        return true
      } else if (request.action === 'getEmailCount') {
        const count = this.getEmailCount()
        sendResponse({ success: true, count })
      } else if (request.action === 'getBulkScanProgress') {
        const progress = this.getBulkScanProgress()
        sendResponse({ success: true, ...progress })
      } else if (request.action === 'scanAllEmails') {
        this.scanAllEmails(request.options).then(result => {
          sendResponse(result)
        }).catch(error => {
          console.error('Error scanning all emails:', error)
          sendResponse({ success: false, error: error.message })
        })
        return true
      } else if (request.action === 'addToBlacklist') {
        this.addToLocalBlacklist(request.domain, request.reason).then(result => {
          sendResponse(result)
        })
        return true
      } else if (request.action === 'removeFromBlacklist') {
        this.removeFromLocalBlacklist(request.domain).then(result => {
          sendResponse(result)
        })
        return true
      } else if (request.action === 'getBlacklist') {
        this.loadLocalBlacklist().then(result => {
          sendResponse({ success: true, blacklist: result })
        })
        return true
      } else if (request.action === 'getSecurityStats') {
        this.getSecurityStats().then(result => {
          sendResponse(result)
        })
        return true
      }
    })
  }

  startEmailMonitoring () {
    // Monitor DOM changes for new emails
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'childList') {
          mutation.addedNodes.forEach((node) => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              this.processNewContent(node)
            }
          })
        }
      })
    })

    observer.observe(document.body, {
      childList: true,
      subtree: true
    })

    // Initial scan of existing content
    this.processNewContent(document.body)
  }

  async processNewContent (element) {
    // Look for the main email content container only
    const emailContainer = element.querySelector('[data-message-id], .ii.gt, .a3s.aiL') ||
                              element.querySelector('.message, [role="listitem"]')

    if (emailContainer && !emailContainer.hasAttribute('data-unsubscriber-processed')) {
      await this.processEmailContainer(emailContainer)
      emailContainer.setAttribute('data-unsubscriber-processed', 'true')
    }
  }

  async processEmailContainer (container) {
    const unsubscribeLinks = await this.findUnsubscribeLinks(container)

    if (unsubscribeLinks.length > 0) {
      this.addUnsubscribeButton(container, unsubscribeLinks)
    }
  }

  async findUnsubscribeLinks (container) {
    const links = container.querySelectorAll('a[href]')
    const unsubscribeLinks = []

    console.log(`Scanning ${links.length} links for unsubscribe patterns...`)

    for (const link of links) {
      const href = link.getAttribute('href')
      const text = link.textContent.trim()
      const title = link.getAttribute('title') || ''
      const parentText = link.parentElement ? link.parentElement.textContent.trim() : ''

      // Enhanced unsubscribe pattern matching with more flexible regex
      const isUnsubscribeLink = this.unsubscribePatterns.some(pattern =>
        pattern.test(text) || pattern.test(title) || pattern.test(href) || pattern.test(parentText)
      ) || this.isLikelyUnsubscribeLink(href, text, title, parentText)

      // Enhanced debug logging
      if (text.toLowerCase().includes('unsubscribe') || href.toLowerCase().includes('unsubscribe') ||
          text.toLowerCase().includes('opt') || href.toLowerCase().includes('opt') ||
          text.toLowerCase().includes('manage') || href.toLowerCase().includes('preferences')) {
        console.log(`🔍 Found potential unsubscribe link: "${text}" | href: "${href}" | matches: ${isUnsubscribeLink}`)
      }

      // Check for dangerous patterns
      const isDangerous = this.dangerousPatterns.some(pattern =>
        pattern.test(text) || pattern.test(title) || pattern.test(href) || pattern.test(parentText)
      )

      if (isUnsubscribeLink && !isDangerous && this.isLegitimateUnsubscribeLink(href, text, title, parentText)) {
        const phishingCheck = this.detectPhishingPatterns(href, text, title)
        const trustLevel = await this.calculateTrustLevel(href, text, title)

        const linkData = {
          element: link,
          href,
          text,
          title,
          parentText,
          isDangerous: this.isLinkSuspicious(href),
          isValid: this.validateUnsubscribeUrl(href),
          trustLevel,
          phishingCheck,
          extractedAt: new Date().toISOString(),
          securityWarnings: this.generateSecurityWarnings(href, text, title, phishingCheck, trustLevel)
        }

        unsubscribeLinks.push(linkData)
        console.log(`✅ Added unsubscribe link: "${text}" (trust: ${trustLevel})`)
      }
    }

    // Also check for List-Unsubscribe headers and mailto links
    const listUnsubscribeLinks = this.extractListUnsubscribeLinks(container)
    unsubscribeLinks.push(...listUnsubscribeLinks)

    // Store extraction results locally
    this.storeExtractionResults(unsubscribeLinks, container)

    console.log(`Found ${unsubscribeLinks.length} unsubscribe links total`)
    return unsubscribeLinks
  }

  // Enhanced unsubscribe link detection with more flexible patterns
  isLikelyUnsubscribeLink (href, text, title, parentText) {
    // Convert to lowercase for case-insensitive matching
    const lowerHref = href.toLowerCase()
    const lowerText = text.toLowerCase()
    const lowerTitle = title.toLowerCase()
    const lowerParentText = parentText.toLowerCase()

    // URL-based detection patterns
    const urlPatterns = [
      /\/unsubscribe/i,
      /\/optout/i,
      /\/opt-out/i,
      /\/remove/i,
      /\/preferences/i,
      /\/settings/i,
      /\/manage/i,
      /\/stop/i,
      /\/cancel/i,
      /\/unsub/i,
      /unsubscribe/i,
      /optout/i,
      /preferences/i,
      /emailsettings/i,
      /email-settings/i,
      /mailinglist/i,
      /mailing-list/i,
      /newsletter/i,
      /communication/i,
      /notifications/i
    ]

    // Text-based detection patterns (more flexible)
    const textPatterns = [
      'unsubscribe',
      'opt out',
      'optout',
      'opt-out',
      'remove me',
      'remove',
      'stop emails',
      'stop receiving',
      'manage preferences',
      'email preferences',
      'email settings',
      'notification settings',
      'update preferences',
      'communication preferences',
      'manage subscriptions',
      'manage subscription',
      'cancel subscription',
      'leave list',
      'remove from list',
      'turn off',
      'disable',
      'no longer receive',
      'stop sending',
      'click here to stop',
      'click to unsubscribe',
      'one-click unsubscribe',
      'instant unsubscribe',
      'here to unsubscribe',
      'here to opt out',
      'here to remove',
      'here to stop'
    ]

    // Check URL patterns
    if (urlPatterns.some(pattern => pattern.test(lowerHref))) {
      return true
    }

    // Check text patterns
    if (textPatterns.some(pattern =>
      lowerText.includes(pattern) ||
      lowerTitle.includes(pattern) ||
      lowerParentText.includes(pattern)
    )) {
      return true
    }

    // Check for mailto unsubscribe links
    if (lowerHref.startsWith('mailto:') && (
      lowerHref.includes('unsubscribe') ||
      lowerHref.includes('optout') ||
      lowerHref.includes('remove')
    )) {
      return true
    }

    return false
  }

  // Filter out unwanted links that match unsubscribe patterns but aren't actual unsubscribe links
  isLegitimateUnsubscribeLink (href, text, title, parentText) {
    const lowerHref = href.toLowerCase()
    const lowerText = text.toLowerCase()
    const lowerTitle = title.toLowerCase()

    // Filter out common non-unsubscribe links that match patterns
    const unwantedPatterns = [
      /update.*billing/i,
      /billing.*information/i,
      /payment.*method/i,
      /account.*settings/i,
      /profile.*settings/i,
      /privacy.*policy/i,
      /terms.*service/i,
      /help.*center/i,
      /customer.*support/i,
      /contact.*us/i,
      /about.*us/i,
      /our.*company/i,
      /social.*media/i,
      /follow.*us/i,
      /download.*app/i,
      /mobile.*app/i,
      /view.*in.*browser/i,
      /forward.*friend/i,
      /share.*email/i,
      /print.*version/i,
      /save.*pdf/i
    ]

    // Check if this is an unwanted link
    const isUnwanted = unwantedPatterns.some(pattern =>
      pattern.test(text) || pattern.test(title) || pattern.test(href)
    )

    if (isUnwanted) {
      console.log(`🚫 Filtered out unwanted link: "${text}" (${href})`)
      return false
    }

    // Must contain clear unsubscribe intent
    const legitUnsubscribePatterns = [
      /^unsubscribe$/i,
      /^opt[\s\-]?out$/i,
      /^remove[\s\-]?me$/i,
      /unsubscribe[\s\-]?(here|now|link)?$/i,
      /stop[\s\-]?emails?$/i,
      /cancel[\s\-]?subscription$/i,
      /list[\s\-]?unsubscribe$/i,
      /email[\s\-]?preferences$/i,
      /manage[\s\-]?subscription$/i,
      /notification[\s\-]?settings$/i
    ]

    const hasLegitPattern = legitUnsubscribePatterns.some(pattern =>
      pattern.test(text) || pattern.test(title)
    ) || lowerHref.includes('/unsubscribe') || lowerHref.includes('/optout') || lowerHref.includes('/opt-out')

    if (!hasLegitPattern) {
      console.log(`🔍 Questionable unsubscribe link: "${text}" (${href})`)
    }

    return hasLegitPattern
  }

  isLinkSuspicious (href) {
    try {
      const url = new URL(href)

      // Check for malicious domains
      if (this.maliciousDomains.includes(url.hostname)) {
        return true
      }

      // Check for suspicious patterns in URL
      const suspiciousPatterns = [
        /[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}/, // IP addresses
        /[a-f0-9]{32,}/, // Long hex strings
        /[a-zA-Z0-9]{20,}\.tk$|\.ml$|\.ga$|\.cf$/, // Free domains
        /[^a-zA-Z0-9\-.]/g.test(url.hostname), // Non-standard characters
        /\.(exe|zip|rar|bat|cmd|scr|pif|com)$/i.test(url.pathname), // Executable files
        /[a-zA-Z0-9]{50,}/ // Extremely long random strings
      ]

      if (suspiciousPatterns.some(pattern => pattern.test(href))) {
        return true
      }

      // Check for suspicious TLDs
      const suspiciousTlds = ['.tk', '.ml', '.ga', '.cf', '.pw', '.cc', '.info', '.biz']
      if (suspiciousTlds.some(tld => url.hostname.endsWith(tld))) {
        return true
      }

      return false
    } catch (error) {
      return true // Invalid URLs are suspicious
    }
  }

  validateUnsubscribeUrl (href) {
    try {
      const url = new URL(href)

      // Must be HTTP or HTTPS
      if (!['http:', 'https:'].includes(url.protocol)) {
        return false
      }

      // Must have a valid hostname
      if (!url.hostname || url.hostname.length < 3) {
        return false
      }

      // Must have at least one dot in hostname (domain.tld)
      if (!url.hostname.includes('.')) {
        return false
      }

      // Check for valid TLD
      const tld = url.hostname.split('.').pop()
      if (tld.length < 2 || tld.length > 10) {
        return false
      }

      return true
    } catch (error) {
      return false
    }
  }

  async calculateTrustLevel (href, text, title) {
    let trustScore = 30 // Start with base score instead of 0

    try {
      const url = new URL(href)

      // Check local blacklist first
      const blacklistEntry = await this.isInLocalBlacklist(url.hostname)
      if (blacklistEntry) {
        return 0 // Blacklisted domains get 0 trust
      }

      // Check manual overrides first
      const override = this.getManualOverride(url.hostname)
      if (override) {
        return override.trustLevel
      }

      // Skip expensive domain reputation checks for performance
      // Use simple local checks instead

      // Simple SSL check (no certificate validation for performance)
      if (url.protocol === 'https:') {
        trustScore += 20
      }

      // Enhanced phishing pattern detection
      const phishingCheck = this.detectPhishingPatterns(href, text, title)
      if (phishingCheck.isPhishing) {
        trustScore -= phishingCheck.riskScore
      }

      // High trust for known domains (exact match)
      if (this.trustedDomains.includes(url.hostname)) {
        trustScore += 50
      }

      // Check trusted domain patterns (for subdomains)
      const matchesTrustedPattern = this.trustedDomainPatterns.some(pattern =>
        pattern.test(url.hostname)
      )
      if (matchesTrustedPattern && !this.trustedDomains.includes(url.hostname)) {
        trustScore += 45 // Slightly less than exact match
      }

      // Additional HTTPS bonus (already counted above)
      // Skip SSL validation for performance

      // Clear unsubscribe text bonus
      const clearPatterns = [
        /^unsubscribe$/i,
        /^opt[- ]?out$/i,
        /^remove[- ]?me$/i,
        /unsubscribe/i,
        /opt[- ]?out/i,
        /remove[- ]?subscription/i,
        /manage[- ]?preferences/i,
        /email[- ]?preferences/i,
        /notification[- ]?settings/i
      ]

      if (clearPatterns.some(pattern => pattern.test(text))) {
        trustScore += 15
      }

      // Domain reputation bonuses
      if (this.hasGoodDomainReputation(url.hostname)) {
        trustScore += 10
      }

      // Email service provider bonus
      if (this.isEmailServiceProvider(url.hostname)) {
        trustScore += 25
      }

      // Penalty for suspicious characteristics
      if (this.isLinkSuspicious(href)) {
        trustScore -= 30
      }

      // Penalty for malicious domains
      if (this.maliciousDomains.includes(url.hostname)) {
        trustScore -= 40
      }

      // Bonus for legitimate unsubscribe URL patterns
      if (this.hasLegitimateUnsubscribePattern(href)) {
        trustScore += 10
      }

      // Normalize to 0-100 scale
      return Math.max(0, Math.min(100, trustScore))
    } catch (error) {
      return 0
    }
  }

  hasGoodDomainReputation (hostname) {
    // Check for established domain age patterns (simplified heuristic)
    const establishedPatterns = [
      /\.(com|org|net|edu|gov)$/,
      /\.(co\.uk|co\.in|com\.au|de|fr|es|it|ca|jp)$/
    ]

    return establishedPatterns.some(pattern => pattern.test(hostname))
  }

  isEmailServiceProvider (hostname) {
    const emailProviders = [
      'mailchimp.com', 'sendgrid.com', 'mailgun.com', 'constantcontact.com',
      'aweber.com', 'getresponse.com', 'campaignmonitor.com', 'sendinblue.com',
      'hubspot.com', 'pardot.com', 'marketo.com', 'eloqua.com', 'intercom.com',
      'zendesk.com', 'freshdesk.com', 'mandrill.com', 'postmark.com',
      'sparkpost.com', 'mailjet.com', 'pepipost.com', 'netcore.co.in',
      'msg91.com', 'textlocal.in', 'kaleyra.com', 'twilio.com'
    ]

    return emailProviders.some(provider =>
      hostname.includes(provider) || hostname.endsWith(provider)
    )
  }

  hasLegitimateUnsubscribePattern (href) {
    const legitimatePatterns = [
      /\/unsubscribe/i,
      /\/opt[_-]?out/i,
      /\/remove/i,
      /\/preferences/i,
      /\/settings/i,
      /\/manage[_-]?subscription/i,
      /\/email[_-]?preferences/i,
      /\/notification[_-]?settings/i,
      /\?unsubscribe/i,
      /\?action=unsubscribe/i,
      /\?opt[_-]?out/i
    ]

    return legitimatePatterns.some(pattern => pattern.test(href))
  }

  extractListUnsubscribeLinks (container) {
    const listUnsubscribeLinks = []

    // Look for mailto unsubscribe links
    const mailtoLinks = container.querySelectorAll('a[href^="mailto:"]')
    mailtoLinks.forEach(link => {
      const href = link.getAttribute('href')
      const text = link.textContent.trim()

      if (href.toLowerCase().includes('unsubscribe') ||
                this.unsubscribePatterns.some(pattern => pattern.test(text))) {
        listUnsubscribeLinks.push({
          element: link,
          href,
          text,
          type: 'mailto',
          isDangerous: false,
          isValid: true,
          trustLevel: 80, // mailto links are generally trustworthy
          extractedAt: new Date().toISOString()
        })
      }
    })

    return listUnsubscribeLinks
  }

  async storeExtractionResults (unsubscribeLinks, container = null) {
    try {
    // Get current email sender information
      let senderInfo = {}

      if (container) {
      // Use the container if provided
        senderInfo = this.extractSenderInfo(container)
      } else {
      // Try to find the current email element
        const currentEmail = this.findCurrentEmail()
        if (currentEmail) {
          senderInfo = this.extractSenderInfo(currentEmail)
        } else {
          console.log('No current email found - skipping storage')
          return // Skip storage when no current email is found
        }
      }

      // Check if we have valid sender information from current email
      if (!senderInfo.email || senderInfo.email === null) {
        console.log('No valid sender information found in current email - skipping storage')
        return // Skip storage when no valid current email data
      }

      // Enhanced metadata collection for Task 6A
      const timestamp = new Date().toISOString()
      const senderId = this.generateSenderId(senderInfo)

      // Debug logging for the storeExtractionResults process
      console.log('📦 storeExtractionResults debug:', {
        senderInfo,
        senderId,
        unsubscribeLinksCount: unsubscribeLinks.length,
        unsubscribeLinks: unsubscribeLinks.map(link => ({ href: link.href, text: link.text }))
      })

      // Calculate email frequency and patterns
      const frequency = await this.calculateEmailFrequency(senderInfo)
      const emailCount = await this.getTotalEmailCount(senderId)

      // Store comprehensive subscription data for each link
      for (const link of unsubscribeLinks) {
        const subscriptionData = {
          senderId,
          senderName: senderInfo.name || this.extractNameFromEmail(senderInfo.email),
          senderEmail: senderInfo.email || 'Unknown',
          senderDomain: senderInfo.domain || this.extractDomainFromEmail(senderInfo.email),
          unsubscribeUrl: link.href,
          unsubscribeText: link.text,
          unsubscribeType: link.type || 'http',

          // Email pattern data
          lastEmailDate: senderInfo.date || timestamp,
          frequency,
          emailCount,

          // Security analysis results
          securityStatus: this.determineSecurityStatus(link),
          trustLevel: link.trustLevel,
          securityWarnings: link.securityWarnings || [],
          phishingCheck: link.phishingCheck,

          // Categorization (will be enhanced in later tasks)
          category: 'unknown',

          // Metadata
          extractedAt: timestamp,
          lastUpdated: timestamp,
          discoveredDate: timestamp,
          status: 'pending_dashboard_review',
          needsSecurityAnalysis: link.trustLevel < 80 || (link.phishingCheck && link.phishingCheck.isPhishing),

          // Validation flags
          isDangerous: link.isDangerous,
          isValid: link.isValid,

          // Context information
          extractionContext: {
            url: window.location.href,
            emailSubject: senderInfo.subject || 'Unknown',
            gmail_thread_id: this.extractThreadId(container),
            email_message_id: this.extractMessageId(container)
          }
        }

        // Store individual subscription data
        await this.storeSubscriptionData(subscriptionData)
      }

      // Also maintain backward compatibility with existing extraction storage
      const extractionData = {
        timestamp,
        url: window.location.href,
        sender: senderInfo,
        linksFound: unsubscribeLinks.length,
        links: unsubscribeLinks.map(link => ({
          href: link.href,
          text: link.text,
          type: link.type || 'http',
          isDangerous: link.isDangerous,
          isValid: link.isValid,
          trustLevel: link.trustLevel,
          extractedAt: link.extractedAt
        }))
      }

      // Store in Chrome storage with context validation
      if (this.isExtensionContextValid()) {
        chrome.storage.local.get(['unsubscribeExtractions'], (result) => {
          if (!this.isExtensionContextValid()) {
            console.warn('Extension context invalidated during storage operation')
            return
          }

          const extractions = result.unsubscribeExtractions || []
          extractions.push(extractionData)

          // Keep only last 1000 extractions to prevent storage overflow
          if (extractions.length > 1000) {
            extractions.splice(0, extractions.length - 1000)
          }

          chrome.storage.local.set({ unsubscribeExtractions: extractions })
        })
      }

      console.log(`Enhanced storage: Stored ${unsubscribeLinks.length} subscription entries with comprehensive metadata`)
    } catch (error) {
      console.error('Error storing extraction results:', error)
    }
  }

  // Helper methods for Task 6A: Enhanced Link Collection System
  generateSenderId (senderInfo) {
    // Create a unique but consistent ID for the sender
    const email = senderInfo.email || 'unknown'
    const domain = senderInfo.domain || this.extractDomainFromEmail(email)

    // Debug logging to track sender ID generation
    console.log('🆔 generateSenderId debug:', {
      senderInfo,
      email,
      domain,
      originalEmail: senderInfo.email,
      originalDomain: senderInfo.domain
    })

    // Use domain as primary identifier to consolidate same provider
    // Only use email local part if domain is generic
    const genericDomains = ['gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com', 'aol.com']
    let senderId
    if (genericDomains.includes(domain.toLowerCase())) {
      senderId = `${domain}_${email.split('@')[0] || 'unknown'}`.replace(/[^a-zA-Z0-9_]/g, '_')
    } else {
      // For business domains, use just the domain to consolidate multiple emails from same company
      senderId = domain.replace(/[^a-zA-Z0-9_]/g, '_')
    }

    console.log('🆔 Generated senderId:', senderId)
    return senderId
  }

  extractNameFromEmail (email) {
    if (!email || email === 'Unknown') return 'Unknown'
    const localPart = email.split('@')[0]
    // Convert common email patterns to readable names
    return localPart
      .replace(/[._-]/g, ' ')
      .replace(/no[.\-_]?reply/gi, 'No Reply')
      .replace(/\b\w/g, l => l.toUpperCase())
  }

  extractDomainFromEmail (email) {
    if (!email || email === 'Unknown') return 'unknown'
    const parts = email.split('@')
    return parts.length > 1 ? parts[1].toLowerCase() : 'unknown'
  }

  async calculateEmailFrequency (senderInfo) {
    try {
      const domain = senderInfo.domain || this.extractDomainFromEmail(senderInfo.email)

      // Get existing subscription data for this domain
      const result = await chrome.storage.local.get(['subscriptionDatabase'])
      const subscriptions = result.subscriptionDatabase || []

      const existingSubscription = subscriptions.find(sub =>
        sub.senderDomain === domain || sub.senderEmail === senderInfo.email
      )

      if (existingSubscription) {
        const daysSinceFirst = (Date.now() - new Date(existingSubscription.discoveredDate)) / (1000 * 60 * 60 * 24)
        const emailsPerDay = existingSubscription.emailCount / Math.max(daysSinceFirst, 1)

        if (emailsPerDay >= 1) return 'daily'
        if (emailsPerDay >= 0.2) return 'weekly' // More than once per 5 days
        if (emailsPerDay >= 0.05) return 'monthly' // More than once per 20 days
        return 'rare'
      }

      // Default for new subscriptions
      return 'unknown'
    } catch (error) {
      console.warn('Error calculating email frequency:', error)
      return 'unknown'
    }
  }

  async getTotalEmailCount (senderId) {
    try {
      const result = await chrome.storage.local.get(['subscriptionDatabase'])
      const subscriptions = result.subscriptionDatabase || []

      const existingSubscription = subscriptions.find(sub => sub.senderId === senderId)
      return existingSubscription ? existingSubscription.emailCount : 1
    } catch (error) {
      console.warn('Error getting email count:', error)
      return 1
    }
  }

  determineSecurityStatus (link) {
    if (link.phishingCheck && link.phishingCheck.isPhishing) return 'high_risk'
    if (link.trustLevel < 30) return 'high_risk'
    if (link.trustLevel < 50) return 'medium_risk'
    if (link.trustLevel < 80) return 'low_risk'
    return 'safe'
  }

  extractThreadId (container) {
    if (!container) return null

    // Try multiple selectors for Gmail thread ID
    const threadSelectors = [
      '[data-thread-id]',
      '[data-legacy-thread-id]',
      '.nH[data-thread-id]'
    ]

    for (const selector of threadSelectors) {
      const element = container.querySelector(selector) || container.closest(selector)
      if (element) {
        return element.getAttribute('data-thread-id') ||
               element.getAttribute('data-legacy-thread-id')
      }
    }

    return null
  }

  extractMessageId (container) {
    if (!container) return null

    // Try to find Gmail message ID
    const messageElement = container.querySelector('[data-message-id]') ||
                          container.closest('[data-message-id]')

    return messageElement ? messageElement.getAttribute('data-message-id') : null
  }

  async storeSubscriptionData (subscriptionData) {
    try {
      // Get existing subscription database
      const result = await chrome.storage.local.get(['subscriptionDatabase', 'subscriptionMetadata'])
      const subscriptions = result.subscriptionDatabase || []
      const metadata = result.subscriptionMetadata || {
        lastScan: null,
        totalSubscriptions: 0,
        lastUpdate: null
      }

      // Check if subscription already exists for this sender
      const existingIndex = subscriptions.findIndex(sub =>
        sub.senderId === subscriptionData.senderId
      )

      if (existingIndex >= 0) {
        // Update existing subscription
        const existing = subscriptions[existingIndex]

        // Merge unsubscribe links
        const unsubscribeLinks = existing.unsubscribeLinks || []
        const newLink = {
          href: subscriptionData.unsubscribeUrl,
          text: subscriptionData.unsubscribeText || 'Unsubscribe',
          type: subscriptionData.unsubscribeType || 'http',
          trustLevel: subscriptionData.trustLevel || 0,
          securityStatus: subscriptionData.securityStatus || 'unknown'
        }

        // Only add link if it doesn't already exist
        if (!unsubscribeLinks.some(link => link.href === newLink.href)) {
          unsubscribeLinks.push(newLink)
        }

        // Track unique emails by message ID or date to avoid double counting
        const currentEmailId = subscriptionData.extractionContext?.email_message_id || subscriptionData.lastEmailDate
        const lastProcessedEmailId = existing.lastProcessedEmailId
        const shouldIncrementCount = currentEmailId !== lastProcessedEmailId

        subscriptions[existingIndex] = {
          ...existing,
          senderName: subscriptionData.senderName || existing.senderName,
          senderEmail: subscriptionData.senderEmail || existing.senderEmail,
          senderDomain: subscriptionData.senderDomain || existing.senderDomain,
          emailCount: shouldIncrementCount ? (existing.emailCount || 0) + 1 : existing.emailCount,
          lastEmailDate: subscriptionData.lastEmailDate,
          lastProcessedEmailId: currentEmailId,
          lastUpdated: subscriptionData.lastUpdated,
          frequency: subscriptionData.frequency || existing.frequency,
          unsubscribeLinks,
          // Update security status consistently - use the highest trust level found
          securityStatus: this.getMostOptimisticSecurityStatus(existing.securityStatus, subscriptionData.securityStatus),
          trustLevel: Math.max(existing.trustLevel || 0, subscriptionData.trustLevel || 0),
          // Preserve original discovery date
          discoveredDate: existing.discoveredDate
        }
        console.log(`Updated existing subscription for ${subscriptionData.senderEmail}${shouldIncrementCount ? ' (email count incremented)' : ' (same email, count unchanged)'}`)
      } else {
        // Add new subscription with proper structure
        const newSubscription = {
          senderId: subscriptionData.senderId,
          senderName: subscriptionData.senderName || 'Unknown',
          senderEmail: subscriptionData.senderEmail || 'Unknown',
          senderDomain: subscriptionData.senderDomain || 'unknown.com',
          frequency: subscriptionData.frequency || 'unknown',
          lastEmailDate: subscriptionData.lastEmailDate,
          emailCount: 1,
          lastProcessedEmailId: subscriptionData.extractionContext?.email_message_id || subscriptionData.lastEmailDate,
          category: subscriptionData.category || 'unknown',
          trustLevel: subscriptionData.trustLevel || 0,
          securityStatus: subscriptionData.securityStatus || 'unknown',
          unsubscribeLinks: [{
            href: subscriptionData.unsubscribeUrl,
            text: subscriptionData.unsubscribeText || 'Unsubscribe',
            type: subscriptionData.unsubscribeType || 'http',
            trustLevel: subscriptionData.trustLevel || 0,
            securityStatus: subscriptionData.securityStatus || 'unknown'
          }],
          securityWarnings: subscriptionData.securityWarnings || [],
          phishingCheck: subscriptionData.phishingCheck,
          isDangerous: subscriptionData.isDangerous || false,
          discoveredDate: subscriptionData.discoveredDate,
          lastUpdated: subscriptionData.lastUpdated,
          extractionContext: subscriptionData.extractionContext
        }

        subscriptions.push(newSubscription)
        console.log(`Added new subscription for ${subscriptionData.senderEmail}`)
      }

      // Update metadata
      metadata.lastScan = new Date().toISOString()
      metadata.totalSubscriptions = subscriptions.length
      metadata.lastUpdate = new Date().toISOString()

      // Store updated data
      await chrome.storage.local.set({
        subscriptionDatabase: subscriptions,
        subscriptionMetadata: metadata
      })

      console.log(`Subscription database updated: ${subscriptions.length} total subscriptions`)
    } catch (error) {
      console.error('Error storing subscription data:', error)
    }
  }

  getMostRestrictiveSecurityStatus (status1, status2) {
    const hierarchy = {
      dangerous: 4,
      caution: 3,
      unknown: 2,
      safe: 1
    }

    const level1 = hierarchy[status1] || 2
    const level2 = hierarchy[status2] || 2

    const maxLevel = Math.max(level1, level2)
    return Object.keys(hierarchy).find(key => hierarchy[key] === maxLevel) || 'unknown'
  }

  getMostOptimisticSecurityStatus (status1, status2) {
    const hierarchy = {
      safe: 4,
      unknown: 3,
      caution: 2,
      dangerous: 1
    }

    const level1 = hierarchy[status1] || 3
    const level2 = hierarchy[status2] || 3

    const maxLevel = Math.max(level1, level2)
    return Object.keys(hierarchy).find(key => hierarchy[key] === maxLevel) || 'unknown'
  }

  findCurrentEmail () {
    // Try to find the currently viewed email element
    const emailSelectors = [
      '.ii.gt .a3s.aiL',
      '[data-message-id] .ii.gt',
      '.a3s.aiL',
      '[role="listitem"] .ii.gt',
      '.adn.ads .ii.gt',
      '.message',
      '.nH.if',
      '.Bs.nH',
      '.gs .ii.gt',
      '.adP.adO'
    ]

    for (const selector of emailSelectors) {
      const element = document.querySelector(selector)
      if (element && element.textContent.trim().length > 0) {
        return element
      }
    }

    return null
  }

  extractSenderInfoSimple () {
    // Try multiple selectors for sender email
    const senderSelectors = [
      '[email]',
      '.go .gD[email]',
      '.gE.iv.gt .gD[email]',
      '.yW span[email]',
      '.gb[email]',
      '.oL.aDm .oM .aDn .oZ-x3d .oY .aoo span[email]'
    ]

    let senderElement = null
    let senderEmail = 'Unknown'
    let senderName = 'Unknown'

    for (const selector of senderSelectors) {
      senderElement = document.querySelector(selector)
      if (senderElement) {
        const extractedEmail = senderElement.getAttribute('email') || senderElement.textContent.trim()
        const extractedName = senderElement.getAttribute('name') || senderElement.getAttribute('title') || senderElement.textContent.trim()

        // Skip if the email is the user's own email or shows as 'me'
        if (extractedEmail && extractedEmail !== 'me' && !extractedEmail.toLowerCase().includes('me@') && extractedEmail.includes('@')) {
          senderEmail = extractedEmail
          senderName = extractedName
          break
        }
      }
    }

    // If no email found, try text-based extraction from visible elements
    if (senderEmail === 'Unknown') {
      const nameSelectors = [
        '.go .gD',
        '.gE.iv.gt .gD',
        '.yW span',
        '.gb',
        '.qu .qu .go .gD'
      ]

      for (const selector of nameSelectors) {
        const element = document.querySelector(selector)
        if (element && element.textContent.trim()) {
          const text = element.textContent.trim()
          // Check if it looks like an email
          if (text.includes('@') && text.includes('.')) {
            senderEmail = text
            senderName = text.split('@')[0].replace(/[._-]/g, ' ')
            break
          } else if (text.length > 0 && !text.includes('Gmail') && !text.includes('Inbox')) {
            senderName = text
            // Try to find email elsewhere
            const emailPattern = /[\w\.-]+@[\w\.-]+\.\w+/
            const pageText = document.body.textContent
            const emailMatch = pageText.match(emailPattern)
            if (emailMatch) {
              senderEmail = emailMatch[0]
            }
            break
          }
        }
      }
    }

    // Extract subject
    const subjectSelectors = [
      '.hP',
      '.bog',
      '.hU',
      '.thread-subject',
      '.aoo .aoo .aoo .aoo .a1f .aOB'
    ]

    let subject = 'Unknown'
    for (const selector of subjectSelectors) {
      const subjectElement = document.querySelector(selector)
      if (subjectElement && subjectElement.textContent.trim()) {
        subject = subjectElement.textContent.trim()
        break
      }
    }

    console.log('Extracted sender info:', { email: senderEmail, name: senderName, subject })

    return {
      email: senderEmail,
      name: senderName,
      subject
    }
  }

  addUnsubscribeButton (container, unsubscribeLinks) {
  // Check if button already exists anywhere in the document
    if (document.querySelector('.unsubscriber-button')) {
      return
    }

    // Extract sender information for dashboard integration
    const senderInfo = this.extractSenderInfo(container)
    const senderId = this.generateSenderId(senderInfo)

    const button = document.createElement('div')
    button.className = 'unsubscriber-button'
    button.innerHTML = `
          <button class="unsubscriber-btn">
              <span class="unsubscriber-icon">📊</span>
              <span class="unsubscriber-text">Manage Subscriptions (${unsubscribeLinks.length})</span>
          </button>
      `

    // Add styles
    const style = document.createElement('style')
    style.textContent = `
          .unsubscriber-button {
              margin: 16px 0;
              padding: 12px 16px;
              border-top: 1px solid #e8eaed;
              border-bottom: 1px solid #e8eaed;
              background: #f8f9fa;
              border-radius: 8px;
              display: flex;
              justify-content: center;
              align-items: center;
          }
          
          .unsubscriber-btn {
              display: flex;
              align-items: center;
              gap: 8px;
              background: #1a73e8;
              border: none;
              border-radius: 20px;
              padding: 8px 16px;
              font-size: 14px;
              font-weight: 500;
              color: #ffffff;
              cursor: pointer;
              transition: all 0.2s ease;
              box-shadow: 0 1px 2px 0 rgba(60,64,67,.3), 0 1px 3px 1px rgba(60,64,67,.15);
          }
          
          .unsubscriber-btn:hover {
              background: #1557b0;
              box-shadow: 0 1px 3px 0 rgba(60,64,67,.3), 0 4px 8px 3px rgba(60,64,67,.15);
              transform: translateY(-1px);
          }
          
          .unsubscriber-btn:active {
              transform: translateY(0);
              box-shadow: 0 1px 2px 0 rgba(60,64,67,.3), 0 1px 3px 1px rgba(60,64,67,.15);
          }
          
          .unsubscriber-icon {
              font-size: 16px;
          }
          
          .unsubscriber-text {
              font-family: 'Google Sans', Roboto, sans-serif;
          }
      `

    if (!document.querySelector('#unsubscriber-styles')) {
      style.id = 'unsubscriber-styles'
      document.head.appendChild(style)
    }

    // Modified click event - opens dashboard instead of modal
    button.addEventListener('click', (e) => {
      e.preventDefault()
      this.openDashboard({
        focusOnSender: senderId,
        highlightSender: senderId,
        showSecurityAnalysis: true,
        fromEmail: true
      })
    })

    // Find the best insertion point in Gmail UI
    const emailActions = container.querySelector('.amn, .ams, .aoo') || // Gmail action buttons area
                      container.querySelector('.ii.gt') || // Email body
                      container

    // Insert button after the email content but before any existing actions
    if (emailActions && emailActions !== container) {
      emailActions.insertAdjacentElement('afterend', button)
    } else {
      container.appendChild(button)
    }
  }

  // Task 3 Integration: Open Dashboard instead of immediate unsubscribe
  openDashboard (options = {}) {
    try {
      const {
        focusOnSender = null,
        highlightSender = null,
        showSecurityAnalysis = false,
        fromEmail = false
      } = options

      // Create a full-screen dashboard overlay within Gmail
      this.createDashboardOverlay({
        focusOnSender,
        highlightSender,
        showSecurityAnalysis,
        fromEmail
      })

      console.log('Opened dashboard with options:', options)
    } catch (error) {
      console.error('Error opening dashboard:', error)
      // Fallback to popup approach
      this.openDashboardPopup(options)
    }
  }

  createDashboardOverlay (options) {
    // Remove any existing dashboard overlay
    const existingOverlay = document.querySelector('#subscription-dashboard-overlay')
    if (existingOverlay) {
      existingOverlay.remove()
    }

    // Create overlay container
    const overlay = document.createElement('div')
    overlay.id = 'subscription-dashboard-overlay'
    overlay.className = 'subscription-dashboard-overlay'

    // Load dashboard content
    overlay.innerHTML = `
      <div class="dashboard-overlay-content">
        <div class="dashboard-overlay-header">
          <h2>📊 Subscription Dashboard</h2>
          <div class="dashboard-overlay-controls">
            ${options.fromEmail ? '<span class="context-info">Managing subscriptions from this email</span>' : ''}
            <button class="close-dashboard-btn" id="closeDashboardBtn">✕</button>
          </div>
        </div>
        
        <div class="dashboard-overlay-body" id="dashboardOverlayBody">
          <div class="loading-dashboard">
            <div class="loading-spinner"></div>
            <p>Loading your subscriptions...</p>
          </div>
        </div>
      </div>
    `

    // Add overlay styles
    const overlayStyle = document.createElement('style')
    overlayStyle.id = 'dashboard-overlay-styles'
    overlayStyle.textContent = `
      .subscription-dashboard-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.8);
        z-index: 10001;
        display: flex;
        justify-content: center;
        align-items: center;
        font-family: 'Google Sans', Roboto, sans-serif;
      }
      
      .dashboard-overlay-content {
        background: white;
        border-radius: 12px;
        width: 90%;
        max-width: 1200px;
        height: 85%;
        max-height: 800px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        display: flex;
        flex-direction: column;
        overflow: hidden;
      }
      
      .dashboard-overlay-header {
        background: #1a73e8;
        color: white;
        padding: 16px 24px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-radius: 12px 12px 0 0;
      }
      
      .dashboard-overlay-header h2 {
        margin: 0;
        font-size: 20px;
        font-weight: 500;
      }
      
      .dashboard-overlay-controls {
        display: flex;
        align-items: center;
        gap: 16px;
      }
      
      .context-info {
        font-size: 14px;
        opacity: 0.9;
        background: rgba(255, 255, 255, 0.1);
        padding: 4px 12px;
        border-radius: 16px;
      }
      
      .close-dashboard-btn {
        background: rgba(255, 255, 255, 0.1);
        border: none;
        color: white;
        width: 32px;
        height: 32px;
        border-radius: 50%;
        cursor: pointer;
        font-size: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: background 0.2s ease;
      }
      
      .close-dashboard-btn:hover {
        background: rgba(255, 255, 255, 0.2);
      }
      
      .dashboard-overlay-body {
        flex: 1;
        padding: 24px;
        overflow-y: auto;
        background: #f8f9fa;
      }
      
      .loading-dashboard {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100%;
        color: #5f6368;
      }
      
      .loading-spinner {
        width: 32px;
        height: 32px;
        border: 3px solid #e8eaed;
        border-top: 3px solid #1a73e8;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-bottom: 16px;
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
      
      .subscription-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 16px;
        margin-top: 20px;
      }
      
      .subscription-card {
        background: white;
        border-radius: 8px;
        padding: 16px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        border: 1px solid #e8eaed;
        transition: all 0.2s ease;
      }
      
      .subscription-card:hover {
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
        transform: translateY(-1px);
      }
      
      .subscription-card.highlighted {
        border: 2px solid #1a73e8;
        box-shadow: 0 2px 12px rgba(26, 115, 232, 0.2);
      }
      
      .subscription-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 12px;
      }
      
      .subscription-info h3 {
        margin: 0 0 4px 0;
        font-size: 16px;
        font-weight: 500;
        color: #202124;
      }
      
      .subscription-info p {
        margin: 0;
        font-size: 14px;
        color: #5f6368;
      }
      
      .security-indicator {
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 12px;
        font-weight: 500;
        text-transform: uppercase;
      }
      
      .security-safe { background: #e8f5e8; color: #137333; }
      .security-low-risk { background: #fef7e0; color: #ea8600; }
      .security-medium-risk { background: #fce8e6; color: #ea4335; }
      .security-high-risk { background: #fce8e6; color: #d33b2c; border: 1px solid #ea4335; }
      
      .subscription-actions {
        margin-top: 12px;
        display: flex;
        gap: 8px;
      }
      
      .action-btn {
        padding: 6px 12px;
        border: 1px solid #dadce0;
        background: #f8f9fa;
        border-radius: 4px;
        font-size: 13px;
        cursor: pointer;
        transition: all 0.2s ease;
      }
      
      .action-btn:hover {
        background: #e8eaed;
      }
      
      .action-btn.primary {
        background: #1a73e8;
        color: white;
        border-color: #1a73e8;
      }
      
      .action-btn.primary:hover {
        background: #1557b0;
      }
      
      .empty-state {
        text-align: center;
        padding: 40px;
        color: #5f6368;
      }
      
      .empty-state h3 {
        color: #202124;
        margin-bottom: 8px;
      }
    `

    // Add to page
    document.head.appendChild(overlayStyle)
    document.body.appendChild(overlay)

    // Set up event listeners
    overlay.querySelector('#closeDashboardBtn').addEventListener('click', () => {
      this.closeDashboard()
    })

    // Close on overlay click (but not content click)
    overlay.addEventListener('click', (e) => {
      if (e.target === overlay) {
        this.closeDashboard()
      }
    })

    // Load dashboard data
    this.loadDashboardData(options)
  }

  async loadDashboardData (options) {
    try {
      // Get subscription database
      const { subscriptions, metadata } = await this.getSubscriptionDatabase()

      // Render dashboard content
      this.renderDashboardContent(subscriptions, metadata, options)
    } catch (error) {
      console.error('Error loading dashboard data:', error)
      this.renderDashboardError()
    }
  }

  renderDashboardContent (subscriptions, metadata, options) {
    const dashboardBody = document.querySelector('#dashboardOverlayBody')
    if (!dashboardBody) return

    if (subscriptions.length === 0) {
      dashboardBody.innerHTML = `
        <div class="empty-state">
          <h3>No Subscriptions Found</h3>
          <p>Run a bulk scan to discover your email subscriptions</p>
          <button class="action-btn primary" onclick="window.location.reload()">
            Scan Current Emails
          </button>
        </div>
      `
      return
    }

    // Sort subscriptions - prioritize focused sender
    let sortedSubscriptions = [...subscriptions]
    if (options.focusOnSender) {
      sortedSubscriptions = sortedSubscriptions.sort((a, b) => {
        if (a.senderId === options.focusOnSender) return -1
        if (b.senderId === options.focusOnSender) return 1
        return 0
      })
    }

    // Create subscription cards
    const subscriptionCards = sortedSubscriptions.map(subscription => {
      const isHighlighted = options.highlightSender === subscription.senderId

      return `
        <div class="subscription-card ${isHighlighted ? 'highlighted' : ''}" data-sender-id="${subscription.senderId}">
          <div class="subscription-header">
            <div class="subscription-info">
              <h3>${subscription.senderName}</h3>
              <p>${subscription.senderEmail}</p>
              <p style="font-size: 12px; margin-top: 4px;">
                ${subscription.frequency} • ${subscription.emailCount} emails
              </p>
            </div>
            <div class="security-indicator security-${subscription.securityStatus}">
              ${subscription.securityStatus.replace('_', ' ')}
            </div>
          </div>
          
          ${options.showSecurityAnalysis && subscription.needsSecurityAnalysis
? `
            <div style="background: #fef7e0; padding: 8px; border-radius: 4px; margin-bottom: 12px; font-size: 13px;">
              ⚠️ Security analysis recommended before unsubscribing
            </div>
          `
: ''}
          
          <div class="subscription-actions">
            <button class="action-btn primary" onclick="window.unsubscriberInstance.handleUnsubscribe('${subscription.senderId}')">
              Unsubscribe
            </button>
            <button class="action-btn" onclick="window.unsubscriberInstance.showSubscriptionDetails('${subscription.senderId}')">
              Details
            </button>
            ${subscription.securityStatus === 'high_risk'
? `
              <button class="action-btn" onclick="window.unsubscriberInstance.reportSuspicious('${subscription.senderId}')">
                Report
              </button>
            `
: ''}
          </div>
        </div>
      `
    }).join('')

    dashboardBody.innerHTML = `
      <div class="dashboard-stats" style="display: flex; gap: 16px; margin-bottom: 24px;">
        <div style="background: white; padding: 16px; border-radius: 8px; text-align: center; flex: 1;">
          <div style="font-size: 24px; font-weight: 600; color: #1a73e8;">${subscriptions.length}</div>
          <div style="font-size: 14px; color: #5f6368;">Total Subscriptions</div>
        </div>
        <div style="background: white; padding: 16px; border-radius: 8px; text-align: center; flex: 1;">
          <div style="font-size: 24px; font-weight: 600; color: #34a853;">${subscriptions.filter(s => s.securityStatus === 'safe').length}</div>
          <div style="font-size: 14px; color: #5f6368;">Safe</div>
        </div>
        <div style="background: white; padding: 16px; border-radius: 8px; text-align: center; flex: 1;">
          <div style="font-size: 24px; font-weight: 600; color: #ea4335;">${subscriptions.filter(s => s.securityStatus === 'high_risk').length}</div>
          <div style="font-size: 14px; color: #5f6368;">High Risk</div>
        </div>
      </div>
      
      <div class="subscription-grid">
        ${subscriptionCards}
      </div>
    `

    // Store instance reference for button handlers
    window.unsubscriberInstance = this
  }

  renderDashboardError () {
    const dashboardBody = document.querySelector('#dashboardOverlayBody')
    if (!dashboardBody) return

    dashboardBody.innerHTML = `
      <div class="empty-state">
        <h3>Error Loading Subscriptions</h3>
        <p>Please try again or run a new scan</p>
        <button class="action-btn primary" onclick="window.location.reload()">
          Refresh
        </button>
      </div>
    `
  }

  closeDashboard () {
    const overlay = document.querySelector('#subscription-dashboard-overlay')
    const style = document.querySelector('#dashboard-overlay-styles')

    if (overlay) overlay.remove()
    if (style) style.remove()

    // Clean up global reference
    if (window.unsubscriberInstance) {
      delete window.unsubscriberInstance
    }
  }

  // Dashboard action handlers
  async handleUnsubscribe (senderId) {
    try {
      console.log('Handling unsubscribe for:', senderId)
      const subscription = await this.getSubscriptionBySenderId(senderId)

      if (!subscription) {
        alert('Subscription not found')
        return
      }

      // For now, show confirmation and open the unsubscribe link
      const confirmed = confirm(`Unsubscribe from ${subscription.senderName}?\n\nThis will open their unsubscribe page in a new tab.`)

      if (confirmed) {
        // Update status
        await this.updateSubscriptionStatus(senderId, 'unsubscribed')

        // Open unsubscribe link
        window.open(subscription.unsubscribeUrl, '_blank', 'noopener,noreferrer')

        // Refresh dashboard
        this.loadDashboardData({ focusOnSender: senderId })

        console.log('Unsubscribe initiated for:', subscription.senderName)
      }
    } catch (error) {
      console.error('Error handling unsubscribe:', error)
      alert('Error processing unsubscribe request')
    }
  }

  showSubscriptionDetails (senderId) {
    // This would show detailed information about the subscription
    console.log('Showing details for:', senderId)
    alert('Subscription details coming in future update!')
  }

  reportSuspicious (senderId) {
    // This would report a suspicious subscription
    console.log('Reporting suspicious:', senderId)
    alert('Reporting feature coming in future update!')
  }

  openDashboardPopup (options) {
    // Fallback method - opens dashboard in a new popup window
    try {
      const dashboardUrl = chrome.runtime.getURL('dashboard.html')
      const params = new URLSearchParams(options).toString()
      const finalUrl = `${dashboardUrl}?${params}`

      chrome.windows.create({
        url: finalUrl,
        type: 'popup',
        width: 1000,
        height: 700,
        focused: true
      })
    } catch (error) {
      console.error('Error opening dashboard popup:', error)
    }
  }

  showUnsubscribeModal (unsubscribeLinks) {
    // Create modal overlay
    const modal = document.createElement('div')
    modal.className = 'unsubscriber-modal'
    modal.innerHTML = `
            <div class="unsubscriber-modal-content">
                <div class="unsubscriber-modal-header">
                    <h3>Unsubscribe Options</h3>
                    <button class="unsubscriber-close">&times;</button>
                </div>
                <div class="unsubscriber-modal-body">
                    <p>Found ${unsubscribeLinks.length} unsubscribe link(s) in this email:</p>
                    <div class="unsubscriber-links">
${unsubscribeLinks.map((link, index) => `
                            <div class="unsubscriber-link-item">
                                <button class="unsubscriber-link-btn ${link.trustLevel < 50 ? 'low-trust' : link.trustLevel >= 80 ? 'high-trust' : 'medium-trust'}" 
                                        data-href="${link.href}" data-index="${index}">
                                    <div class="link-info">
                                        <span class="link-text">${link.text || 'Unsubscribe Link'}</span>
                                        <span class="link-type">${link.type || 'HTTP'}</span>
                                    </div>
                                    <div class="link-meta">
                                        <span class="trust-level">Trust: ${link.trustLevel}%</span>
                                        ${link.securityWarnings && link.securityWarnings.length > 0
                                            ? `<span class="security-warnings">
                                                ${link.securityWarnings.map(warning =>
                                                    `<span class="warning-${warning.type}">${warning.icon} ${warning.message}</span>`
                                                ).join('')}
                                            </span>`
: ''}
                                    </div>
                                    ${link.phishingCheck && link.phishingCheck.isPhishing
                                        ? `<div class="phishing-details">
                                            <small>Detected patterns: ${link.phishingCheck.detectedPatterns.map(p => p.category).join(', ')}</small>
                                        </div>`
: ''}
                                </button>
                            </div>
                        `).join('')}
                    </div>
                    <div class="unsubscriber-warning">
                        <p><strong>Privacy Notice:</strong> Clicking will open the unsubscribe link in a new tab. Links marked with ⚠️ may be suspicious.</p>
                    </div>
                </div>
            </div>
        `

    // Add modal styles
    const modalStyle = document.createElement('style')
    modalStyle.textContent = `
            .unsubscriber-modal {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 10000;
            }
            
            .unsubscriber-modal-content {
                background: white;
                border-radius: 8px;
                padding: 0;
                max-width: 500px;
                width: 90%;
                max-height: 80vh;
                overflow-y: auto;
                box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
            }
            
            .unsubscriber-modal-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 16px 20px;
                border-bottom: 1px solid #e8eaed;
            }
            
            .unsubscriber-modal-header h3 {
                margin: 0;
                color: #333;
            }
            
            .unsubscriber-close {
                background: none;
                border: none;
                font-size: 24px;
                cursor: pointer;
                color: #5f6368;
                padding: 0;
                width: 30px;
                height: 30px;
                display: flex;
                align-items: center;
                justify-content: center;
            }
            
            .unsubscriber-modal-body {
                padding: 20px;
            }
            
            .unsubscriber-links {
                margin: 16px 0;
            }
            
            .unsubscriber-link-item {
                margin-bottom: 8px;
            }
            
            .unsubscriber-link-btn {
                width: 100%;
                padding: 12px;
                background: #f8f9fa;
                border: 1px solid #dadce0;
                border-radius: 4px;
                cursor: pointer;
                text-align: left;
                font-size: 14px;
                transition: all 0.2s ease;
                display: flex;
                flex-direction: column;
                gap: 4px;
            }
            
            .unsubscriber-link-btn:hover {
                background: #e8eaed;
            }
            
            .unsubscriber-link-btn.low-trust {
                border-color: #ea4335;
                background: #fce8e6;
            }
            
            .unsubscriber-link-btn.medium-trust {
                border-color: #fbbc04;
                background: #fef7e0;
            }
            
            .unsubscriber-link-btn.high-trust {
                border-color: #34a853;
                background: #e8f5e8;
            }
            
            .link-info {
                display: flex;
                justify-content: space-between;
                align-items: center;
            }
            
            .link-text {
                font-weight: 500;
                color: #333;
            }
            
            .link-type {
                font-size: 12px;
                color: #5f6368;
                background: #e8eaed;
                padding: 2px 6px;
                border-radius: 3px;
            }
            
            .link-meta {
                display: flex;
                justify-content: space-between;
                align-items: center;
                font-size: 12px;
            }
            
            .trust-level {
                color: #5f6368;
                font-weight: 500;
            }
            
            .warning {
                color: #ea4335;
                font-weight: 500;
            }
            
            .invalid {
                color: #ea4335;
                font-weight: 500;
            }
            
            .unsubscriber-warning {
                margin-top: 16px;
                padding: 12px;
                background: #e8f0fe;
                border-radius: 4px;
                font-size: 12px;
                color: #5f6368;
            }
            
            .security-warnings {
                display: flex;
                flex-direction: column;
                gap: 4px;
                margin-top: 4px;
            }
            
            .warning-danger {
                color: #ea4335;
                font-weight: 600;
                font-size: 11px;
            }
            
            .warning-warning {
                color: #fbbc04;
                font-weight: 500;
                font-size: 11px;
            }
            
            .phishing-details {
                margin-top: 6px;
                padding: 4px 8px;
                background: #fce8e6;
                border-radius: 3px;
                border-left: 3px solid #ea4335;
            }
            
            .phishing-details small {
                color: #ea4335;
                font-weight: 500;
            }
        `

    document.head.appendChild(modalStyle)
    document.body.appendChild(modal)

    // Event listeners
    modal.querySelector('.unsubscriber-close').addEventListener('click', () => {
      document.body.removeChild(modal)
      document.head.removeChild(modalStyle)
    })

    modal.addEventListener('click', (e) => {
      if (e.target === modal) {
        document.body.removeChild(modal)
        document.head.removeChild(modalStyle)
      }
    })

    // Link click handlers
    modal.querySelectorAll('.unsubscriber-link-btn').forEach(btn => {
      btn.addEventListener('click', async () => {
        const href = btn.getAttribute('data-href')
        const index = parseInt(btn.getAttribute('data-index'))
        const link = unsubscribeLinks[index]

        // Show security confirmation for risky links
        if (link.trustLevel < 50 || (link.phishingCheck && link.phishingCheck.isPhishing)) {
          const confirmed = await this.showSecurityConfirmation(link)
          if (!confirmed) {
            return // User canceled
          }
        }

        // Open the link
        window.open(href, '_blank', 'noopener,noreferrer')

        // Record the unsubscribe action
        this.recordUnsubscribeAction(link)

        // Close modal
        document.body.removeChild(modal)
        document.head.removeChild(modalStyle)
      })
    })
  }

  async scanCurrentEmail () {
    try {
      // Check if we're on Gmail first
      if (!this.isGmailPage()) {
        return {
          success: false,
          error: 'Error scanning email. Make sure you\'re on Gmail.'
        }
      }

      // Find the currently viewed email with multiple selector strategies
      const currentEmail = document.querySelector('.ii.gt .a3s.aiL') ||
                                 document.querySelector('[data-message-id] .ii.gt') ||
                                 document.querySelector('.a3s.aiL') ||
                                 document.querySelector('[role="listitem"] .ii.gt') ||
                                 document.querySelector('.adn.ads .ii.gt') ||
                                 document.querySelector('.message') ||
                                 document.querySelector('.nH.if') ||
                                 document.querySelector('.Bs.nH') ||
                                 document.querySelector('.gs .ii.gt') ||
                                 document.querySelector('.adP.adO')

      if (!currentEmail) {
        // Try to find any email content area with safer selectors
        let emailBody = null

        // Try safe selectors one by one - removed [data-thread-id] as it matches metadata, not content
        const safeSelectors = [
          '.nH .if',
          '.Bs .nH',
          '.ii.gt',
          '.adn.ads',
          '.ConversationView',
          '.h7',
          '.nH[role="main"]',
          '.zA.yW', // Email list item in conversation view
          '.Cp' // Gmail conversation container
        ]

        for (const selector of safeSelectors) {
          try {
            emailBody = document.querySelector(selector)
            // Add better validation to ensure this is actual email content, not just metadata
            if (emailBody &&
                emailBody.textContent.trim().length > 50 && // Must have substantial content
                !emailBody.matches('span[data-thread-id]') && // Not a thread metadata span
                (emailBody.querySelector('.a3s') || // Contains email content
                 emailBody.querySelector('.ii') || // Contains email content
                 emailBody.querySelector('.ConversationView') || // Contains conversation
                 emailBody.querySelector('[role="listitem"]'))) { // Contains email items
              console.log(`Found email content using selector: ${selector}`)
              break
            } else {
              emailBody = null // Reset if validation fails
            }
          } catch (selectorError) {
            console.warn(`Invalid selector "${selector}":`, selectorError)
            continue
          }
        }

        if (!emailBody) {
          return {
            success: false,
            error: 'No email content found. Please open an email first.'
          }
        }

        // Use the email body as fallback
        console.log('Using fallback email body:', emailBody)
        const unsubscribeLinks = await this.findUnsubscribeLinks(emailBody)

        return {
          success: true,
          unsubscribeFound: unsubscribeLinks.length > 0,
          unsubscribeCount: unsubscribeLinks.length,
          emailProcessed: true
        }
      }

      console.log('Found email container:', currentEmail)
      const unsubscribeLinks = await this.findUnsubscribeLinks(currentEmail)

      if (unsubscribeLinks.length > 0) {
        // Process the email if not already processed
        if (!currentEmail.hasAttribute('data-unsubscriber-processed')) {
          await this.processEmailContainer(currentEmail)
        }
      }

      return {
        success: true,
        unsubscribeFound: unsubscribeLinks.length > 0,
        unsubscribeCount: unsubscribeLinks.length,
        emailProcessed: true
      }
    } catch (error) {
      console.error('Error scanning current email:', error)
      return {
        success: false,
        error: error.message
      }
    }
  }

  async scanAllEmails (options = {}) {
    try {
      // Check if we're on Gmail first
      if (!this.isGmailPage()) {
        return {
          success: false,
          error: 'Error during bulk scan. Make sure you\'re on Gmail.'
        }
      }

      const startTime = Date.now()
      const {
        maxEmails = 100
      } = options

      // Initialize bulk scan progress tracking
      this.bulkScanProgress.processed = 0
      this.bulkScanProgress.withUnsubscribe = 0
      this.bulkScanProgress.currentEmail = ''
      this.bulkScanProgress.isRunning = true

      let totalScanned = 0
      let totalWithUnsubscribe = 0
      const subscriptionDatabase = new Map()
      const errors = []

      console.log('Bulk scan initialization completed, starting email processing...')

      // For bulk scanning, prioritize list items to scan ALL emails in the inbox
      const emailSelectors = [
        // List items FIRST for bulk scanning - this will find all emails in inbox
        'tr.zA', // Inbox list items (metadata only) - PRIORITY for bulk scan
        'tr.yW', // Alternative list items (selected/read)
        'tr[data-thread-id]', // Thread rows with data attributes
        'tr[data-legacy-thread-id]', // Legacy thread rows
        '.zA', // Generic list items (div format)
        '.yW', // Selected/read list items (div format)
        // Gmail conversation list items (various layouts)
        'div[data-thread-id]',
        'div[data-legacy-thread-id]',
        '.nH [role="listitem"]',
        '[role="listitem"]',
        '.Cp [data-thread-perm-id]',
        // Email content containers (actual email body) - lower priority for bulk scan
        '.ii.gt', // Individual email content (only expanded emails)
        '.a3s.aiL', // Email content with links
        '.adn.ads', // Conversation view messages
        '.hiP', // Another conversation format
        '[data-message-id]' // Message containers
      ]

      let emailElements = []

      // Try different selectors to find emails - prioritize list items for comprehensive scanning
      for (const selector of emailSelectors) {
        emailElements = Array.from(document.querySelectorAll(selector))
        if (emailElements.length > 0) {
          console.log(`Found ${emailElements.length} emails using selector: ${selector}`)

          // For bulk scan, prefer list items over expanded content to get ALL emails
          if (selector.includes('tr.zA') || selector.includes('tr.yW') || selector.includes('.zA') ||
              selector.includes('.yW') || selector.includes('tr[data-thread-id]') ||
              selector.includes('div[data-thread-id]') || selector.includes('[role="listitem"]')) {
            console.log(`Using list items for bulk scan - this will scan all ${emailElements.length} emails in the current view`)
            console.log('Will automatically expand each email during processing to access unsubscribe links')
            break // Use list items for comprehensive scanning
          } else if (selector.includes('.ii.gt') || selector.includes('.a3s.aiL')) {
            console.log(`Found ${emailElements.length} expanded emails - this may be a conversation view`)
            break // Use expanded content if no list items available
          } else {
            console.log(`Found ${emailElements.length} emails using ${selector}`)
            break
          }
        }
      }

      if (emailElements.length === 0) {
        return {
          success: false,
          error: 'No emails found in current view. Please make sure you are in Gmail inbox or email list.'
        }
      }

      // Limit the number of emails to scan
      const emailsToScan = emailElements.slice(0, maxEmails)

      // Set the total count for progress tracking
      this.bulkScanProgress.total = emailsToScan.length

      console.log(`Starting bulk scan of ${emailsToScan.length} emails...`)

      // Special handling for Gmail inbox view - automatically expand emails
      if (emailsToScan.length > 0 && (emailsToScan[0].classList.contains('zA') || emailsToScan[0].tagName === 'TR')) {
        console.log('Detected Gmail inbox list view. Will automatically expand emails to access unsubscribe links.')
      }

      // Process emails in smaller batches with better timing to improve performance
      const batchSize = 5 // Reduced batch size for better performance
      const batches = []

      for (let i = 0; i < emailsToScan.length; i += batchSize) {
        batches.push(emailsToScan.slice(i, i + batchSize))
      }

      for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
        const batch = batches[batchIndex]

        // Process batch with automatic email expansion
        const batchPromises = batch.map(async (emailElement, index) => {
          try {
            const globalIndex = batchIndex * batchSize + index

            // For list items, try to expand them automatically
            let emailContent = null
            let wasExpanded = false

            // Check if this is a list item that needs expansion
            const isListItem = emailElement.classList.contains('zA') ||
                              emailElement.classList.contains('yW') ||
                              emailElement.tagName === 'TR' ||
                              emailElement.hasAttribute('data-thread-id') ||
                              emailElement.hasAttribute('data-legacy-thread-id') ||
                              emailElement.getAttribute('role') === 'listitem'

            if (isListItem) {
              // This is a list item - try to expand it to get the full email content
              console.log(`Processing list item ${globalIndex + 1}: ${emailElement.tagName}.${Array.from(emailElement.classList).join('.')}`)
              emailContent = await this.expandAndExtractEmail(emailElement, globalIndex)
              wasExpanded = true
            } else {
              // This is already expanded content
              console.log(`Processing expanded content ${globalIndex + 1}: ${emailElement.tagName}.${Array.from(emailElement.classList).join('.')}`)
              emailContent = this.extractEmailContent(emailElement)
            }

            if (!emailContent) {
              return { index: globalIndex, error: 'Could not extract email content' }
            }

            // Extract sender information
            const senderInfo = this.extractSenderInfo(emailElement)

            // Update current email being processed
            this.bulkScanProgress.currentEmail = senderInfo.name || senderInfo.email || `Email ${globalIndex + 1}`

            // Find unsubscribe links
            const unsubscribeLinks = await this.findUnsubscribeLinks(emailContent)

            // If we expanded the email, try to close it to avoid UI clutter
            if (wasExpanded && unsubscribeLinks.length === 0) {
              // Only close if no unsubscribe links found to avoid disrupting user experience
              setTimeout(() => {
                try {
                  // Try to close the expanded email by clicking elsewhere or pressing Escape
                  const closeButton = document.querySelector('.T-I-J3.J-J5-Ji.T-I-JO .ar7')
                  if (closeButton) {
                    closeButton.click()
                  }
                } catch (error) {
                  // Ignore close errors
                }
              }, 100)
            }

            // Return data without modifying shared variables (avoid race conditions)
            return {
              index: globalIndex,
              success: true,
              unsubscribeCount: unsubscribeLinks.length,
              unsubscribeLinks,
              sender: senderInfo
            }
          } catch (error) {
            console.error(`Error processing email ${globalIndex}:`, error)
            return { index: globalIndex, error: error.message }
          }
        })

        // Wait for batch to complete
        const batchResults = await Promise.all(batchPromises)

        // Process results and collect errors
        batchResults.forEach(result => {
          if (result.error) {
            errors.push(result.error)
          } else if (result.success) {
            totalScanned++

            if (result.unsubscribeCount > 0) {
              totalWithUnsubscribe++

              // Store in subscription database
              const senderInfo = result.sender
              const senderId = this.generateSenderId(senderInfo)
              const domain = senderInfo.domain || 'unknown'
              const senderEmail = senderInfo.email || 'unknown'

              if (!subscriptionDatabase.has(senderId)) {
                subscriptionDatabase.set(senderId, {
                  senderId,
                  domain,
                  senderEmail,
                  senderName: senderInfo.name || senderEmail,
                  emailCount: 0,
                  unsubscribeLinks: [],
                  lastEmailDate: null,
                  firstDetected: new Date().toISOString()
                })
              }

              const domainData = subscriptionDatabase.get(senderId)
              domainData.emailCount++
              domainData.lastEmailDate = senderInfo.date || new Date().toISOString()

              // Add unique unsubscribe links
              result.unsubscribeLinks.forEach(link => {
                const existingLink = domainData.unsubscribeLinks.find(existing =>
                  existing.href === link.href
                )
                if (!existingLink) {
                  domainData.unsubscribeLinks.push({
                    href: link.href,
                    text: link.text,
                    trustLevel: link.trustLevel,
                    isValid: link.isValid,
                    extractedAt: link.extractedAt
                  })
                }
              })
            }

            // Update bulk scan progress
            this.bulkScanProgress.processed = result.index + 1
            this.bulkScanProgress.withUnsubscribe = totalWithUnsubscribe
          }
        })

        console.log(`Batch ${batchIndex + 1}/${batches.length} completed. Scanned: ${totalScanned}, With unsubscribe: ${totalWithUnsubscribe}`)

        // Optimized delay between batches to balance performance and accuracy
        if (batchIndex < batches.length - 1) {
          const delay = emailsToScan[0] && (emailsToScan[0].classList.contains('zA') || emailsToScan[0].tagName === 'TR') ? 300 : 50
          await new Promise(resolve => setTimeout(resolve, delay))
        }
      }

      // Store the subscription database
      await this.storeSubscriptionDatabase(subscriptionDatabase)

      const endTime = Date.now()
      const duration = endTime - startTime

      // Calculate total unsubscribe links found across all subscriptions
      const totalUnsubscribeLinks = Array.from(subscriptionDatabase.values())
        .reduce((total, subscription) => total + subscription.unsubscribeLinks.length, 0)

      // Mark bulk scan as completed
      this.bulkScanProgress.isRunning = false
      this.bulkScanProgress.currentEmail = 'Scan Complete'

      console.log(`Bulk scan completed successfully! Scanned ${totalScanned} emails, found ${totalWithUnsubscribe} with unsubscribe links, total ${totalUnsubscribeLinks} unsubscribe links in ${duration}ms`)

      return {
        success: true,
        totalScanned,
        totalWithUnsubscribe,
        totalUnsubscribeLinks,
        subscriptionCount: subscriptionDatabase.size,
        subscriptions: Array.from(subscriptionDatabase.values()),
        duration,
        errors: errors.length > 0 ? errors : null
      }
    } catch (error) {
      // Mark bulk scan as failed
      this.bulkScanProgress.isRunning = false
      this.bulkScanProgress.currentEmail = 'Scan Failed'

      console.error('Error in scanAllEmails:', error)
      console.error('Error stack:', error.stack)
      return {
        success: false,
        error: `Bulk scan error: ${error.message}`,
        errorDetails: error.stack
      }
    }
  }

  async expandAndExtractEmail (emailListItem, index) {
    console.log(`Attempting to expand email ${index + 1} for link extraction...`)

    try {
      // Store original state
      const originallySelected = emailListItem.classList.contains('yW')

      // Detect if email is unread before expansion
      // Gmail uses 'zE' class for unread emails and removes it when read
      const wasUnread = emailListItem.classList.contains('zE') ||
                       emailListItem.querySelector('.zE') !== null ||
                       emailListItem.querySelector('[class*="unread"]') !== null ||
                       !emailListItem.classList.contains('yW') // yW typically indicates read/selected

      console.log(`Email ${index + 1} was ${wasUnread ? 'unread' : 'read'} before expansion`)

      // Check if we can find a thread ID to track the expansion
      const threadId = emailListItem.getAttribute('data-thread-id') ||
                       emailListItem.getAttribute('data-legacy-thread-id')

      // Click the email list item to expand it
      emailListItem.click()

      // Optimized wait time for Gmail to process the click and load content
      await new Promise(resolve => setTimeout(resolve, 300))

      // Try multiple strategies to find the expanded content
      let expandedContent = null

      // Strategy 1: Look for content with the same thread ID
      if (threadId) {
        expandedContent = document.querySelector(`[data-thread-id="${threadId}"] .ii.gt .a3s.aiL`) ||
                         document.querySelector(`[data-thread-id="${threadId}"] .a3s.aiL`) ||
                         document.querySelector(`[data-legacy-thread-id="${threadId}"] .ii.gt .a3s.aiL`)
      }

      // Strategy 2: Look for any newly expanded content
      if (!expandedContent) {
        const contentSelectors = [
          '.ii.gt .a3s.aiL', // Full email content
          '.a3s.aiL', // Email content
          '.ii.gt', // Email container
          '.adn.ads .ii.gt' // Conversation email
        ]

        for (const selector of contentSelectors) {
          expandedContent = document.querySelector(selector)
          if (expandedContent && expandedContent.innerHTML.trim().length > 100) { // Ensure substantial content
            break
          }
        }
      }

      // If email was originally unread, mark it as unread again
      if (wasUnread) {
        console.log(`Restoring unread status for email ${index + 1}`)
        setTimeout(() => {
          try {
            // Try multiple methods to mark as unread
            // Method 1: Click the mark as unread button if visible
            const unreadButton = document.querySelector('[data-tooltip*="unread" i], [aria-label*="unread" i], [title*="unread" i]')
            if (unreadButton && unreadButton.offsetParent !== null) {
              unreadButton.click()
              console.log(`Marked email ${index + 1} as unread via button`)
              return
            }

            // Method 2: Use keyboard shortcut (Shift+U for mark as unread)
            if (document.activeElement) {
              const event = new KeyboardEvent('keydown', {
                key: 'u',
                shiftKey: true,
                bubbles: true,
                cancelable: true
              })
              document.activeElement.dispatchEvent(event)
              console.log(`Marked email ${index + 1} as unread via keyboard shortcut`)
              return
            }

            // Method 3: Try to restore the unread class directly
            if (emailListItem) {
              emailListItem.classList.add('zE')
              const unreadIndicator = emailListItem.querySelector('.yW')
              if (unreadIndicator) {
                unreadIndicator.classList.remove('yW')
              }
              console.log(`Marked email ${index + 1} as unread via class manipulation`)
            }
          } catch (restoreError) {
            console.log(`Could not restore unread status for email ${index + 1}:`, restoreError.message)
          }
        }, 200) // Small delay to ensure Gmail has processed the expansion
      }

      if (expandedContent && expandedContent.innerHTML.trim().length > 0) {
        console.log(`Successfully expanded email ${index + 1}, found ${expandedContent.querySelectorAll('a[href]').length} links`)
        return expandedContent
      } else {
        console.log(`Could not expand email ${index + 1}, will scan list item metadata`)
        return emailListItem
      }
    } catch (error) {
      console.log(`Error expanding email ${index + 1}:`, error.message)
      return emailListItem
    }
  }

  extractEmailContent (emailElement) {
    // Try multiple strategies to extract email content
    let content = null

    // Strategy 1: Look for email body content
    const bodySelectors = [
      '.ii.gt .a3s.aiL', // Main email content
      '.a3s.aiL', // Alternative email content
      '.ii.gt', // Email container
      '.adn.ads .ii.gt', // Conversation view
      '.hiP .ii.gt', // Another conversation format
      '.message-content', // Generic message content
      '[data-message-id] .ii.gt', // Message with ID
      '.gmail_default', // Default Gmail content
      '.gA .ii.gt', // Another Gmail layout
      '.kv .ii.gt' // Yet another layout
    ]

    for (const selector of bodySelectors) {
      content = emailElement.querySelector(selector)
      if (content && content.innerHTML.trim().length > 0) {
        break
      }
    }

    // Strategy 2: For list view emails, try to get the expanded version
    if (!content || content.innerHTML.trim().length === 0) {
      // If this is a list item, try to find the content elsewhere
      if (emailElement.classList.contains('zA') || emailElement.tagName === 'TR') {
        // Try to find associated content containers
        const threadId = emailElement.getAttribute('data-thread-id') ||
                        emailElement.getAttribute('data-legacy-thread-id')

        if (threadId) {
          // Look for expanded content with the same thread ID
          const expandedContent = document.querySelector(`[data-thread-id="${threadId}"] .ii.gt`) ||
                                  document.querySelector(`[data-legacy-thread-id="${threadId}"] .ii.gt`)
          if (expandedContent) {
            content = expandedContent
          }
        }
      }
    }

    // Strategy 3: Try expanding collapsed emails or clicking on list items
    if (!content || content.innerHTML.trim().length === 0) {
      // First try expand buttons
      const expandButton = emailElement.querySelector('.bog, .aiq, .aj9, .aiz')
      if (expandButton) {
        console.log('Found expand button, clicking to load email content...')
        expandButton.click()
        // Small delay to allow content to load
        setTimeout(() => {
          for (const selector of bodySelectors) {
            const newContent = emailElement.querySelector(selector)
            if (newContent && newContent.innerHTML.trim().length > 0) {
              content = newContent
              break
            }
          }
        }, 100)
      } else if (emailElement.classList.contains('zA') || emailElement.tagName === 'TR') {
        // For inbox list items, try clicking to open the email
        console.log('Attempting to click list item to expand email...')
        try {
          emailElement.click()
          // Give it a moment to expand
          setTimeout(() => {
            // Look for the expanded content in the page
            const expandedContent = document.querySelector('.ii.gt .a3s.aiL, .a3s.aiL, .ii.gt')
            if (expandedContent && expandedContent.innerHTML.trim().length > 0) {
              content = expandedContent
              console.log('Successfully expanded email content via click')
            }
          }, 200)
        } catch (error) {
          console.log('Could not click list item:', error.message)
        }
      }
    }

    // Strategy 4: For bulk scan - if still no content, use the emailElement itself
    // This ensures we can scan the visible text in list items even without expanded content
    if (!content || content.innerHTML.trim().length === 0) {
      // Check if emailElement has any text content at all
      if (emailElement.textContent && emailElement.textContent.trim().length > 0) {
        console.log('Using emailElement itself for content extraction (bulk scan fallback)')
        content = emailElement
      }
    }

    // Strategy 5: Last resort - always return something for bulk scan to work
    if (!content) {
      console.log('No content found, returning emailElement as fallback')
      content = emailElement
    }

    return content
  }

  extractSenderInfo (emailElement) {
    const senderInfo = {}

    // Add null check for emailElement - return empty if no current email
    if (!emailElement || !emailElement.querySelector) {
      console.warn('Invalid emailElement provided to extractSenderInfo - no current email found')
      return {
        email: null,
        name: null,
        domain: null,
        date: null,
        subject: null
      }
    }

    // Extract sender email/name with improved selectors
    const senderSelectors = [
      '.go .gb .g2[email]',
      '.yW .gb[email]',
      '.a1.aN[email]',
      '.qu .go .gb[email]',
      '.yW span[email]',
      '.yW span[name]',
      '.gb[email]',
      '.gb[name]',
      '.oL.aDm .oM .aDn .oZ-x3d .oY .aoo span[email]'
    ]

    for (const selector of senderSelectors) {
      try {
        const element = emailElement.querySelector(selector)
        if (element) {
          senderInfo.email = element.getAttribute('email') || element.textContent.trim()
          senderInfo.name = element.getAttribute('name') || element.getAttribute('title') || element.textContent.trim()
          break
        }
      } catch (error) {
        console.warn(`Error querying selector ${selector}:`, error)
        continue
      }
    }

    // If no email found, try fallback selectors without email attribute (still scoped to emailElement)
    if (!senderInfo.email) {
      const fallbackSelectors = [
        '.go .gb .g2',
        '.yW .gb',
        '.a1.aN',
        '.qu .go .gb',
        '.gD'
      ]

      for (const selector of fallbackSelectors) {
        try {
          const element = emailElement.querySelector(selector)
          if (element) {
            const text = element.textContent.trim()
            if (text.includes('@') && text.includes('.')) {
              senderInfo.email = text
              senderInfo.name = text.split('@')[0].replace(/[._-]/g, ' ')
              break
            } else if (text && !senderInfo.name) {
              senderInfo.name = text
            }
          }
        } catch (error) {
          console.warn(`Error querying fallback selector ${selector}:`, error)
          continue
        }
      }
    }

    // Extract domain from email
    if (senderInfo.email && senderInfo.email.includes('@')) {
      senderInfo.domain = senderInfo.email.split('@')[1].toLowerCase()
    }

    // Extract date with improved selectors (scoped to emailElement)
    const dateSelectors = [
      '.g3',
      '.xY .g3',
      '.zu .g3',
      '.xY span[title]',
      '.date',
      '.aH9.r4 .aOB .a12',
      '.thread-date'
    ]

    for (const selector of dateSelectors) {
      try {
        const element = emailElement.querySelector(selector)
        if (element) {
          senderInfo.date = element.getAttribute('title') || element.textContent.trim()
          break
        }
      } catch (error) {
        console.warn(`Error querying date selector ${selector}:`, error)
        continue
      }
    }

    // Extract subject (scoped to emailElement)
    const subjectSelectors = [
      '.hP',
      '.bog',
      '.hU',
      '.thread-subject',
      '.aoo .aoo .aoo .aoo .a1f .aOB'
    ]

    for (const selector of subjectSelectors) {
      try {
        const element = emailElement.querySelector(selector)
        if (element) {
          senderInfo.subject = element.textContent.trim()
          break
        }
      } catch (error) {
        console.warn(`Error querying subject selector ${selector}:`, error)
        continue
      }
    }

    // Debug logging to track what sender info was extracted
    console.log('🔍 extractSenderInfo result:', {
      senderInfo,
      hasEmail: !!senderInfo.email,
      hasDomain: !!senderInfo.domain,
      hasName: !!senderInfo.name,
      hasSubject: !!senderInfo.subject
    })

    // Return only what was found from the current email element - no fallback
    return {
      email: senderInfo.email || null,
      name: senderInfo.name || null,
      domain: senderInfo.domain || null,
      date: senderInfo.date || null,
      subject: senderInfo.subject || null
    }
  }

  async storeSubscriptionDatabase (subscriptionDatabase) {
    try {
    // Convert Map to array with enhanced Task 6B schema
      const subscriptions = Array.from(subscriptionDatabase.values()).map(domainData => ({
      // Core sender information
        senderId: this.generateSenderId({
          email: domainData.senderEmail,
          domain: domainData.domain
        }),
        senderName: domainData.senderName,
        senderEmail: domainData.senderEmail,
        senderDomain: domainData.domain,

        // Unsubscribe link information - latest for display, all for execution
        unsubscribeUrl: this.getLatestUnsubscribeLink(domainData.unsubscribeLinks)?.href || '',
        unsubscribeText: this.getLatestUnsubscribeLink(domainData.unsubscribeLinks)?.text || '',
        unsubscribeType: this.getLatestUnsubscribeLink(domainData.unsubscribeLinks)?.type || 'http',
        displayLink: this.getLatestUnsubscribeLink(domainData.unsubscribeLinks),

        // Email pattern analysis
        lastEmailDate: domainData.lastEmailDate,
        frequency: this.calculateFrequencyFromCount(domainData.emailCount, domainData.firstDetected),
        emailCount: domainData.emailCount,

        // Security analysis
        securityStatus: this.determineSecurityStatusFromLinks(domainData.unsubscribeLinks),
        trustLevel: this.calculateAverageTrustLevel(domainData.unsubscribeLinks),

        // Categorization (will be enhanced in later tasks)
        category: 'unknown',

        // Status tracking
        status: 'active',
        discoveredDate: domainData.firstDetected,
        lastUpdated: new Date().toISOString(),
        needsSecurityAnalysis: this.needsSecurityAnalysis(domainData.unsubscribeLinks),

        // All unsubscribe links for this sender
        allUnsubscribeLinks: domainData.unsubscribeLinks.map(link => ({
          href: link.href,
          text: link.text,
          trustLevel: link.trustLevel,
          isValid: link.isValid,
          extractedAt: link.extractedAt
        }))
      }))

      // Update subscription metadata
      const metadata = {
        lastScan: new Date().toISOString(),
        totalSubscriptions: subscriptions.length,
        lastUpdate: new Date().toISOString(),
        scanType: 'bulk_scan',
        totalEmailsProcessed: Array.from(subscriptionDatabase.values())
          .reduce((sum, data) => sum + data.emailCount, 0)
      }

      // Store enhanced subscription database
      await chrome.storage.local.set({
        subscriptionDatabase: subscriptions,
        subscriptionMetadata: metadata
      })

      console.log(`Enhanced subscription database stored: ${subscriptions.length} subscriptions with comprehensive metadata`)
    } catch (error) {
      console.error('Error storing subscription database:', error)
    }
  }

  // Helper methods for Task 6B: Subscription Database Architecture
  calculateFrequencyFromCount (emailCount, firstDetected) {
    try {
      const daysSinceFirst = (Date.now() - new Date(firstDetected)) / (1000 * 60 * 60 * 24)
      const emailsPerDay = emailCount / Math.max(daysSinceFirst, 1)

      if (emailsPerDay >= 1) return 'daily'
      if (emailsPerDay >= 0.2) return 'weekly' // More than once per 5 days
      if (emailsPerDay >= 0.05) return 'monthly' // More than once per 20 days
      return 'rare'
    } catch (error) {
      return 'unknown'
    }
  }

  determineSecurityStatusFromLinks (unsubscribeLinks) {
    if (!unsubscribeLinks || unsubscribeLinks.length === 0) return 'unknown'

    const avgTrustLevel = this.calculateAverageTrustLevel(unsubscribeLinks)

    if (avgTrustLevel < 30) return 'high_risk'
    if (avgTrustLevel < 50) return 'medium_risk'
    if (avgTrustLevel < 80) return 'low_risk'
    return 'safe'
  }

  calculateAverageTrustLevel (unsubscribeLinks) {
    if (!unsubscribeLinks || unsubscribeLinks.length === 0) return 0

    const totalTrust = unsubscribeLinks.reduce((sum, link) => sum + (link.trustLevel || 0), 0)
    return Math.round(totalTrust / unsubscribeLinks.length)
  }

  needsSecurityAnalysis (unsubscribeLinks) {
    if (!unsubscribeLinks || unsubscribeLinks.length === 0) return true

    return unsubscribeLinks.some(link =>
      link.trustLevel < 80 ||
      !link.isValid ||
      (link.phishingCheck && link.phishingCheck.isPhishing)
    )
  }

  // Smart link selection for display and execution
  getLatestUnsubscribeLink (unsubscribeLinks) {
    if (!unsubscribeLinks || unsubscribeLinks.length === 0) return null

    // Sort by extractedAt timestamp (most recent first), fallback to array order
    const sortedLinks = unsubscribeLinks.slice().sort((a, b) => {
      const timeA = a.extractedAt ? new Date(a.extractedAt).getTime() : 0
      const timeB = b.extractedAt ? new Date(b.extractedAt).getTime() : 0

      // If timestamps are equal or missing, use trust level as tiebreaker
      if (timeA === timeB) {
        return (b.trustLevel || 0) - (a.trustLevel || 0)
      }

      return timeB - timeA
    })

    return sortedLinks[0]
  }

  getSortedUnsubscribeLinks (unsubscribeLinks) {
    if (!unsubscribeLinks || unsubscribeLinks.length === 0) return []

    // Sort all links by preference for comprehensive unsubscribe execution
    return unsubscribeLinks.slice().sort((a, b) => {
      const timeA = a.extractedAt ? new Date(a.extractedAt).getTime() : 0
      const timeB = b.extractedAt ? new Date(b.extractedAt).getTime() : 0

      // Primary sort: Most recent first
      if (timeA !== timeB) {
        return timeB - timeA
      }

      // Secondary sort: Higher trust level first
      const trustA = a.trustLevel || 0
      const trustB = b.trustLevel || 0
      if (trustA !== trustB) {
        return trustB - trustA
      }

      // Tertiary sort: Prefer main domain over subdomains
      const domainScoreA = this.getDomainReliabilityScore(a.href)
      const domainScoreB = this.getDomainReliabilityScore(b.href)
      return domainScoreB - domainScoreA
    })
  }

  getDomainReliabilityScore (url) {
    try {
      const urlObj = new URL(url)
      const hostname = urlObj.hostname.toLowerCase()

      // Main domain gets highest score
      const parts = hostname.split('.')
      if (parts.length === 2) return 100 // company.com

      // Common reliable subdomains
      if (hostname.startsWith('unsubscribe.')) return 90
      if (hostname.startsWith('email.')) return 85
      if (hostname.startsWith('mail.')) return 80
      if (hostname.startsWith('newsletter.')) return 75
      if (hostname.startsWith('marketing.')) return 70
      if (hostname.startsWith('notifications.')) return 65

      // Other subdomains get lower scores
      return 50
    } catch (error) {
      return 0 // Invalid URL
    }
  }

  // Task 6B: Database Management and Query Methods
  async getSubscriptionDatabase () {
    try {
      const result = await chrome.storage.local.get(['subscriptionDatabase', 'subscriptionMetadata'])
      return {
        subscriptions: result.subscriptionDatabase || [],
        metadata: result.subscriptionMetadata || {
          lastScan: null,
          totalSubscriptions: 0,
          lastUpdate: null
        }
      }
    } catch (error) {
      console.error('Error getting subscription database:', error)
      return { subscriptions: [], metadata: {} }
    }
  }

  async getSubscriptionBySenderId (senderId) {
    try {
      const { subscriptions } = await this.getSubscriptionDatabase()
      return subscriptions.find(sub => sub.senderId === senderId) || null
    } catch (error) {
      console.error('Error getting subscription by sender ID:', error)
      return null
    }
  }

  async getSubscriptionsByDomain (domain) {
    try {
      const { subscriptions } = await this.getSubscriptionDatabase()
      return subscriptions.filter(sub => sub.senderDomain === domain)
    } catch (error) {
      console.error('Error getting subscriptions by domain:', error)
      return []
    }
  }

  async getSubscriptionsBySecurityStatus (securityStatus) {
    try {
      const { subscriptions } = await this.getSubscriptionDatabase()
      return subscriptions.filter(sub => sub.securityStatus === securityStatus)
    } catch (error) {
      console.error('Error getting subscriptions by security status:', error)
      return []
    }
  }

  async updateSubscriptionStatus (senderId, newStatus) {
    try {
      const { subscriptions, metadata } = await this.getSubscriptionDatabase()
      const subscriptionIndex = subscriptions.findIndex(sub => sub.senderId === senderId)

      if (subscriptionIndex >= 0) {
        subscriptions[subscriptionIndex].status = newStatus
        subscriptions[subscriptionIndex].lastUpdated = new Date().toISOString()

        await chrome.storage.local.set({
          subscriptionDatabase: subscriptions,
          subscriptionMetadata: {
            ...metadata,
            lastUpdate: new Date().toISOString()
          }
        })

        console.log(`Updated subscription status for ${senderId} to ${newStatus}`)
        return true
      }

      return false
    } catch (error) {
      console.error('Error updating subscription status:', error)
      return false
    }
  }

  async cleanupOldSubscriptions (daysOld = 90) {
    try {
      const { subscriptions, metadata } = await this.getSubscriptionDatabase()
      const cutoffDate = new Date()
      cutoffDate.setDate(cutoffDate.getDate() - daysOld)

      const activeSubscriptions = subscriptions.filter(sub => {
        const lastEmail = new Date(sub.lastEmailDate)
        return lastEmail > cutoffDate
      })

      if (activeSubscriptions.length !== subscriptions.length) {
        await chrome.storage.local.set({
          subscriptionDatabase: activeSubscriptions,
          subscriptionMetadata: {
            ...metadata,
            totalSubscriptions: activeSubscriptions.length,
            lastUpdate: new Date().toISOString(),
            lastCleanup: new Date().toISOString()
          }
        })

        const removedCount = subscriptions.length - activeSubscriptions.length
        console.log(`Cleaned up ${removedCount} old subscriptions`)
        return removedCount
      }

      return 0
    } catch (error) {
      console.error('Error cleaning up old subscriptions:', error)
      return 0
    }
  }

  async getDatabaseStats () {
    try {
      const { subscriptions, metadata } = await this.getSubscriptionDatabase()

      const stats = {
        totalSubscriptions: subscriptions.length,
        bySecurityStatus: {
          safe: subscriptions.filter(s => s.securityStatus === 'safe').length,
          low_risk: subscriptions.filter(s => s.securityStatus === 'low_risk').length,
          medium_risk: subscriptions.filter(s => s.securityStatus === 'medium_risk').length,
          high_risk: subscriptions.filter(s => s.securityStatus === 'high_risk').length,
          unknown: subscriptions.filter(s => s.securityStatus === 'unknown').length
        },
        byFrequency: {
          daily: subscriptions.filter(s => s.frequency === 'daily').length,
          weekly: subscriptions.filter(s => s.frequency === 'weekly').length,
          monthly: subscriptions.filter(s => s.frequency === 'monthly').length,
          rare: subscriptions.filter(s => s.frequency === 'rare').length,
          unknown: subscriptions.filter(s => s.frequency === 'unknown').length
        },
        byStatus: {
          active: subscriptions.filter(s => s.status === 'active').length,
          unsubscribed: subscriptions.filter(s => s.status === 'unsubscribed').length,
          failed: subscriptions.filter(s => s.status === 'failed').length
        },
        totalEmailsTracked: subscriptions.reduce((sum, s) => sum + s.emailCount, 0),
        needingSecurityAnalysis: subscriptions.filter(s => s.needsSecurityAnalysis).length,
        averageTrustLevel: subscriptions.length > 0
          ? Math.round(subscriptions.reduce((sum, s) => sum + s.trustLevel, 0) / subscriptions.length)
          : 0,
        lastUpdate: metadata.lastUpdate,
        lastScan: metadata.lastScan
      }

      return stats
    } catch (error) {
      console.error('Error getting database stats:', error)
      return null
    }
  }

  // User feedback and override system methods
  async loadUserPreferences () {
    try {
      // Check if extension context is still valid
      if (!this.isExtensionContextValid()) {
        console.warn('Extension context invalidated, skipping user preferences load')
        return
      }

      const result = await chrome.storage.local.get(['userFeedback', 'manualOverrides'])

      if (result.userFeedback) {
        this.userFeedback = new Map(result.userFeedback)
      }

      if (result.manualOverrides) {
        this.manualOverrides = new Map(result.manualOverrides)
      }
    } catch (error) {
      console.error('Error loading user preferences:', error)
    }
  }

  async saveUserPreferences () {
    try {
      await chrome.storage.local.set({
        userFeedback: Array.from(this.userFeedback.entries()),
        manualOverrides: Array.from(this.manualOverrides.entries())
      })
    } catch (error) {
      console.error('Error saving user preferences:', error)
    }
  }

  async reportFalsePositive (domain, url) {
    try {
      // Record the false positive feedback
      if (!this.userFeedback.has(domain)) {
        this.userFeedback.set(domain, { falsePositives: 0, legitimate: 0 })
      }

      const feedback = this.userFeedback.get(domain)
      feedback.falsePositives++
      feedback.lastReported = new Date().toISOString()
      feedback.reportedUrl = url

      this.userFeedback.set(domain, feedback)

      // If domain has multiple false positive reports, add to trusted domains
      if (feedback.falsePositives >= 2) {
        this.trustedDomains.push(domain)
        console.log(`Added ${domain} to trusted domains due to false positive reports`)
      }

      await this.saveUserPreferences()

      return { success: true, message: 'False positive reported successfully' }
    } catch (error) {
      console.error('Error reporting false positive:', error)
      return { success: false, error: error.message }
    }
  }

  async setManualOverride (domain, trustLevel) {
    try {
      this.manualOverrides.set(domain, {
        trustLevel: Math.max(0, Math.min(100, trustLevel)),
        setAt: new Date().toISOString()
      })

      await this.saveUserPreferences()

      return { success: true, message: 'Manual override set successfully' }
    } catch (error) {
      console.error('Error setting manual override:', error)
      return { success: false, error: error.message }
    }
  }

  getManualOverride (domain) {
    return this.manualOverrides.get(domain)
  }

  async checkDomainReputation (domain) {
    // Check if domain has user feedback
    const feedback = this.userFeedback.get(domain)
    if (feedback && feedback.falsePositives > 0) {
      return {
        isLegitimate: true,
        source: 'user_feedback',
        score: Math.min(80 + (feedback.falsePositives * 10), 100)
      }
    }

    // Check manual overrides
    const override = this.getManualOverride(domain)
    if (override) {
      return {
        isLegitimate: override.trustLevel >= 70,
        source: 'manual_override',
        score: override.trustLevel
      }
    }

    // Check real-time domain verification APIs
    const apiResult = await this.checkDomainWithAPIs(domain)
    if (apiResult) {
      return apiResult
    }

    return null
  }

  async checkDomainWithAPIs (domain) {
    // List of legitimate domain verification sources
    const verificationSources = [
      // Company registry APIs
      { name: 'crunchbase', check: this.checkCrunchbase },
      { name: 'clearbit', check: this.checkClearbit },
      { name: 'builtwith', check: this.checkBuiltWith },
      // Email service provider APIs
      { name: 'mailchimp_partners', check: this.checkMailChimpPartners },
      { name: 'sendgrid_partners', check: this.checkSendGridPartners },
      // Security/reputation APIs
      { name: 'virustotal', check: this.checkVirusTotal },
      { name: 'google_safe_browsing', check: this.checkGoogleSafeBrowsing },
      // Domain age and registration
      { name: 'whois', check: this.checkWhoisData },
      { name: 'domain_tools', check: this.checkDomainTools }
    ]

    // Check multiple sources for domain legitimacy
    for (const source of verificationSources) {
      try {
        const result = await source.check(domain)
        if (result && result.isLegitimate) {
          return {
            isLegitimate: true,
            source: source.name,
            score: result.trustScore || 75,
            verified: true
          }
        }
      } catch (error) {
        // Continue to next source if one fails
        console.log(`API check failed for ${source.name}:`, error.message)
      }
    }

    return null
  }

  // SSL Certificate validation (simplified to avoid CORS issues)
  async validateSSLCertificate (url) {
    try {
      const urlObj = new URL(url)

      // Only validate HTTPS URLs
      if (urlObj.protocol !== 'https:') {
        return {
          isValid: false,
          reason: 'Not using HTTPS',
          trustScore: 0
        }
      }

      // Basic HTTPS validation without CORS-problematic fetch
      // Check if URL uses HTTPS and has valid structure
      if (urlObj.hostname && urlObj.hostname.includes('.')) {
        return {
          isValid: true,
          reason: 'Uses HTTPS protocol',
          trustScore: 15
        }
      } else {
        return {
          isValid: false,
          reason: 'Invalid hostname structure',
          trustScore: -10
        }
      }
    } catch (error) {
      return {
        isValid: false,
        reason: 'Invalid URL structure',
        trustScore: -10
      }
    }
  }

  // Enhanced phishing pattern detection
  detectPhishingPatterns (href, text, title) {
    const phishingIndicators = {
      urgency: [
        /urgent/i, /immediate/i, /expires? (today|soon|now)/i,
        /act now/i, /limited time/i, /expires? in/i,
        /final notice/i, /last chance/i, /deadline/i
      ],
      financial: [
        /payment.*fail/i, /billing.*issue/i, /account.*suspend/i,
        /unauthorized.*charge/i, /refund.*process/i, /credit.*expire/i,
        /bank.*verify/i, /update.*payment/i, /billing.*update/i
      ],
      security: [
        /security.*alert/i, /suspicious.*activity/i, /account.*compromise/i,
        /login.*attempt/i, /password.*expire/i, /verify.*identity/i,
        /confirm.*account/i, /validate.*account/i, /reactivate.*account/i
      ],
      generic: [
        /click.*here.*to/i, /verify.*email/i, /confirm.*subscription/i,
        /update.*information/i, /maintain.*access/i, /continue.*service/i
      ],
      suspicious_domains: [
        /paypal-/i, /amazon-/i, /apple-/i, /microsoft-/i, /google-/i,
        /facebook-/i, /twitter-/i, /linkedin-/i, /instagram-/i,
        /secure-/i, /account-/i, /login-/i, /verify-/i, /update-/i
      ]
    }

    const detectedPatterns = []
    let riskScore = 0

    // Check all text content
    const textContent = [href, text, title].join(' ').toLowerCase()

    for (const [category, patterns] of Object.entries(phishingIndicators)) {
      for (const pattern of patterns) {
        if (pattern.test(textContent)) {
          detectedPatterns.push({
            category,
            pattern: pattern.source,
            match: textContent.match(pattern)?.[0]
          })
          riskScore += category === 'financial'
            ? 30
            : category === 'security'
              ? 25
              : category === 'urgency' ? 20 : 15
        }
      }
    }

    // Check for suspicious URL patterns
    try {
      const urlObj = new URL(href)

      // Check for suspicious domain patterns
      if (phishingIndicators.suspicious_domains.some(pattern => pattern.test(urlObj.hostname))) {
        detectedPatterns.push({
          category: 'suspicious_domain',
          pattern: 'Domain name mimicking legitimate service',
          match: urlObj.hostname
        })
        riskScore += 35
      }

      // Check for URL shorteners (higher risk)
      const shorteners = ['bit.ly', 'tinyurl.com', 'goo.gl', 't.co', 'ow.ly']
      if (shorteners.some(shortener => urlObj.hostname.includes(shortener))) {
        detectedPatterns.push({
          category: 'url_shortener',
          pattern: 'URL shortener detected',
          match: urlObj.hostname
        })
        riskScore += 20
      }

      // Check for suspicious URL structure
      if (urlObj.pathname.includes('..') || urlObj.pathname.includes('//')) {
        detectedPatterns.push({
          category: 'url_structure',
          pattern: 'Suspicious URL structure',
          match: urlObj.pathname
        })
        riskScore += 25
      }
    } catch (error) {
      // Invalid URL structure is suspicious
      detectedPatterns.push({
        category: 'invalid_url',
        pattern: 'Invalid URL structure',
        match: href
      })
      riskScore += 40
    }

    return {
      isPhishing: riskScore > 50,
      riskScore,
      detectedPatterns,
      riskLevel: riskScore > 70 ? 'high' : riskScore > 40 ? 'medium' : 'low'
    }
  }

  // Local blacklist management
  async loadLocalBlacklist () {
    try {
      // Check if extension context is still valid
      if (!this.isExtensionContextValid()) {
        console.warn('Extension context invalidated, returning empty blacklist')
        return []
      }

      const result = await chrome.storage.local.get(['localBlacklist'])
      return result.localBlacklist || []
    } catch (error) {
      console.error('Error loading local blacklist:', error)
      return []
    }
  }

  async addToLocalBlacklist (domain, reason) {
    try {
      const blacklist = await this.loadLocalBlacklist()
      const entry = {
        domain,
        reason,
        addedAt: new Date().toISOString(),
        reportedBy: 'user'
      }

      // Avoid duplicates
      if (!blacklist.some(item => item.domain === domain)) {
        blacklist.push(entry)
        await chrome.storage.local.set({ localBlacklist: blacklist })
        console.log(`Added ${domain} to local blacklist`)
      }

      return { success: true, message: 'Domain added to blacklist' }
    } catch (error) {
      console.error('Error adding to blacklist:', error)
      return { success: false, error: error.message }
    }
  }

  async removeFromLocalBlacklist (domain) {
    try {
      const blacklist = await this.loadLocalBlacklist()
      const filtered = blacklist.filter(item => item.domain !== domain)
      await chrome.storage.local.set({ localBlacklist: filtered })
      console.log(`Removed ${domain} from local blacklist`)
      return { success: true, message: 'Domain removed from blacklist' }
    } catch (error) {
      console.error('Error removing from blacklist:', error)
      return { success: false, error: error.message }
    }
  }

  async isInLocalBlacklist (domain) {
    try {
      const blacklist = await this.loadLocalBlacklist()
      return blacklist.find(item => item.domain === domain) || null
    } catch (error) {
      console.error('Error checking blacklist:', error)
      return null
    }
  }

  // Generate security warnings for links
  generateSecurityWarnings (href, text, title, phishingCheck, trustLevel) {
    const warnings = []

    // Trust level warnings
    if (trustLevel < 30) {
      warnings.push({
        type: 'danger',
        message: 'Very low trust score - exercise extreme caution',
        icon: '🛑'
      })
    } else if (trustLevel < 50) {
      warnings.push({
        type: 'warning',
        message: 'Low trust score - verify before clicking',
        icon: '⚠️'
      })
    }

    // Phishing pattern warnings
    if (phishingCheck.isPhishing) {
      warnings.push({
        type: 'danger',
        message: `Phishing patterns detected (${phishingCheck.riskLevel} risk)`,
        icon: '🎣',
        details: phishingCheck.detectedPatterns.map(p => p.category).join(', ')
      })
    }

    // SSL warnings
    try {
      const url = new URL(href)
      if (url.protocol === 'http:') {
        warnings.push({
          type: 'warning',
          message: 'Link uses insecure HTTP connection',
          icon: '🔓'
        })
      }
    } catch (error) {
      warnings.push({
        type: 'danger',
        message: 'Invalid URL format',
        icon: '❌'
      })
    }

    // URL shortener warnings
    const shorteners = ['bit.ly', 'tinyurl.com', 'goo.gl', 't.co', 'ow.ly']
    try {
      const url = new URL(href)
      if (shorteners.some(shortener => url.hostname.includes(shortener))) {
        warnings.push({
          type: 'warning',
          message: 'URL shortener detected - destination unclear',
          icon: '🔗'
        })
      }
    } catch (error) {
      // Already handled above
    }

    return warnings
  }

  // Show security confirmation dialog for risky links
  async showSecurityConfirmation (link) {
    return new Promise((resolve) => {
      const confirmModal = document.createElement('div')
      confirmModal.className = 'unsubscriber-security-modal'
      confirmModal.innerHTML = `
        <div class="unsubscriber-security-modal-content">
          <div class="unsubscriber-security-modal-header">
            <h3>🛡️ Security Warning</h3>
          </div>
          <div class="unsubscriber-security-modal-body">
            <div class="security-alert">
              <p><strong>This link may be risky!</strong></p>
              <div class="risk-details">
                <p><strong>Trust Level:</strong> ${link.trustLevel}%</p>
                <p><strong>URL:</strong> <code>${link.href}</code></p>
                ${link.securityWarnings && link.securityWarnings.length > 0
                  ? `<div class="warning-list">
                    <p><strong>Security Warnings:</strong></p>
                    <ul>
                      ${link.securityWarnings.map(warning =>
                        `<li>${warning.icon} ${warning.message}</li>`
                      ).join('')}
                    </ul>
                  </div>`
: ''}
                ${link.phishingCheck && link.phishingCheck.isPhishing
                  ? `<div class="phishing-warning">
                    <p><strong>Phishing Risk:</strong> ${link.phishingCheck.riskLevel}</p>
                    <p>Detected patterns: ${link.phishingCheck.detectedPatterns.map(p => p.category).join(', ')}</p>
                  </div>`
: ''}
              </div>
            </div>
            <div class="security-actions">
              <button class="security-btn cancel-btn">Cancel</button>
              <button class="security-btn add-blacklist-btn">Add to Blacklist</button>
              <button class="security-btn proceed-btn">Proceed Anyway</button>
            </div>
          </div>
        </div>
      `

      // Add security modal styles
      const securityStyle = document.createElement('style')
      securityStyle.textContent = `
        .unsubscriber-security-modal {
          position: fixed;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background: rgba(0, 0, 0, 0.7);
          display: flex;
          justify-content: center;
          align-items: center;
          z-index: 10001;
        }
        
        .unsubscriber-security-modal-content {
          background: white;
          border-radius: 8px;
          padding: 0;
          max-width: 600px;
          width: 90%;
          max-height: 80vh;
          overflow-y: auto;
          box-shadow: 0 4px 20px rgba(0, 0, 0, 0.4);
        }
        
        .unsubscriber-security-modal-header {
          padding: 16px 20px;
          border-bottom: 1px solid #e8eaed;
          background: #fce8e6;
        }
        
        .unsubscriber-security-modal-header h3 {
          margin: 0;
          color: #ea4335;
        }
        
        .unsubscriber-security-modal-body {
          padding: 20px;
        }
        
        .security-alert {
          margin-bottom: 20px;
        }
        
        .risk-details {
          margin-top: 12px;
          padding: 12px;
          background: #f8f9fa;
          border-radius: 4px;
          border-left: 4px solid #ea4335;
        }
        
        .warning-list ul {
          margin: 8px 0;
          padding-left: 20px;
        }
        
        .warning-list li {
          margin: 4px 0;
          color: #ea4335;
        }
        
        .phishing-warning {
          margin-top: 12px;
          padding: 8px;
          background: #fce8e6;
          border-radius: 4px;
          color: #ea4335;
        }
        
        .security-actions {
          display: flex;
          gap: 12px;
          justify-content: flex-end;
        }
        
        .security-btn {
          padding: 8px 16px;
          border: none;
          border-radius: 4px;
          cursor: pointer;
          font-weight: 500;
          transition: all 0.2s ease;
        }
        
        .cancel-btn {
          background: #f8f9fa;
          color: #5f6368;
          border: 1px solid #dadce0;
        }
        
        .cancel-btn:hover {
          background: #e8eaed;
        }
        
        .add-blacklist-btn {
          background: #fbbc04;
          color: white;
        }
        
        .add-blacklist-btn:hover {
          background: #ea8600;
        }
        
        .proceed-btn {
          background: #ea4335;
          color: white;
        }
        
        .proceed-btn:hover {
          background: #d33b2c;
        }
      `

      document.head.appendChild(securityStyle)
      document.body.appendChild(confirmModal)

      // Event listeners
      confirmModal.querySelector('.cancel-btn').addEventListener('click', () => {
        document.body.removeChild(confirmModal)
        document.head.removeChild(securityStyle)
        resolve(false)
      })

      confirmModal.querySelector('.add-blacklist-btn').addEventListener('click', async () => {
        const url = new URL(link.href)
        await this.addToLocalBlacklist(url.hostname, 'User reported as suspicious')
        document.body.removeChild(confirmModal)
        document.head.removeChild(securityStyle)
        resolve(false)
      })

      confirmModal.querySelector('.proceed-btn').addEventListener('click', () => {
        document.body.removeChild(confirmModal)
        document.head.removeChild(securityStyle)
        resolve(true)
      })
    })
  }

  // Record unsubscribe action for analytics
  async recordUnsubscribeAction (link) {
    try {
      const action = {
        timestamp: new Date().toISOString(),
        href: link.href,
        trustLevel: link.trustLevel,
        isPhishing: link.phishingCheck && link.phishingCheck.isPhishing,
        securityWarnings: link.securityWarnings ? link.securityWarnings.length : 0,
        text: link.text
      }

      // Store in Chrome storage
      const result = await chrome.storage.local.get(['unsubscribeActions'])
      const actions = result.unsubscribeActions || []
      actions.push(action)

      // Keep only last 1000 actions
      if (actions.length > 1000) {
        actions.splice(0, actions.length - 1000)
      }

      await chrome.storage.local.set({ unsubscribeActions: actions })
      console.log('Unsubscribe action recorded:', action)
    } catch (error) {
      console.error('Error recording unsubscribe action:', error)
    }
  }

  // Get security statistics for dashboard
  async getSecurityStats () {
    try {
      const result = await chrome.storage.local.get([
        'unsubscribeActions',
        'localBlacklist',
        'unsubscribeExtractions'
      ])

      const actions = result.unsubscribeActions || []
      const blacklist = result.localBlacklist || []
      const extractions = result.unsubscribeExtractions || []

      // Calculate security statistics
      const totalActions = actions.length
      const phishingBlocked = actions.filter(a => a.isPhishing).length
      const lowTrustBlocked = actions.filter(a => a.trustLevel < 50).length
      const securityWarnings = actions.reduce((sum, a) => sum + a.securityWarnings, 0)

      // Recent activity (last 30 days)
      const thirtyDaysAgo = new Date()
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)
      const recentActions = actions.filter(a =>
        new Date(a.timestamp) > thirtyDaysAgo
      )

      // Top risky domains
      const domainRisks = {}
      extractions.forEach(extraction => {
        extraction.links.forEach(link => {
          try {
            const url = new URL(link.href)
            const domain = url.hostname
            if (!domainRisks[domain]) {
              domainRisks[domain] = {
                domain,
                totalLinks: 0,
                avgTrustLevel: 0,
                totalTrustLevel: 0,
                phishingCount: 0
              }
            }
            domainRisks[domain].totalLinks++
            domainRisks[domain].totalTrustLevel += link.trustLevel
            domainRisks[domain].avgTrustLevel =
              domainRisks[domain].totalTrustLevel / domainRisks[domain].totalLinks
          } catch (error) {
            // Skip invalid URLs
          }
        })
      })

      const topRiskyDomains = Object.values(domainRisks)
        .filter(d => d.avgTrustLevel < 60)
        .sort((a, b) => a.avgTrustLevel - b.avgTrustLevel)
        .slice(0, 10)

      return {
        success: true,
        stats: {
          totalActions,
          phishingBlocked,
          lowTrustBlocked,
          securityWarnings,
          blacklistSize: blacklist.length,
          recentActivity: recentActions.length,
          topRiskyDomains,
          lastUpdated: new Date().toISOString()
        }
      }
    } catch (error) {
      console.error('Error getting security stats:', error)
      return {
        success: false,
        error: error.message
      }
    }
  }

  // API verification methods (implement as needed)
  async checkCrunchbase (domain) {
    // Check if domain belongs to a company in Crunchbase
    // This would require API key and proper implementation
    return null
  }

  async checkClearbit (domain) {
    // Check Clearbit Company API for domain legitimacy
    return null
  }

  async checkBuiltWith (domain) {
    // Check BuiltWith API for domain technology stack
    return null
  }

  async checkMailChimpPartners (domain) {
    // Check if domain is a verified MailChimp partner
    return null
  }

  async checkSendGridPartners (domain) {
    // Check if domain is a verified SendGrid partner
    return null
  }

  async checkVirusTotal (domain) {
    // Check VirusTotal API for domain reputation
    // Note: This is a free API with rate limits
    return null
  }

  async checkGoogleSafeBrowsing (domain) {
    // Check Google Safe Browsing API
    return null
  }

  async checkWhoisData (domain) {
    // Check WHOIS data for domain age and registration info
    return null
  }

  async checkDomainTools (domain) {
    // Check DomainTools API for domain reputation
    return null
  }

  getEmailCount () {
    // Count total emails available for scanning
    const emailSelectors = [
      'tr.zA', // Inbox list items (metadata only)
      'tr.yW', // Alternative list items (selected/read)
      'tr[data-thread-id]', // Thread rows with data attributes
      'tr[data-legacy-thread-id]', // Legacy thread rows
      '.zA', // Generic list items (div format)
      '.yW', // Selected/read list items (div format)
      'div[data-thread-id]',
      'div[data-legacy-thread-id]',
      '.nH [role="listitem"]',
      '[role="listitem"]',
      '.Cp [data-thread-perm-id]'
    ]

    let emailElements = []
    for (const selector of emailSelectors) {
      try {
        const elements = Array.from(document.querySelectorAll(selector))
        emailElements = emailElements.concat(elements)
      } catch (error) {
        console.warn(`Error with selector ${selector}:`, error)
      }
    }

    // Remove duplicates and filter visible elements
    const uniqueEmails = emailElements.filter((element, index, self) =>
      self.indexOf(element) === index &&
      element.offsetHeight > 0 &&
      element.offsetWidth > 0
    )

    return uniqueEmails.length
  }

  getBulkScanProgress () {
    return {
      processed: this.bulkScanProgress.processed,
      total: this.bulkScanProgress.total,
      withUnsubscribe: this.bulkScanProgress.withUnsubscribe,
      currentEmail: this.bulkScanProgress.currentEmail,
      isRunning: this.bulkScanProgress.isRunning
    }
  }

  // ==================== DEBUGGING TOOLS ====================

  /**
   * Initialize debugging tools for Gmail integration
   */
  initializeDebuggingTools () {
    if (!this.debugMode) return

    ExtensionLogger.info(ExtensionLogger.COMPONENTS.CONTENT, 'Initializing debugging tools')

    // Make debugging tools available globally
    window.GmailDebugger = this.createGmailDebugger()
    window.VisualDebugger = this.createVisualDebugger()
    window.ExtractionDebugger = this.createExtractionDebugger()

    // Add visual debugging indicators
    this.addVisualDebuggingIndicators()

    ExtensionLogger.info(ExtensionLogger.COMPONENTS.CONTENT, 'Debugging tools initialized', {
      GmailDebugger: 'available',
      VisualDebugger: 'available',
      ExtractionDebugger: 'available'
    })
  }

  /**
   * Create Gmail debugging utilities
   */
  createGmailDebugger () {
    return {
      testEmailDetection: () => {
        ExtensionLogger.info(ExtensionLogger.COMPONENTS.GMAIL, 'Testing email detection')

        const emails = document.querySelectorAll('[data-message-id]')
        ExtensionLogger.info(ExtensionLogger.COMPONENTS.GMAIL, `Found ${emails.length} emails with message IDs`)

        const threadRows = document.querySelectorAll('[data-thread-id]')
        ExtensionLogger.info(ExtensionLogger.COMPONENTS.GMAIL, `Found ${threadRows.length} thread rows`)

        const conversationElements = document.querySelectorAll('.aDP, .ads, .aCS')
        ExtensionLogger.info(ExtensionLogger.COMPONENTS.GMAIL, `Found ${conversationElements.length} conversation elements`)

        emails.forEach((email, index) => {
          const messageId = email.getAttribute('data-message-id')
          const threadId = email.getAttribute('data-thread-id')
          const subject = email.querySelector('.bog')?.textContent || 'No subject found'

          ExtensionLogger.debug(ExtensionLogger.COMPONENTS.GMAIL, `Email ${index + 1}`, {
            messageId,
            threadId,
            subject: subject.substring(0, 100)
          })
        })

        return { emails: emails.length, threads: threadRows.length, conversations: conversationElements.length }
      },

      getCurrentEmailContent: () => {
        const openEmail = document.querySelector('.ii.gt div[data-message-id]')
        if (!openEmail) {
          ExtensionLogger.warn(ExtensionLogger.COMPONENTS.GMAIL, 'No open email found')
          return null
        }

        const messageId = openEmail.getAttribute('data-message-id')
        const content = openEmail.innerHTML

        ExtensionLogger.info(ExtensionLogger.COMPONENTS.GMAIL, 'Current email content retrieved', {
          messageId,
          contentLength: content.length
        })

        return { messageId, content }
      },

      getEmailListState: () => {
        const inboxRows = document.querySelectorAll('tr.zA')
        const selectedRows = document.querySelectorAll('tr.zA.x7')
        const readRows = document.querySelectorAll('tr.zA.yW')
        const unreadRows = document.querySelectorAll('tr.zA:not(.yW)')

        const state = {
          total: inboxRows.length,
          selected: selectedRows.length,
          read: readRows.length,
          unread: unreadRows.length
        }

        ExtensionLogger.info(ExtensionLogger.COMPONENTS.GMAIL, 'Email list state', state)
        return state
      },

      simulateEmailOpen: (messageId) => {
        const email = document.querySelector(`[data-message-id="${messageId}"]`)
        if (email) {
          email.click()
          ExtensionLogger.info(ExtensionLogger.COMPONENTS.GMAIL, 'Simulated email open', { messageId })
        } else {
          ExtensionLogger.warn(ExtensionLogger.COMPONENTS.GMAIL, 'Email not found for simulation', { messageId })
        }
      }
    }
  }

  /**
   * Create visual debugging utilities
   */
  createVisualDebugger () {
    return {
      highlightDetectedEmails: () => {
        const emails = document.querySelectorAll('[data-message-id]')
        let count = 0

        emails.forEach(email => {
          email.style.border = '2px solid #27ae60'
          email.style.boxShadow = '0 0 10px rgba(39, 174, 96, 0.3)'
          email.style.position = 'relative'

          // Add debug badge
          const badge = document.createElement('div')
          badge.textContent = '📧'
          badge.style.cssText = `
            position: absolute;
            top: 5px;
            right: 5px;
            background: #27ae60;
            color: white;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 12px;
            z-index: 9999;
          `
          email.appendChild(badge)
          count++
        })

        ExtensionLogger.info(ExtensionLogger.COMPONENTS.GMAIL, `Highlighted ${count} detected emails`)
        return count
      },

      highlightUnsubscribeLinks: () => {
        // Use the same comprehensive pattern matching as the main scan function
        const allLinks = document.querySelectorAll('a[href]')
        let count = 0

        allLinks.forEach(link => {
          const href = link.getAttribute('href')
          const text = link.textContent.trim()
          const title = link.getAttribute('title') || ''
          const parentText = link.parentElement ? link.parentElement.textContent.trim() : ''

          // Use the same unsubscribe patterns as the main scan function
          const matchesPattern = this.unsubscribePatterns.some(pattern => 
            pattern.test(text) || pattern.test(title) || pattern.test(href) || pattern.test(parentText)
          ) || this.isLikelyUnsubscribeLink(href, text, title, parentText)

          // Check for dangerous patterns (avoid highlighting suspicious links)
          const isDangerous = this.dangerousPatterns.some(pattern =>
            pattern.test(text) || pattern.test(title) || pattern.test(href) || pattern.test(parentText)
          )

          // Only highlight if it matches unsubscribe patterns and isn't dangerous
          if (matchesPattern && !isDangerous && this.isLegitimateUnsubscribeLink(href, text, title, parentText)) {
            link.style.backgroundColor = '#f1c40f'
            link.style.border = '2px solid #e67e22'
            link.style.padding = '2px 4px'
            link.style.borderRadius = '3px'
            link.style.position = 'relative'

            // Add debug badge
            const badge = document.createElement('span')
            badge.textContent = '🔗'
            badge.style.cssText = `
              position: absolute;
              top: -8px;
              right: -8px;
              background: #e67e22;
              color: white;
              padding: 2px 4px;
              border-radius: 50%;
              font-size: 10px;
              z-index: 9999;
            `
            link.style.position = 'relative'
            link.appendChild(badge)

            // Add comprehensive tooltip
            link.title = `🔗 Unsubscribe Link\nText: "${text}"\nURL: ${href}`
            count++
          }
        })

        ExtensionLogger.info(ExtensionLogger.COMPONENTS.EXTRACTION, `Highlighted ${count} unsubscribe links`)
        return count
      },

      showEmailStructure: () => {
        const openEmail = document.querySelector('.ii.gt div[data-message-id]')
        if (!openEmail) {
          ExtensionLogger.warn(ExtensionLogger.COMPONENTS.GMAIL, 'No open email to analyze')
          return
        }

        // Create structure overlay
        const overlay = document.createElement('div')
        overlay.style.cssText = `
          position: fixed;
          top: 20px;
          right: 20px;
          width: 300px;
          max-height: 500px;
          background: white;
          border: 1px solid #ddd;
          border-radius: 8px;
          padding: 15px;
          box-shadow: 0 4px 12px rgba(0,0,0,0.15);
          z-index: 10000;
          overflow-y: auto;
          font-family: monospace;
          font-size: 12px;
        `

        const structure = this.analyzeEmailStructure(openEmail)
        overlay.innerHTML = `
          <h3>Email Structure</h3>
          <button onclick="this.parentElement.remove()" style="float: right;">×</button>
          <pre>${JSON.stringify(structure, null, 2)}</pre>
        `

        document.body.appendChild(overlay)
        ExtensionLogger.info(ExtensionLogger.COMPONENTS.GMAIL, 'Email structure overlay displayed')
      },

      clearVisualDebugger: () => {
        // Remove all debug styling from emails
        document.querySelectorAll('[data-message-id]').forEach(email => {
          email.style.border = ''
          email.style.boxShadow = ''
          const emailBadge = email.querySelector('div[style*="position: absolute"]')
          if (emailBadge) emailBadge.remove()
        })

        // Remove all debug styling from links (broader selector to catch all highlighted links)
        document.querySelectorAll('a[href]').forEach(link => {
          // Check if this link has our debug styling
          if (link.style.backgroundColor === 'rgb(241, 196, 15)' || link.style.backgroundColor === '#f1c40f') {
            link.style.backgroundColor = ''
            link.style.border = ''
            link.style.padding = ''
            link.style.borderRadius = ''
            link.style.position = ''

            // Remove debug badge
            const linkBadge = link.querySelector('span[style*="position: absolute"]')
            if (linkBadge) linkBadge.remove()

            // Remove debug tooltip if it's our format
            if (link.title && link.title.startsWith('🔗 Unsubscribe Link')) {
              link.removeAttribute('title')
            }
          }
        })

        // Remove overlays
        document.querySelectorAll('[style*="position: fixed"][style*="z-index: 10000"]').forEach(overlay => {
          overlay.remove()
        })

        ExtensionLogger.info(ExtensionLogger.COMPONENTS.GMAIL, 'Visual debugger cleared')
      }
    }
  }

  /**
   * Create extraction debugging utilities
   */
  createExtractionDebugger () {
    return {
      testUnsubscribeExtraction: (emailElement = null) => {
        const target = emailElement || document.querySelector('.ii.gt div[data-message-id]')
        if (!target) {
          ExtensionLogger.warn(ExtensionLogger.COMPONENTS.EXTRACTION, 'No email element for extraction test')
          return null
        }

        ExtensionLogger.info(ExtensionLogger.COMPONENTS.EXTRACTION, 'Testing unsubscribe extraction')

        const result = this.scanForUnsubscribeLinks(target)

        ExtensionLogger.info(ExtensionLogger.COMPONENTS.EXTRACTION, 'Extraction results', {
          found: result.found,
          linksCount: result.links.length,
          confidence: result.confidence
        })

        result.links.forEach((link, index) => {
          ExtensionLogger.debug(ExtensionLogger.COMPONENTS.EXTRACTION, `Link ${index + 1}`, {
            url: link.url,
            text: link.text,
            score: link.score,
            type: link.type
          })
        })

        return result
      },

      analyzeEmailPatterns: () => {
        const openEmail = document.querySelector('.ii.gt div[data-message-id]')
        if (!openEmail) {
          ExtensionLogger.warn(ExtensionLogger.COMPONENTS.EXTRACTION, 'No open email for pattern analysis')
          return null
        }

        const text = openEmail.textContent.toLowerCase()
        const patterns = {
          unsubscribe: [],
          dangerous: [],
          suspicious: []
        }

        // Test unsubscribe patterns
        this.unsubscribePatterns.forEach((pattern, index) => {
          if (pattern.test(text)) {
            patterns.unsubscribe.push({ index, pattern: pattern.toString() })
          }
        })

        // Test dangerous patterns
        this.dangerousPatterns.forEach((pattern, index) => {
          if (pattern.test(text)) {
            patterns.dangerous.push({ index, pattern: pattern.toString() })
          }
        })

        ExtensionLogger.info(ExtensionLogger.COMPONENTS.EXTRACTION, 'Pattern analysis complete', patterns)
        return patterns
      },

      validateUnsubscribeLinks: () => {
        const links = document.querySelectorAll('a[href*="unsubscribe"], a[href*="opt-out"], a[href*="remove"]')
        const results = []

        links.forEach((link, index) => {
          const validation = this.validateUnsubscribeLink(link.href, link.textContent)
          results.push({
            index,
            url: link.href,
            text: link.textContent,
            isValid: validation.isValid,
            isSafe: validation.isSafe,
            score: validation.score,
            issues: validation.issues
          })
        })

        ExtensionLogger.info(ExtensionLogger.COMPONENTS.EXTRACTION, 'Link validation complete', {
          total: results.length,
          valid: results.filter(r => r.isValid).length,
          safe: results.filter(r => r.isSafe).length
        })

        return results
      }
    }
  }

  /**
   * Add visual debugging indicators to Gmail interface
   */
  addVisualDebuggingIndicators () {
    if (!this.debugMode) return

    // Add debug panel to Gmail interface
    const debugPanel = document.createElement('div')
    debugPanel.id = 'gmail-debug-panel'
    debugPanel.style.cssText = `
      position: fixed;
      top: 10px;
      right: 10px;
      background: #2c3e50;
      color: white;
      padding: 10px;
      border-radius: 5px;
      font-family: monospace;
      font-size: 12px;
      z-index: 9999;
      box-shadow: 0 2px 10px rgba(0,0,0,0.2);
      max-width: 250px;
      transition: opacity 0.3s ease;
    `

    // Create panel structure without inline event handlers
    const title = document.createElement('div')
    title.style.cssText = 'margin-bottom: 8px; font-weight: bold;'
    title.textContent = '🔧 Debug Panel'
    debugPanel.appendChild(title)

    // Button style template
    const buttonStyle = 'margin: 2px; padding: 4px 8px; border: none; border-radius: 3px; cursor: pointer; background: #3498db; color: white; font-size: 11px;'

    // Create buttons with proper event listeners
    const buttons = [
      { text: 'Test Email Detection', action: () => this.safeDebugCall('GmailDebugger', 'testEmailDetection') },
      { text: 'Highlight Emails', action: () => this.safeDebugCall('VisualDebugger', 'highlightDetectedEmails') },
      { text: 'Highlight Links', action: () => this.safeDebugCall('VisualDebugger', 'highlightUnsubscribeLinks') },
      { text: 'Clear Debug', action: () => this.safeDebugCall('VisualDebugger', 'clearVisualDebugger') },
      { text: 'Open Dashboard', action: () => this.openDebugDashboard() }
    ]

    buttons.forEach(({ text, action }) => {
      const button = document.createElement('button')
      button.style.cssText = buttonStyle
      button.textContent = text
      button.addEventListener('click', action)
      debugPanel.appendChild(button)
      debugPanel.appendChild(document.createElement('br'))
    })

    // Add hover functionality to restore opacity
    debugPanel.addEventListener('mouseenter', () => {
      debugPanel.style.opacity = '1'
    })

    debugPanel.addEventListener('mouseleave', () => {
      if (debugPanel.classList.contains('faded')) {
        debugPanel.style.opacity = '0.3'
      }
    })

    document.body.appendChild(debugPanel)

    // Auto-hide after 10 seconds
    setTimeout(() => {
      debugPanel.style.opacity = '0.3'
      debugPanel.classList.add('faded')
    }, 10000)

    ExtensionLogger.info(ExtensionLogger.COMPONENTS.CONTENT, 'Visual debugging indicators added with proper event listeners')
  }

  /**
   * Safely call debug functions with error handling
   */
  safeDebugCall (debuggerName, methodName) {
    try {
      if (!window[debuggerName]) {
        ExtensionLogger.error(ExtensionLogger.COMPONENTS.CONTENT, `${debuggerName} not available`)
        this.showDebugFeedback(`❌ ${debuggerName} not initialized`, 'error')
        return
      }

      if (typeof window[debuggerName][methodName] !== 'function') {
        ExtensionLogger.error(ExtensionLogger.COMPONENTS.CONTENT, `Method ${methodName} not found on ${debuggerName}`)
        this.showDebugFeedback(`❌ Method ${methodName} not found`, 'error')
        return
      }

      const result = window[debuggerName][methodName]()
      ExtensionLogger.info(ExtensionLogger.COMPONENTS.CONTENT, `Debug call successful: ${debuggerName}.${methodName}`, result)
      this.showDebugFeedback(`✅ ${methodName} executed successfully`, 'success')
      return result
    } catch (error) {
      ExtensionLogger.error(ExtensionLogger.COMPONENTS.CONTENT, `Debug call failed: ${debuggerName}.${methodName}`, error)
      this.showDebugFeedback(`❌ ${methodName} failed: ${error.message}`, 'error')
    }
  }

  /**
   * Open debug dashboard with proper extension URL
   */
  openDebugDashboard () {
    try {
      if (chrome && chrome.runtime && chrome.runtime.getURL) {
        const dashboardUrl = chrome.runtime.getURL('debug.html')
        window.open(dashboardUrl, '_blank')
        ExtensionLogger.info(ExtensionLogger.COMPONENTS.CONTENT, 'Debug dashboard opened', { url: dashboardUrl })
        this.showDebugFeedback('✅ Dashboard opened', 'success')
      } else {
        ExtensionLogger.warn(ExtensionLogger.COMPONENTS.CONTENT, 'Chrome extension context not available for dashboard')
        this.showDebugFeedback('❌ Dashboard unavailable', 'error')
      }
    } catch (error) {
      ExtensionLogger.error(ExtensionLogger.COMPONENTS.CONTENT, 'Failed to open debug dashboard', error)
      this.showDebugFeedback(`❌ Dashboard error: ${error.message}`, 'error')
    }
  }

  /**
   * Show visual feedback for debug operations
   */
  showDebugFeedback (message, type = 'info') {
    const debugPanel = document.getElementById('gmail-debug-panel')
    if (!debugPanel) return

    const feedback = document.createElement('div')
    feedback.style.cssText = `
      margin-top: 5px;
      padding: 3px;
      border-radius: 2px;
      font-size: 10px;
      background: ${type === 'success' ? '#27ae60' : type === 'error' ? '#e74c3c' : '#3498db'};
      color: white;
    `
    feedback.textContent = message
    debugPanel.appendChild(feedback)

    // Remove feedback after 3 seconds
    setTimeout(() => {
      if (feedback.parentNode) {
        feedback.parentNode.removeChild(feedback)
      }
    }, 3000)
  }

  /**
   * Analyze email structure for debugging
   */
  analyzeEmailStructure (emailElement) {
    const structure = {
      messageId: emailElement.getAttribute('data-message-id'),
      tagName: emailElement.tagName,
      classes: emailElement.className,
      childCount: emailElement.children.length,
      textLength: emailElement.textContent.length,
      links: [],
      images: [],
      buttons: []
    }

    // Analyze links
    emailElement.querySelectorAll('a').forEach(link => {
      structure.links.push({
        href: link.href,
        text: link.textContent.substring(0, 50),
        target: link.target
      })
    })

    // Analyze images
    emailElement.querySelectorAll('img').forEach(img => {
      structure.images.push({
        src: img.src,
        alt: img.alt,
        width: img.width,
        height: img.height
      })
    })

    // Analyze buttons
    emailElement.querySelectorAll('button, input[type="button"], input[type="submit"]').forEach(button => {
      structure.buttons.push({
        type: button.type,
        text: button.textContent,
        value: button.value
      })
    })

    return structure
  }
}

// Cleanup function for when extension context is invalidated
function cleanupContentScript () {
  if (window.gmailUnsubscriber) {
    // Remove any event listeners or observers
    console.log('Cleaning up content script due to context invalidation')
    window.gmailUnsubscriber = null
  }
}

// Listen for context invalidation
if (chrome && chrome.runtime) {
  chrome.runtime.onConnect.addListener(() => {
    // This will throw an error if context is invalidated
    chrome.runtime.onDisconnect.addListener(cleanupContentScript)
  })
}

// Initialize the content script when DOM is ready
function initializeContentScript () {
  try {
    // Clean up any existing instance
    if (window.gmailUnsubscriber) {
      cleanupContentScript()
    }

    const unsubscriber = new GmailUnsubscriberContent()
    window.gmailUnsubscriber = unsubscriber
  } catch (error) {
    console.error('Error initializing content script:', error)
  }
}

if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initializeContentScript)
} else {
  initializeContentScript()
}
