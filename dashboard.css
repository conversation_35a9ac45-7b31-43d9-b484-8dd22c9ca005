/* Dashboard-specific styles */

/* Global styles for dashboard page */
body {
    margin: 0;
    padding: 0;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: #f8f9fa;
    overflow-x: hidden;
}

html {
    margin: 0;
    padding: 0;
}

.dashboard-container {
    width: 100%;
    max-width: 1200px;
    min-height: 100vh;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    background: white;
    box-shadow: none;
    overflow: hidden;
}

/* Responsive breakpoints */
@media (max-width: 1400px) {
    .dashboard-container {
        max-width: 95%;
        margin: 20px auto;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }
}

@media (max-width: 768px) {
    .dashboard-container {
        max-width: 100%;
        margin: 0;
        border-radius: 0;
        min-height: 100vh;
    }
}

.dashboard-header {
    background: linear-gradient(135deg, #4285f4 0%, #34a853 100%);
    color: white;
    padding: 24px 32px;
}

@media (max-width: 768px) {
    .dashboard-header {
        padding: 16px 20px;
    }
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.dashboard-title {
    font-size: 20px;
    font-weight: 600;
    margin: 0;
}

.back-btn {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.back-btn:hover {
    background: rgba(255, 255, 255, 0.3);
}

.dashboard-main {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 32px 40px;
    overflow: hidden;
}

@media (max-width: 768px) {
    .dashboard-main {
        padding: 20px 16px;
    }
}

.dashboard-controls {
    display: flex;
    flex-direction: column;
    gap: 20px;
    margin-bottom: 32px;
}

.control-group {
    display: flex;
    gap: 16px;
    align-items: center;
    flex-wrap: wrap;
}

@media (max-width: 768px) {
    .dashboard-controls {
        gap: 16px;
        margin-bottom: 24px;
    }
    
    .control-group {
        gap: 12px;
    }
}

.search-container {
    position: relative;
    flex: 1;
}

.search-input {
    width: 100%;
    padding: 8px 36px 8px 12px;
    border: 1px solid #dadce0;
    border-radius: 6px;
    font-size: 14px;
    outline: none;
    transition: border-color 0.2s ease;
}

.search-input:focus {
    border-color: #4285f4;
}

.search-icon {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #5f6368;
    pointer-events: none;
}

.sort-select,
.filter-select {
    padding: 8px 12px;
    border: 1px solid #dadce0;
    border-radius: 6px;
    font-size: 14px;
    background: white;
    color: #3c4043;
    cursor: pointer;
    outline: none;
}

.sort-select:focus,
.filter-select:focus {
    border-color: #4285f4;
}

.bulk-actions {
    display: flex;
    gap: 8px;
    align-items: center;
}

.bulk-btn {
    padding: 6px 12px;
    border: 1px solid #dadce0;
    border-radius: 4px;
    background: white;
    color: #3c4043;
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.bulk-btn:hover {
    background: #f8f9fa;
}

.bulk-btn.primary {
    background: #4285f4;
    color: white;
    border-color: #4285f4;
}

.bulk-btn.primary:hover {
    background: #3367d6;
}

.bulk-btn:disabled {
    background: #f8f9fa;
    color: #5f6368;
    cursor: not-allowed;
}

.bulk-btn:disabled:hover {
    background: #f8f9fa;
}

.dashboard-stats {
    display: flex;
    gap: 24px;
    margin-bottom: 32px;
}

.stat-card {
    flex: 1;
    background: #f8f9fa;
    border-radius: 12px;
    padding: 24px 20px;
    text-align: center;
    border: 1px solid #e8eaed;
    transition: all 0.2s ease;
}

.stat-card:hover {
    background: #ffffff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

@media (max-width: 768px) {
    .dashboard-stats {
        flex-direction: column;
        gap: 16px;
        margin-bottom: 24px;
    }
    
    .stat-card {
        padding: 20px 16px;
    }
}

.stat-value {
    font-size: 32px;
    font-weight: 700;
    color: #1f1f1f;
    margin-bottom: 8px;
    line-height: 1;
}

.stat-label {
    font-size: 14px;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.8px;
    font-weight: 600;
}

@media (max-width: 768px) {
    .stat-value {
        font-size: 28px;
        margin-bottom: 6px;
    }
    
    .stat-label {
        font-size: 12px;
        letter-spacing: 0.6px;
    }
}

.subscription-list {
    flex: 1;
    overflow-y: auto;
    border: 1px solid #e8eaed;
    border-radius: 8px;
    background: white;
}

.loading-message,
.empty-message {
    text-align: center;
    padding: 40px;
    color: #5f6368;
    font-size: 16px;
}

.subscription-item {
    display: flex;
    flex-direction: column;
    padding: 24px 32px;
    border-bottom: 1px solid #f1f3f4;
    transition: all 0.2s ease;
    position: relative;
}

@media (max-width: 768px) {
    .subscription-item {
        padding: 20px 16px;
    }
}

.subscription-item.risk-high {
    border-left: 4px solid #dc3545;
    background: #fff5f5;
}

.subscription-item.risk-medium {
    border-left: 4px solid #ffc107;
    background: #fffbf0;
}

.subscription-item.risk-low {
    border-left: 4px solid #28a745;
    background: #f8fff9;
}

.subscription-item.risk-unknown {
    border-left: 4px solid #6c757d;
    background: #f8f9fa;
}

.subscription-header {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    gap: 12px;
}

.risk-indicator {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
}

.risk-indicator.high {
    background: #f8d7da;
    color: #721c24;
}

.risk-indicator.medium {
    background: #fff3cd;
    color: #856404;
}

.risk-indicator.low {
    background: #d1f2dd;
    color: #0f5132;
}

.risk-indicator.unknown {
    background: #e2e3e5;
    color: #41464b;
}

.risk-icon {
    font-size: 12px;
}

.sender-info {
    flex: 1;
    min-width: 0;
}

.sender-name {
    font-size: 16px;
    font-weight: 600;
    color: #3c4043;
    margin: 0 0 4px 0;
}

.sender-email {
    font-size: 13px;
    color: #5f6368;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.subscription-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 8px;
    margin-bottom: 12px;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 12px;
}

.detail-label {
    color: #5f6368;
    font-weight: 500;
}

.detail-value {
    color: #3c4043;
    font-weight: 600;
}

.detail-value.frequency {
    color: #1a73e8;
}

.detail-value.trust-level {
    padding: 2px 6px;
    border-radius: 8px;
    background: #e8f0fe;
    color: #1a73e8;
    font-size: 11px;
}

.subscription-actions {
    display: flex;
    gap: 8px;
    align-items: center;
}

.action-unsubscribe, .action-details, .action-block {
    padding: 6px 12px;
    border: 1px solid #dadce0;
    border-radius: 4px;
    background: white;
    color: #3c4043;
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.action-unsubscribe.safe {
    background: #28a745;
    color: white;
    border-color: #28a745;
}

.action-unsubscribe.caution {
    background: #ffc107;
    color: #212529;
    border-color: #ffc107;
}

.action-unsubscribe.dangerous {
    background: #dc3545;
    color: white;
    border-color: #dc3545;
    cursor: not-allowed;
}

.action-unsubscribe.unknown {
    background: #6c757d;
    color: white;
    border-color: #6c757d;
    cursor: not-allowed;
}

.action-block {
    background: #6c757d;
    color: white;
    border-color: #6c757d;
}

.action-details:hover, .action-block:hover {
    background: #f8f9fa;
}

.action-unsubscribe.safe:hover {
    background: #218838;
}

.action-unsubscribe.caution:hover {
    background: #e0a800;
}

.security-details {
    margin-top: 12px;
    padding: 12px;
    background: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #e8eaed;
}

.security-details h5 {
    margin: 0 0 8px 0;
    font-size: 13px;
    font-weight: 600;
    color: #3c4043;
}

.security-findings {
    list-style: none;
    padding: 0;
    margin: 0;
}

.security-findings li {
    padding: 4px 0;
    font-size: 12px;
}

.finding-safe {
    color: #0f5132;
}

.finding-warning {
    color: #856404;
}

.finding-danger {
    color: #721c24;
}
.finding-info {
    color: #0a4f88;
    font-weight: 600;
}
.finding-link {
    color: #3367d6;
    font-size: 14px;
    margin-left: 16px;
}

.subscription-item:hover {
    background: #f8f9fa;
}

.subscription-item:last-child {
    border-bottom: none;
}

.subscription-checkbox {
    margin-right: 12px;
    width: 16px;
    height: 16px;
    cursor: pointer;
}

.subscription-info {
    flex: 1;
    min-width: 0;
}

.subscription-domain {
    font-size: 14px;
    font-weight: 500;
    color: #3c4043;
    margin-bottom: 2px;
}

.subscription-details {
    font-size: 12px;
    color: #5f6368;
    display: flex;
    gap: 12px;
    align-items: center;
}

.subscription-email {
    font-size: 12px;
    color: #5f6368;
    margin-bottom: 4px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.subscription-stats {
    display: flex;
    gap: 8px;
    align-items: center;
    margin-left: 12px;
}

.stat-badge {
    background: #e8f0fe;
    color: #1a73e8;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
}

.stat-badge.frequency {
    background: #fce8e6;
    color: #d93025;
}

.stat-badge.date {
    background: #e6f4ea;
    color: #137333;
}

.subscription-actions {
    display: flex;
    gap: 8px;
    margin-left: 12px;
}

.action-btn-small {
    padding: 4px 8px;
    border: 1px solid #dadce0;
    border-radius: 4px;
    background: white;
    color: #3c4043;
    font-size: 11px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.action-btn-small:hover {
    background: #f8f9fa;
}

.action-btn-small.unsubscribe {
    background: #ea4335;
    color: white;
    border-color: #ea4335;
}

.action-btn-small.unsubscribe:hover {
    background: #d93025;
}

/* Bulk Unsubscribe Modal */
.bulk-unsubscribe-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal-content {
    background: white;
    border-radius: 8px;
    padding: 24px;
    max-width: 400px;
    width: 90%;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
}

.modal-header h3 {
    margin: 0 0 16px 0;
    font-size: 18px;
    font-weight: 600;
    color: #3c4043;
}

.modal-body p {
    margin: 0 0 16px 0;
    font-size: 14px;
    color: #5f6368;
}

.modal-warning {
    background: #fef7e0;
    border: 1px solid #fdd663;
    border-radius: 6px;
    padding: 12px;
    margin: 16px 0;
}

.modal-warning p {
    margin: 0;
    font-size: 13px;
    color: #b06000;
}

.modal-progress {
    margin: 16px 0;
}

.progress-bar {
    width: 100%;
    height: 6px;
    background: #e8eaed;
    border-radius: 3px;
    overflow: hidden;
    margin-bottom: 8px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #4285f4 0%, #34a853 100%);
    width: 0%;
    transition: width 0.3s ease;
}

.progress-text {
    font-size: 12px;
    color: #5f6368;
    text-align: center;
}

.modal-actions {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
    margin-top: 20px;
}

.modal-btn {
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.cancel-btn {
    background: #f8f9fa;
    color: #3c4043;
    border: 1px solid #dadce0;
}

.cancel-btn:hover {
    background: #e8eaed;
}

.confirm-btn {
    background: #ea4335;
    color: white;
}

.confirm-btn:hover {
    background: #d93025;
}

/* Responsive adjustments */
@media (max-width: 650px) {
    .dashboard-container {
        width: 100%;
        height: 100%;
        border-radius: 0;
    }
    
    .control-group {
        flex-direction: column;
        align-items: stretch;
    }
    
    .dashboard-stats {
        flex-direction: column;
        gap: 12px;
    }
    
    .subscription-details {
        flex-direction: column;
        gap: 4px;
        align-items: flex-start;
    }
    
    .subscription-stats {
        margin-left: 0;
        margin-top: 8px;
    }
}