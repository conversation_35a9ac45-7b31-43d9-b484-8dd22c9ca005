<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Subscription Dashboard - Email Unsubscriber</title>
    <link rel="stylesheet" href="popup.css">
    <link rel="stylesheet" href="dashboard.css">
</head>
<body>
    <div class="dashboard-container">
        <header class="dashboard-header">
            <div class="header-content">
                <h1 class="dashboard-title">Subscription Dashboard</h1>
                <button class="back-btn" id="backBtn">← Back</button>
            </div>
        </header>
        
        <div class="dashboard-main">
            <div class="dashboard-controls">
                <div class="control-group">
                    <div class="search-container">
                        <input type="text" id="searchInput" placeholder="Search subscriptions..." class="search-input">
                        <span class="search-icon">🔍</span>
                    </div>
                </div>
                
                <div class="control-group">
                    <select id="sortSelect" class="sort-select">
                        <option value="domain">Sort by Domain</option>
                        <option value="frequency">Sort by Frequency</option>
                        <option value="date">Sort by Date</option>
                        <option value="name">Sort by Name</option>
                        <option value="risk">Sort by Risk Level</option>
                        <option value="category">Sort by Category</option>
                    </select>
                    
                    <select id="filterSelect" class="filter-select">
                        <option value="all">All Subscriptions</option>
                        <option value="frequent">Frequent Senders</option>
                        <option value="infrequent">Infrequent Senders</option>
                        <option value="recent">Recent</option>
                        <option value="old">Old</option>
                    </select>
                    
                    <select id="riskFilterSelect" class="filter-select">
                        <option value="all">All Risk Levels</option>
                        <option value="low">Safe Only</option>
                        <option value="medium">Needs Review</option>
                        <option value="high">High Risk</option>
                        <option value="unknown">Unknown Risk</option>
                    </select>
                    
                    <select id="categoryFilterSelect" class="filter-select">
                        <option value="all">All Categories</option>
                        <option value="newsletter">Newsletters</option>
                        <option value="promotional">Promotional</option>
                        <option value="transactional">Transactional</option>
                        <option value="unknown">Uncategorized</option>
                    </select>
                </div>
                
                <div class="control-group">
                    <div class="bulk-actions">
                        <button class="bulk-btn" id="selectAllBtn">Select All</button>
                        <button class="bulk-btn" id="deselectAllBtn">Deselect All</button>
                        <button class="bulk-btn primary" id="bulkUnsubscribeBtn" disabled>
                            Unsubscribe Selected (<span id="selectedCount">0</span>)
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="dashboard-stats">
                <div class="stat-card">
                    <div class="stat-value" id="totalSubscriptions">0</div>
                    <div class="stat-label">Total Subscriptions</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="totalEmails">0</div>
                    <div class="stat-label">Total Emails</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="selectedSubscriptions">0</div>
                    <div class="stat-label">Selected</div>
                </div>
            </div>
            
            <div class="subscription-list" id="subscriptionList">
                <div class="loading-message" id="loadingMessage">
                    Loading subscriptions...
                </div>
                <div class="empty-message" id="emptyMessage" style="display: none;">
                    <h3>No Subscriptions Found</h3>
                    <p>To get started:</p>
                    <ol>
                        <li>Open Gmail in another tab</li>
                        <li>Go to your inbox</li>
                        <li>Open some emails with unsubscribe links</li>
                        <li>The extension will automatically detect and collect subscription data</li>
                        <li>Refresh this dashboard to see your subscriptions</li>
                    </ol>
                    <p><strong>Tip:</strong> The extension works by analyzing emails you open in Gmail.</p>
                </div>
            </div>
        </div>
        
        <div class="bulk-unsubscribe-modal" id="bulkUnsubscribeModal" style="display: none;">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>Bulk Unsubscribe Confirmation</h3>
                </div>
                <div class="modal-body">
                    <p>Are you sure you want to unsubscribe from <span id="modalSelectedCount">0</span> subscriptions?</p>
                    <div class="modal-warning">
                        <p>⚠️ This action cannot be undone. The unsubscribe process will begin immediately.</p>
                    </div>
                    <div class="modal-progress" id="modalProgress" style="display: none;">
                        <div class="progress-bar">
                            <div class="progress-fill" id="modalProgressFill"></div>
                        </div>
                        <div class="progress-text" id="modalProgressText">Processing...</div>
                    </div>
                </div>
                <div class="modal-actions">
                    <button class="modal-btn cancel-btn" id="cancelBulkUnsubscribe">Cancel</button>
                    <button class="modal-btn confirm-btn" id="confirmBulkUnsubscribe">Proceed</button>
                </div>
            </div>
        </div>
    </div>
    
    <script src="dashboard.js"></script>
</body>
</html>