class SubscriptionDashboard {
  constructor () {
    this.subscriptions = []
    this.filteredSubscriptions = []
    this.selectedSubscriptions = new Set()
    this.currentSort = 'domain'
    this.currentFilter = 'all'
    this.currentRiskFilter = 'all'
    this.currentCategoryFilter = 'all'
    this.searchTerm = ''
    this.bulkUnsubscribeInProgress = false
    this.isDebugMode = true // Enable debug logging

    this.init()
  }

  async init () {
    try {
      this.log('Initializing dashboard...')
      await this.loadSubscriptions()
      this.setupEventListeners()
      this.renderSubscriptions()
      this.updateStats()
      this.log('Dashboard initialized successfully')
    } catch (error) {
      this.logError('Failed to initialize dashboard:', error)
      this.showError('Failed to load dashboard. Please refresh the page.')
    }
  }

  log (message, data = null) {
    if (this.isDebugMode) {
      console.log(`[Dashboard] ${message}`, data || '')
    }
  }

  logError (message, error = null) {
    console.error(`[Dashboard] ${message}`, error || '')
  }

  showError (message) {
    // Create or update error display
    let errorDiv = document.getElementById('dashboardError')
    if (!errorDiv) {
      errorDiv = document.createElement('div')
      errorDiv.id = 'dashboardError'
      errorDiv.className = 'error-message'
      errorDiv.style.cssText = 'background: #f8d7da; color: #721c24; padding: 12px; margin: 16px; border-radius: 6px; border: 1px solid #f5c6cb;'
      const container = document.querySelector('.dashboard-main')
      if (container) {
        container.insertBefore(errorDiv, container.firstChild)
      }
    }
    errorDiv.textContent = message
    errorDiv.style.display = 'block'
  }

  hideError () {
    const errorDiv = document.getElementById('dashboardError')
    if (errorDiv) {
      errorDiv.style.display = 'none'
    }
  }

  setupEventListeners () {
    this.log('Setting up event listeners...')

    // Back button
    const backBtn = document.getElementById('backBtn')
    if (backBtn) {
      backBtn.addEventListener('click', () => this.goBack())
    }

    // Search input
    const searchInput = document.getElementById('searchInput')
    if (searchInput) {
      searchInput.addEventListener('input', (e) => {
        this.searchTerm = e.target.value.toLowerCase()
        this.log('Search term changed:', this.searchTerm)
        this.applyFilters()
      })
    }

    // Filter selects
    this.setupFilterSelect('sortSelect', 'currentSort')
    this.setupFilterSelect('filterSelect', 'currentFilter')
    this.setupFilterSelect('riskFilterSelect', 'currentRiskFilter')
    this.setupFilterSelect('categoryFilterSelect', 'currentCategoryFilter')

    // Bulk action buttons
    const selectAllBtn = document.getElementById('selectAllBtn')
    if (selectAllBtn) {
      selectAllBtn.addEventListener('click', () => this.selectAll())
    }

    const deselectAllBtn = document.getElementById('deselectAllBtn')
    if (deselectAllBtn) {
      deselectAllBtn.addEventListener('click', () => this.deselectAll())
    }

    const bulkUnsubscribeBtn = document.getElementById('bulkUnsubscribeBtn')
    if (bulkUnsubscribeBtn) {
      bulkUnsubscribeBtn.addEventListener('click', () => this.showBulkUnsubscribeModal())
    }

    // Modal buttons
    const cancelBtn = document.getElementById('cancelBulkUnsubscribe')
    if (cancelBtn) {
      cancelBtn.addEventListener('click', () => this.hideBulkUnsubscribeModal())
    }

    const confirmBtn = document.getElementById('confirmBulkUnsubscribe')
    if (confirmBtn) {
      confirmBtn.addEventListener('click', () => this.performBulkUnsubscribe())
    }

    // Event delegation for subscription list
    const subscriptionList = document.getElementById('subscriptionList')
    if (subscriptionList) {
      // Handle button clicks
      subscriptionList.addEventListener('click', (event) => {
        this.handleSubscriptionClick(event)
      })

      // Handle checkbox changes
      subscriptionList.addEventListener('change', (event) => {
        this.handleCheckboxChange(event)
      })
    }

    this.log('Event listeners set up successfully')
  }

  setupFilterSelect (elementId, propertyName) {
    const element = document.getElementById(elementId)
    if (element) {
      element.addEventListener('change', (e) => {
        this[propertyName] = e.target.value
        this.log(`Filter changed - ${propertyName}:`, this[propertyName])
        this.applyFilters()
      })
    } else {
      this.logError(`Filter element not found: ${elementId}`)
    }
  }

  handleSubscriptionClick (event) {
    const button = event.target.closest('[data-action]')
    if (!button) return

    const action = button.dataset.action
    const senderId = button.dataset.senderId

    if (!senderId) {
      this.logError('No senderId found for action:', action)
      return
    }

    this.log(`Button clicked - Action: ${action}, SenderId: ${senderId}`)

    switch (action) {
      case 'unsubscribe':
        this.unsubscribeSingle(senderId)
        break
      case 'details':
        this.viewDetails(senderId)
        break
      case 'block':
        this.blockSender(senderId)
        break
      default:
        this.logError('Unknown action:', action)
    }
  }

  handleCheckboxChange (event) {
    if (!event.target.classList.contains('subscription-checkbox')) return

    const subscriptionItem = event.target.closest('.subscription-item')
    if (!subscriptionItem) {
      this.logError('No subscription item found for checkbox')
      return
    }

    const senderId = subscriptionItem.dataset.senderId || subscriptionItem.dataset.domain
    if (!senderId) {
      this.logError('No senderId found for checkbox')
      return
    }

    this.log('Checkbox changed for:', senderId)
    this.toggleSelection(senderId)
  }

  async loadSubscriptions () {
    this.log('Loading subscriptions...')
    try {
      // Check if Chrome storage API is available
      if (typeof chrome !== 'undefined' && chrome.storage && chrome.storage.local) {
        // Try both keys to ensure compatibility
        const result = await chrome.storage.local.get(['subscriptions', 'subscriptionDatabase'])
        this.subscriptions = result.subscriptions || result.subscriptionDatabase || []
        this.log('Loaded subscriptions from Chrome storage:', this.subscriptions.length)
        this.log('Storage keys checked:', Object.keys(result))
      } else {
        this.log('Chrome storage API not available, checking localStorage fallback...')
        const storedData = localStorage.getItem('subscriptions') || localStorage.getItem('subscriptionDatabase')
        this.subscriptions = storedData ? JSON.parse(storedData) : []
        this.log('Loaded subscriptions from localStorage:', this.subscriptions.length)
      }

      // Log the loaded data for debugging
      this.log('Raw subscriptions loaded:', this.subscriptions.length)
      if (this.subscriptions.length > 0) {
        this.log('First subscription sample:', this.subscriptions[0])
      }

      // No sample data - only show real extracted data
      if (this.subscriptions.length === 0) {
        this.log('No subscriptions found. Please run email extraction first.')
      }

      // Validate and standardize data
      this.subscriptions = this.subscriptions.map(sub => this.validateSubscription(sub)).filter(Boolean)

      this.filteredSubscriptions = [...this.subscriptions]
      this.log('Subscriptions loaded successfully:', this.subscriptions.length)
    } catch (error) {
      this.logError('Error loading subscriptions:', error)
      this.subscriptions = []
      this.filteredSubscriptions = []
      this.showError('Failed to load subscription data. Please check if email extraction has been run.')
    }
  }

  async saveSubscriptions () {
    try {
      if (typeof chrome !== 'undefined' && chrome.storage && chrome.storage.local) {
        // Save to both keys for compatibility
        await chrome.storage.local.set({ 
          subscriptions: this.subscriptions,
          subscriptionDatabase: this.subscriptions 
        })
        this.log('Subscriptions saved to Chrome storage')
      } else {
        localStorage.setItem('subscriptions', JSON.stringify(this.subscriptions))
        localStorage.setItem('subscriptionDatabase', JSON.stringify(this.subscriptions))
        this.log('Subscriptions saved to localStorage')
      }
    } catch (error) {
      this.logError('Error saving subscriptions:', error)
    }
  }

  validateSubscription (subscription) {
    try {
      // Ensure required fields exist
      const senderId = subscription.senderId || subscription.domain || subscription.senderEmail
      if (!senderId) {
        this.logError('Invalid subscription - no identifier:', subscription)
        return null
      }

      // Convert single unsubscribe URL to links array format
      let unsubscribeLinks = []
      if (Array.isArray(subscription.unsubscribeLinks)) {
        unsubscribeLinks = subscription.unsubscribeLinks
      } else if (subscription.unsubscribeUrl) {
        // Convert individual URL to links array format
        unsubscribeLinks = [{
          href: subscription.unsubscribeUrl,
          text: subscription.unsubscribeText || 'Unsubscribe',
          type: subscription.unsubscribeType || 'http',
          trustLevel: subscription.trustLevel || 0,
          securityStatus: subscription.securityStatus || 'unknown'
        }]
      }

      // Standardize the subscription object
      return {
        senderId,
        domain: subscription.domain || subscription.senderDomain || this.extractDomainFromEmail(subscription.senderEmail) || senderId,
        senderEmail: subscription.senderEmail || senderId,
        senderName: subscription.senderName || this.extractNameFromEmail(subscription.senderEmail) || subscription.domain || senderId,
        frequency: subscription.frequency || 'unknown',
        lastEmailDate: subscription.lastEmailDate || new Date().toISOString(),
        emailCount: parseInt(subscription.emailCount) || 1,
        category: subscription.category || 'unknown',
        trustLevel: parseInt(subscription.trustLevel) || 0,
        securityStatus: subscription.securityStatus || 'unknown',
        unsubscribeLinks,
        securityWarnings: Array.isArray(subscription.securityWarnings) ? subscription.securityWarnings : [],
        phishingCheck: subscription.phishingCheck || null,
        isDangerous: Boolean(subscription.isDangerous)
      }
    } catch (error) {
      this.logError('Error validating subscription:', error, subscription)
      return null
    }
  }

  extractDomainFromEmail (email) {
    if (typeof email === 'string' && email.includes('@')) {
      return email.split('@')[1].toLowerCase()
    }
    return null
  }

  extractNameFromEmail (email) {
    if (typeof email === 'string' && email.includes('@')) {
      const localPart = email.split('@')[0]
      // Convert common patterns like firstname.lastname to readable names
      return localPart.replace(/[._-]/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
    }
    return null
  }

  applyFilters () {
    this.log('Applying filters...')
    let filtered = [...this.subscriptions]

    // Apply search filter
    if (this.searchTerm) {
      filtered = filtered.filter(sub =>
        (sub.domain || '').toLowerCase().includes(this.searchTerm) ||
        (sub.senderEmail || '').toLowerCase().includes(this.searchTerm) ||
        (sub.senderName || '').toLowerCase().includes(this.searchTerm)
      )
    }

    // Apply frequency filter
    switch (this.currentFilter) {
      case 'frequent':
        filtered = filtered.filter(sub => (sub.emailCount || 0) >= 5)
        break
      case 'infrequent':
        filtered = filtered.filter(sub => (sub.emailCount || 0) < 5)
        break
      case 'recent':
        const oneWeekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
        filtered = filtered.filter(sub => new Date(sub.lastEmailDate || 0) > oneWeekAgo)
        break
      case 'old':
        const oneMonthAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
        filtered = filtered.filter(sub => new Date(sub.lastEmailDate || 0) < oneMonthAgo)
        break
    }

    // Apply risk level filter
    if (this.currentRiskFilter !== 'all') {
      filtered = filtered.filter(sub => {
        const riskLevel = this.determineRiskLevel(sub.securityStatus, sub.trustLevel)
        return riskLevel === this.currentRiskFilter
      })
    }

    // Apply category filter
    if (this.currentCategoryFilter !== 'all') {
      filtered = filtered.filter(sub => {
        const category = sub.category || 'unknown'
        return category === this.currentCategoryFilter
      })
    }

    // Apply sort
    switch (this.currentSort) {
      case 'domain':
        filtered.sort((a, b) => (a.domain || '').localeCompare(b.domain || ''))
        break
      case 'frequency':
        filtered.sort((a, b) => (b.emailCount || 0) - (a.emailCount || 0))
        break
      case 'date':
        filtered.sort((a, b) => new Date(b.lastEmailDate || 0) - new Date(a.lastEmailDate || 0))
        break
      case 'name':
        filtered.sort((a, b) => (a.senderName || '').localeCompare(b.senderName || ''))
        break
      case 'risk':
        filtered.sort((a, b) => {
          const riskOrder = { high: 4, medium: 3, unknown: 2, low: 1 }
          const aRisk = this.determineRiskLevel(a.securityStatus, a.trustLevel)
          const bRisk = this.determineRiskLevel(b.securityStatus, b.trustLevel)
          return (riskOrder[bRisk] || 0) - (riskOrder[aRisk] || 0)
        })
        break
      case 'category':
        filtered.sort((a, b) => (a.category || 'unknown').localeCompare(b.category || 'unknown'))
        break
    }

    this.filteredSubscriptions = filtered
    this.log('Filters applied, results:', filtered.length)
    this.renderSubscriptions()
    this.updateStats()
  }

  renderSubscriptions () {
    this.log('Rendering subscriptions...')
    const listContainer = document.getElementById('subscriptionList')
    const loadingMessage = document.getElementById('loadingMessage')
    const emptyMessage = document.getElementById('emptyMessage')

    if (!listContainer) {
      this.logError('Subscription list container not found')
      return
    }

    // Hide loading message
    if (loadingMessage) {
      loadingMessage.style.display = 'none'
    }

    if (this.filteredSubscriptions.length === 0) {
      if (emptyMessage) {
        emptyMessage.style.display = 'block'
      }
      listContainer.innerHTML = ''
      if (emptyMessage) {
        listContainer.appendChild(emptyMessage)
      }
      return
    }

    if (emptyMessage) {
      emptyMessage.style.display = 'none'
    }
    listContainer.innerHTML = ''

    this.filteredSubscriptions.forEach(subscription => {
      try {
        const item = this.createSubscriptionItem(subscription)
        listContainer.appendChild(item)
      } catch (error) {
        this.logError('Error creating subscription item:', error, subscription)
      }
    })

    this.log('Subscriptions rendered successfully')
  }

  createSubscriptionItem (subscription) {
    const item = document.createElement('div')

    // Determine risk level and appropriate classes
    const securityStatus = subscription.securityStatus || 'unknown'
    const riskLevel = this.determineRiskLevel(securityStatus, subscription.trustLevel)
    const riskClass = `risk-${riskLevel}`

    item.className = `subscription-item ${riskClass}`
    item.dataset.domain = subscription.domain
    item.dataset.senderId = subscription.senderId
    item.dataset.riskLevel = riskLevel
    item.dataset.category = subscription.category || 'unknown'

    const isSelected = this.selectedSubscriptions.has(subscription.senderId)
    const lastEmailDate = new Date(subscription.lastEmailDate)
    const formattedDate = lastEmailDate.toLocaleDateString()
    const trustLevel = subscription.trustLevel || 0

    // Risk indicator configuration
    const riskConfig = this.getRiskConfig(riskLevel, securityStatus)

    // Action button configuration based on security
    const actionConfig = this.getActionConfig(riskLevel, securityStatus, subscription)

    item.innerHTML = `
      <div class="subscription-header">
        <input type="checkbox" class="item-select subscription-checkbox" 
               ${isSelected ? 'checked' : ''}>
        
        <div class="risk-indicator ${riskLevel}">
          <span class="risk-icon">${riskConfig.icon}</span>
          <span class="risk-text">${riskConfig.label}</span>
        </div>
        
        <div class="sender-info">
          <h4 class="sender-name">${this.escapeHtml(subscription.senderName)}</h4>
          <span class="sender-email">${this.escapeHtml(subscription.senderEmail)}</span>
        </div>
      </div>
      
      <div class="subscription-details">
        <div class="detail-item">
          <span class="detail-label">Frequency:</span>
          <span class="detail-value frequency">${this.escapeHtml(subscription.frequency)}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">Last Email:</span>
          <span class="detail-value last-email">${formattedDate}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">Total Emails:</span>
          <span class="detail-value total-count">${subscription.emailCount}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">Category:</span>
          <span class="detail-value category">${this.escapeHtml(subscription.category)}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">Trust Level:</span>
          <span class="detail-value trust-level">${trustLevel}%</span>
        </div>
      </div>
      
      <div class="subscription-actions">
        <button class="action-unsubscribe ${actionConfig.safetyClass}" 
                data-action="unsubscribe" 
                data-sender-id="${this.escapeHtml(subscription.senderId)}"
                ${actionConfig.disabled ? 'disabled' : ''}>
          ${this.escapeHtml(actionConfig.label)}
        </button>
        <button class="action-details" 
                data-action="details" 
                data-sender-id="${this.escapeHtml(subscription.senderId)}">
          Details
        </button>
        <button class="action-block" 
                data-action="block" 
                data-sender-id="${this.escapeHtml(subscription.senderId)}">
          Block
        </button>
      </div>
      
      <!-- Security details (expandable) -->
      <div class="security-details" style="display: none;" id="security-${this.escapeHtml(subscription.senderId)}">
        <h5>Security Analysis</h5>
        <ul class="security-findings">
          ${this.generateSecurityFindings(subscription)}
        </ul>
      </div>
    `

    return item
  }

  escapeHtml (text) {
    if (typeof text !== 'string') return ''
    const div = document.createElement('div')
    div.textContent = text
    return div.innerHTML
  }

  determineRiskLevel (securityStatus, trustLevel) {
    // Standardized risk assessment logic
    const trust = parseInt(trustLevel) || 0

    if (securityStatus === 'dangerous' || trust < 30) {
      return 'high'
    } else if (securityStatus === 'caution' || trust < 70) {
      return 'medium'
    } else if (securityStatus === 'safe' && trust >= 80) {
      return 'low'
    }
    return 'unknown'
  }

  getRiskConfig (riskLevel, securityStatus) {
    const configs = {
      high: { icon: '⚠️', label: 'High Risk', color: '#dc3545' },
      medium: { icon: '⚡', label: 'Needs Review', color: '#ffc107' },
      low: { icon: '✅', label: 'Safe', color: '#28a745' },
      unknown: { icon: '❓', label: 'Unknown', color: '#6c757d' }
    }
    return configs[riskLevel] || configs.unknown
  }

  getActionConfig (riskLevel, securityStatus, subscription) {
    // Get the best unsubscribe link to show what action will be taken
    const unsubscribeLinks = subscription.unsubscribeLinks || []
    let linkText = 'Unsubscribe'
    let linkInfo = ''

    if (unsubscribeLinks.length > 0) {
      const sortedLinks = this.getSortedUnsubscribeLinks(unsubscribeLinks)
      const bestLink = sortedLinks.find(link => link.href && link.href.startsWith('http'))

      if (bestLink) {
        linkText = bestLink.text || 'Unsubscribe'
        linkInfo = ` (${bestLink.trustLevel || 0}% trust)`
      }
    }

    const configs = {
      high: {
        label: 'Requires Review',
        safetyClass: 'dangerous',
        disabled: true,
        linkText,
        linkInfo
      },
      medium: {
        label: `${linkText}${linkInfo}`,
        safetyClass: 'caution',
        disabled: false,
        linkText,
        linkInfo
      },
      low: {
        label: `${linkText}${linkInfo}`,
        safetyClass: 'safe',
        disabled: false,
        linkText,
        linkInfo
      },
      unknown: {
        label: 'Analyze First',
        safetyClass: 'unknown',
        disabled: true,
        linkText,
        linkInfo
      }
    }
    return configs[riskLevel] || configs.unknown
  }

  generateSecurityFindings (subscription) {
    const findings = []
    const riskLevel = this.determineRiskLevel(subscription.securityStatus, subscription.trustLevel)

    // Add findings based on actual security issues
    if (subscription.securityWarnings && subscription.securityWarnings.length > 0) {
      subscription.securityWarnings.forEach(warning => {
        findings.push(`<li class="finding-warning">${this.escapeHtml(warning)}</li>`)
      })
    }

    if (subscription.phishingCheck && subscription.phishingCheck.isPhishing) {
      findings.push('<li class="finding-danger">Potential phishing patterns detected</li>')
    }

    // Only show trust level warning if it's actually problematic
    if (subscription.trustLevel < 50 && riskLevel !== 'low') {
      findings.push(`<li class="finding-warning">Low trust level (${subscription.trustLevel}%)</li>`)
    }

    // Only show dangerous flag if it actually makes the subscription dangerous
    if (subscription.isDangerous && riskLevel === 'high') {
      findings.push('<li class="finding-danger">Flagged as dangerous</li>')
    }

    // Add security status specific findings
    if (subscription.securityStatus === 'dangerous') {
      findings.push('<li class="finding-danger">Security analysis marked as dangerous</li>')
    } else if (subscription.securityStatus === 'caution') {
      findings.push('<li class="finding-warning">Security analysis requires caution</li>')
    }

    // Add unsubscribe link information
    const unsubscribeLinks = subscription.unsubscribeLinks || []
    if (unsubscribeLinks.length > 0) {
      findings.push('<li class="finding-info"><strong>Available Unsubscribe Options:</strong></li>')
      const sortedLinks = this.getSortedUnsubscribeLinks(unsubscribeLinks)
      sortedLinks.forEach((link, index) => {
        const trustInfo = link.trustLevel ? ` (${link.trustLevel}% trust)` : ''
        const linkText = link.text || 'Unsubscribe'
        const priority = index === 0 ? ' <strong>[PRIMARY]</strong>' : ''
        findings.push(`<li class="finding-link">• ${this.escapeHtml(linkText)}${trustInfo}${priority}</li>`)
      })
    }

    // If no actual security issues found and risk level is low, show safe message
    if (findings.length === 0 && riskLevel === 'low') {
      findings.push('<li class="finding-safe">No security issues detected</li>')
    } else if (findings.length === 0) {
      findings.push('<li class="finding-warning">Security analysis pending</li>')
    }

    return findings.join('')
  }

  async blockSender (senderId) {
    this.log('Blocking sender:', senderId)

    const subscription = this.subscriptions.find(sub => sub.senderId === senderId)
    const senderName = subscription ? subscription.senderName : senderId

    if (!confirm(`Are you sure you want to block all emails from ${senderName}? This will add them to your local blacklist.`)) {
      return
    }

    try {
      // Add to blacklist using storage wrapper
      let blacklist = []
      if (typeof chrome !== 'undefined' && chrome.storage && chrome.storage.local) {
        const result = await chrome.storage.local.get(['blacklistedSenders'])
        blacklist = result.blacklistedSenders || []
      } else {
        const storedData = localStorage.getItem('blacklistedSenders')
        blacklist = storedData ? JSON.parse(storedData) : []
      }

      if (!blacklist.includes(senderId)) {
        blacklist.push(senderId)

        // Save blacklist
        if (typeof chrome !== 'undefined' && chrome.storage && chrome.storage.local) {
          await chrome.storage.local.set({ blacklistedSenders: blacklist })
        } else {
          localStorage.setItem('blacklistedSenders', JSON.stringify(blacklist))
        }

        // Remove from subscriptions
        this.subscriptions = this.subscriptions.filter(sub => sub.senderId !== senderId)

        // Update database
        await this.saveSubscriptions()

        // Refresh dashboard
        this.applyFilters()

        alert(`Successfully blocked ${senderName}. Future emails from this sender will be filtered.`)
        this.log(`Blocked sender successfully: ${senderId}`)
      } else {
        alert(`${senderName} is already blocked.`)
      }
    } catch (error) {
      this.logError('Error blocking sender:', error)
      alert('Failed to block sender. Please try again.')
    }
  }

  toggleSelection (senderId) {
    this.log('Toggling selection for:', senderId)

    if (this.selectedSubscriptions.has(senderId)) {
      this.selectedSubscriptions.delete(senderId)
      this.log('Removed from selection')
    } else {
      this.selectedSubscriptions.add(senderId)
      this.log('Added to selection')
    }

    this.log('Selected subscriptions:', Array.from(this.selectedSubscriptions))
    this.updateSelectionUI()
  }

  selectAll () {
    this.selectedSubscriptions.clear()
    this.filteredSubscriptions.forEach(sub => {
      this.selectedSubscriptions.add(sub.senderId)
    })
    this.updateSelectionUI()
    this.renderSubscriptions()
  }

  deselectAll () {
    this.selectedSubscriptions.clear()
    this.updateSelectionUI()
    this.renderSubscriptions()
  }

  updateSelectionUI () {
    const selectedCount = this.selectedSubscriptions.size
    const bulkUnsubscribeBtn = document.getElementById('bulkUnsubscribeBtn')
    const selectedCountSpan = document.getElementById('selectedCount')

    this.log('Updating selection UI, count:', selectedCount)

    // Update the count display
    if (selectedCountSpan) {
      selectedCountSpan.textContent = selectedCount
    } else {
      this.logError('Selected count span not found')
    }

    // Update button state
    if (bulkUnsubscribeBtn) {
      bulkUnsubscribeBtn.disabled = selectedCount === 0
    } else {
      this.logError('Bulk unsubscribe button not found')
    }

    // Update all checkboxes to reflect current selection state
    this.updateCheckboxStates()
    this.updateStats()
  }

  updateCheckboxStates () {
    const checkboxes = document.querySelectorAll('.subscription-checkbox')
    this.log('Updating checkbox states for', checkboxes.length, 'checkboxes')

    checkboxes.forEach(checkbox => {
      const subscriptionItem = checkbox.closest('.subscription-item')
      if (subscriptionItem) {
        const senderId = subscriptionItem.dataset.senderId || subscriptionItem.dataset.domain
        const isSelected = this.selectedSubscriptions.has(senderId)
        checkbox.checked = isSelected
      }
    })
  }

  updateStats () {
    const totalSubscriptions = this.subscriptions.length
    const totalEmails = this.subscriptions.reduce((sum, sub) => sum + (sub.emailCount || 0), 0)
    const selectedSubscriptions = this.selectedSubscriptions.size

    const elements = {
      totalSubscriptions: document.getElementById('totalSubscriptions'),
      totalEmails: document.getElementById('totalEmails'),
      selectedSubscriptions: document.getElementById('selectedSubscriptions')
    }

    if (elements.totalSubscriptions) {
      elements.totalSubscriptions.textContent = totalSubscriptions
    }
    if (elements.totalEmails) {
      elements.totalEmails.textContent = totalEmails
    }
    if (elements.selectedSubscriptions) {
      elements.selectedSubscriptions.textContent = selectedSubscriptions
    }
  }

  showBulkUnsubscribeModal () {
    const modal = document.getElementById('bulkUnsubscribeModal')
    const selectedCount = document.getElementById('modalSelectedCount')

    if (selectedCount) {
      selectedCount.textContent = this.selectedSubscriptions.size
    }
    if (modal) {
      modal.style.display = 'flex'
    }
  }

  hideBulkUnsubscribeModal () {
    const modal = document.getElementById('bulkUnsubscribeModal')
    if (modal) {
      modal.style.display = 'none'
    }
  }

  async performBulkUnsubscribe () {
    if (this.bulkUnsubscribeInProgress) return

    this.bulkUnsubscribeInProgress = true
    const selectedSenderIds = Array.from(this.selectedSubscriptions)
    const modalProgress = document.getElementById('modalProgress')
    const modalProgressFill = document.getElementById('modalProgressFill')
    const modalProgressText = document.getElementById('modalProgressText')
    const confirmBtn = document.getElementById('confirmBulkUnsubscribe')

    // Show progress
    if (modalProgress) modalProgress.style.display = 'block'
    if (confirmBtn) confirmBtn.disabled = true

    try {
      let completed = 0
      const total = selectedSenderIds.length
      let successCount = 0
      let failedCount = 0

      for (const senderId of selectedSenderIds) {
        const subscription = this.subscriptions.find(sub => sub.senderId === senderId)
        if (subscription) {
          // Update progress
          const progress = ((completed + 1) / total) * 100
          if (modalProgressFill) modalProgressFill.style.width = `${progress}%`
          if (modalProgressText) {
            modalProgressText.textContent = `Unsubscribing from ${subscription.senderName}... (${completed + 1}/${total})`
          }

          // Attempt to unsubscribe using comprehensive approach
          const success = await this.comprehensiveUnsubscribe(subscription)
          if (success) {
            successCount++
          } else {
            failedCount++
          }
          completed++

          // Small delay to show progress and prevent overwhelming the browser
          await new Promise(resolve => setTimeout(resolve, 1000))
        } else {
          completed++
          failedCount++
        }
      }

      // Update progress to complete
      if (modalProgressFill) modalProgressFill.style.width = '100%'
      if (modalProgressText) {
        if (failedCount > 0) {
          modalProgressText.textContent = `Completed! Opened ${successCount} unsubscribe links. ${failedCount} failed.`
        } else {
          modalProgressText.textContent = `Completed! Opened ${successCount} unsubscribe links in new tabs.`
        }
      }

      // Clean up selected subscriptions
      this.selectedSubscriptions.clear()
      this.updateSelectionUI()

      // Hide modal after delay
      setTimeout(() => {
        this.hideBulkUnsubscribeModal()
        if (modalProgress) modalProgress.style.display = 'none'
        if (confirmBtn) confirmBtn.disabled = false
      }, 2000)
    } catch (error) {
      this.logError('Error during bulk unsubscribe:', error)
      if (modalProgressText) {
        modalProgressText.textContent = 'Error occurred during bulk unsubscribe.'
      }
    } finally {
      this.bulkUnsubscribeInProgress = false
    }
  }

  async simulateUnsubscribe (subscription) {
    this.log(`Unsubscribing from ${subscription.domain}`)

    // Get the best unsubscribe link for this subscription
    const unsubscribeLinks = subscription.unsubscribeLinks || []

    if (unsubscribeLinks.length === 0) {
      this.log('No unsubscribe links found for this subscription')
      return false
    }

    // Sort links by trust level (highest first) and take the best one
    const bestLink = unsubscribeLinks
      .filter(link => link.href && link.href.startsWith('http'))
      .sort((a, b) => (b.trustLevel || 0) - (a.trustLevel || 0))[0]

    if (!bestLink) {
      this.log('No valid unsubscribe links found')
      return false
    }

    this.log(`Opening unsubscribe link in sandbox: ${bestLink.href} (Trust: ${bestLink.trustLevel}%)`)

    try {
      // Open the unsubscribe link in a sandboxed iframe instead of new tab
      this.showSandboxedUnsubscribe(bestLink, subscription)

      this.log(`Opened unsubscribe link for ${subscription.senderName} in sandbox`)
      return true
    } catch (error) {
      this.logError('Error opening unsubscribe link:', error)
      return false
    }
  }

  showSandboxedUnsubscribe (link, subscription) {
    // Use the subscription's overall security assessment for consistency
    const trustLevel = Math.max(subscription.trustLevel || 0, link.trustLevel || 0)
    const securityStatus = subscription.securityStatus || link.securityStatus || 'unknown'
    const riskLevel = this.determineRiskLevel(securityStatus, trustLevel)

    // Create modal overlay for secure unsubscribe
    const modal = document.createElement('div')
    modal.className = 'sandbox-modal'
    modal.innerHTML = `
      <div class="sandbox-modal-content">
        <div class="sandbox-modal-header">
          <h3>🔒 Secure Unsubscribe from ${this.escapeHtml(subscription.senderName)}</h3>
          <span class="sandbox-modal-close">&times;</span>
        </div>
        <div class="sandbox-modal-body">
          <div class="security-notice">
            <h4>🛡️ Security Analysis</h4>
            <div class="security-details">
              <p><strong>Domain:</strong> ${this.escapeHtml(new URL(link.href).hostname)}</p>
              <p><strong>Trust Level:</strong> <span class="trust-level-${this.getTrustClass(trustLevel)}">${trustLevel}%</span></p>
              <p><strong>Security Status:</strong> <span class="security-${securityStatus}">${securityStatus}</span></p>
              <p><strong>Risk Level:</strong> <span class="risk-${riskLevel}">${this.getRiskConfig(riskLevel).label}</span></p>
            </div>
          </div>
          
          <div class="iframe-container">
            <div class="iframe-blocked-message">
              <div class="blocked-icon">🛡️</div>
              <h4>Secure Unsubscribe Process</h4>
              <p>For your security, unsubscribe links are not embedded directly.</p>
              <p>Choose a secure option below to proceed safely:</p>
            </div>
          </div>
          
          <div class="url-display">
            <label>Unsubscribe URL:</label>
            <input type="text" readonly value="${this.escapeHtml(link.href)}" class="url-input" onclick="this.select()">
            <button class="copy-btn" onclick="navigator.clipboard.writeText('${this.escapeHtml(link.href)}')">📋 Copy</button>
          </div>
        </div>
        <div class="sandbox-modal-footer">
          <button class="btn-secondary close-btn">❌ Cancel</button>
          <button class="btn-warning preview-btn" data-url="${this.escapeHtml(link.href)}">👁️ Preview Safely</button>
          <button class="btn-primary open-btn" data-url="${this.escapeHtml(link.href)}">🌐 Open Securely</button>
        </div>
      </div>
    `

    // Add enhanced styles
    if (!document.getElementById('sandbox-modal-styles')) {
      const style = document.createElement('style')
      style.id = 'sandbox-modal-styles'
      style.textContent = `
        .sandbox-modal {
          position: fixed;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background: rgba(0, 0, 0, 0.8);
          display: flex;
          justify-content: center;
          align-items: center;
          z-index: 10000;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        .sandbox-modal-content {
          background: white;
          border-radius: 12px;
          width: 90%;
          max-width: 700px;
          max-height: 80vh;
          display: flex;
          flex-direction: column;
          box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
          overflow: hidden;
        }
        .sandbox-modal-header {
          padding: 20px;
          border-bottom: 1px solid #e9ecef;
          display: flex;
          justify-content: space-between;
          align-items: center;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          color: white;
        }
        .sandbox-modal-header h3 {
          margin: 0;
          font-size: 18px;
          font-weight: 600;
        }
        .sandbox-modal-close {
          font-size: 24px;
          cursor: pointer;
          opacity: 0.8;
          line-height: 1;
          transition: opacity 0.2s;
        }
        .sandbox-modal-close:hover {
          opacity: 1;
        }
        .sandbox-modal-body {
          padding: 24px;
          overflow-y: auto;
          flex: 1;
        }
        .security-notice {
          background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
          border: 1px solid #dee2e6;
          border-radius: 8px;
          padding: 16px;
          margin-bottom: 20px;
        }
        .security-notice h4 {
          margin: 0 0 12px 0;
          color: #495057;
          font-size: 16px;
        }
        .security-details p {
          margin: 8px 0;
          font-size: 14px;
          color: #6c757d;
        }
        .trust-level-high { color: #28a745; font-weight: bold; }
        .trust-level-medium { color: #ffc107; font-weight: bold; }
        .trust-level-low { color: #dc3545; font-weight: bold; }
        .security-safe { color: #28a745; font-weight: bold; }
        .security-caution { color: #ffc107; font-weight: bold; }
        .security-dangerous { color: #dc3545; font-weight: bold; }
        .security-unknown { color: #6c757d; font-weight: bold; }
        .iframe-container {
          background: #f8f9fa;
          border: 2px dashed #dee2e6;
          border-radius: 8px;
          padding: 40px 20px;
          text-align: center;
          margin-bottom: 20px;
        }
        .iframe-blocked-message {
          color: #6c757d;
        }
        .blocked-icon {
          font-size: 48px;
          margin-bottom: 16px;
        }
        .iframe-blocked-message h4 {
          margin: 0 0 12px 0;
          color: #495057;
        }
        .iframe-blocked-message p {
          margin: 8px 0;
          font-size: 14px;
          line-height: 1.5;
        }
        .url-display {
          background: #f8f9fa;
          border-radius: 8px;
          padding: 16px;
          display: flex;
          align-items: center;
          gap: 12px;
        }
        .url-display label {
          font-weight: 600;
          color: #495057;
          min-width: 120px;
        }
        .url-input {
          flex: 1;
          padding: 8px 12px;
          border: 1px solid #ced4da;
          border-radius: 4px;
          font-size: 13px;
          font-family: monospace;
          background: white;
        }
        .copy-btn {
          padding: 8px 12px;
          background: #6c757d;
          color: white;
          border: none;
          border-radius: 4px;
          cursor: pointer;
          font-size: 12px;
          transition: background 0.2s;
        }
        .copy-btn:hover {
          background: #5a6268;
        }
        .sandbox-modal-footer {
          padding: 20px;
          border-top: 1px solid #e9ecef;
          display: flex;
          justify-content: flex-end;
          gap: 12px;
          background: #f8f9fa;
        }
        .btn-primary, .btn-secondary, .btn-warning {
          padding: 10px 20px;
          border-radius: 6px;
          border: none;
          cursor: pointer;
          font-size: 14px;
          font-weight: 500;
          transition: all 0.2s;
        }
        .btn-primary {
          background: #007bff;
          color: white;
        }
        .btn-primary:hover {
          background: #0056b3;
          transform: translateY(-1px);
        }
        .btn-warning {
          background: #ffc107;
          color: #212529;
        }
        .btn-warning:hover {
          background: #e0a800;
          transform: translateY(-1px);
        }
        .btn-secondary {
          background: #6c757d;
          color: white;
        }
        .btn-secondary:hover {
          background: #545b62;
        }
      `
      document.head.appendChild(style)
    }

    // Add event listeners
    const closeBtn = modal.querySelector('.sandbox-modal-close')
    const cancelBtn = modal.querySelector('.close-btn')
    const previewBtn = modal.querySelector('.preview-btn')
    const openBtn = modal.querySelector('.open-btn')

    // Close functionality
    const closeModal = () => modal.remove()
    closeBtn.addEventListener('click', closeModal)
    cancelBtn.addEventListener('click', closeModal)

    // Preview functionality - shows URL analysis
    previewBtn.addEventListener('click', () => {
      this.showUrlAnalysis(link.href)
    })

    // Open securely functionality
    openBtn.addEventListener('click', () => {
      this.openUrlSecurely(link.href, subscription)
      closeModal()
    })

    // Close on escape key
    const handleEscape = (e) => {
      if (e.key === 'Escape') {
        closeModal()
        document.removeEventListener('keydown', handleEscape)
      }
    }
    document.addEventListener('keydown', handleEscape)

    // Close on outside click
    modal.addEventListener('click', (e) => {
      if (e.target === modal) {
        closeModal()
      }
    })

    document.body.appendChild(modal)
  }

  getTrustClass (trustLevel) {
    const level = parseInt(trustLevel) || 0
    if (level >= 80) return 'high'
    if (level >= 50) return 'medium'
    return 'low'
  }

  showUrlAnalysis (url) {
    try {
      const urlObj = new URL(url)
      const analysis = {
        protocol: urlObj.protocol,
        hostname: urlObj.hostname,
        path: urlObj.pathname,
        hasParams: urlObj.search.length > 0,
        isHttps: urlObj.protocol === 'https:',
        isSuspiciousDomain: this.checkSuspiciousDomain(urlObj.hostname),
        domainAge: 'Unknown' // Could be enhanced with domain age checking
      }

      alert(`🔍 URL Security Analysis:

🌐 Domain: ${analysis.hostname}
🔒 Protocol: ${analysis.protocol} ${analysis.isHttps ? '✅' : '⚠️'}
📍 Path: ${analysis.path}
📋 Has Parameters: ${analysis.hasParams ? 'Yes' : 'No'}
⚠️ Suspicious Domain: ${analysis.isSuspiciousDomain ? 'Yes ⚠️' : 'No ✅'}

Recommendation: ${this.getSecurityRecommendation(analysis)}`)
    } catch (error) {
      alert('❌ Invalid URL format')
    }
  }

  checkSuspiciousDomain (hostname) {
    const suspiciousPatterns = [
      /bit\.ly|tinyurl|t\.co|goo\.gl/, // URL shorteners
      /[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}/, // IP addresses
      /[a-z0-9]{20,}\.com/, // Random long domains
      /\-[a-z0-9]{10,}\./ // Domains with long random strings
    ]

    return suspiciousPatterns.some(pattern => pattern.test(hostname.toLowerCase()))
  }

  getSecurityRecommendation (analysis) {
    if (!analysis.isHttps) return '⚠️ Use caution - Not using HTTPS'
    if (analysis.isSuspiciousDomain) return '⚠️ Use caution - Suspicious domain pattern'
    return '✅ Appears safe to proceed'
  }

  async openUrlSecurely (url, subscription) {
    try {
      // Show security confirmation
      const proceed = confirm(`🔒 Security Confirmation

Opening: ${url}

Security measures in place:
✅ Opening in isolated tab
✅ No access to your main session
✅ Limited permissions

Do you want to proceed?`)

      if (proceed) {
        // Try Chrome extension API first
        if (typeof chrome !== 'undefined' && chrome.tabs && chrome.tabs.create) {
          try {
            // Create tab with security restrictions
            const tab = await chrome.tabs.create({
              url,
              active: true,
              openerTabId: null // Prevent access to opener
            })

            // Set up tab monitoring for additional security
            this.monitorSecureTab(tab.id, subscription)

            this.log(`Securely opened unsubscribe link for ${subscription.senderName}`)
          } catch (chromeError) {
            this.logError('Chrome API failed, using fallback:', chromeError)
            this.openUrlFallback(url)
          }
        } else {
          // Fallback for non-extension environments
          this.log('Chrome tabs API not available, using fallback')
          this.openUrlFallback(url)
        }

        // Show success message
        setTimeout(() => {
          alert(`✅ Unsubscribe link opened securely!

Please complete the unsubscribe process in the new tab.
The tab has been isolated for your security.`)
        }, 500)
      }
    } catch (error) {
      this.logError('Error opening URL securely:', error)
      alert('❌ Failed to open unsubscribe link. Please try copying the URL and opening it manually.')
    }
  }

  openUrlFallback (url) {
    // Secure fallback method
    const newWindow = window.open('', '_blank', 'noopener,noreferrer')
    if (newWindow) {
      newWindow.opener = null
      newWindow.location.href = url
      this.log(`Opened URL in secure window: ${url}`)
    } else {
      // Popup blocked - copy to clipboard
      this.copyToClipboard(url)
      alert(`🚫 Popup blocked. URL copied to clipboard:
${url}

Please paste it in a new tab manually.`)
    }
  }

  async copyToClipboard (text) {
    try {
      if (navigator.clipboard) {
        await navigator.clipboard.writeText(text)
      } else {
        // Fallback for older browsers
        const textArea = document.createElement('textarea')
        textArea.value = text
        textArea.style.position = 'fixed'
        textArea.style.opacity = '0'
        document.body.appendChild(textArea)
        textArea.focus()
        textArea.select()
        document.execCommand('copy')
        document.body.removeChild(textArea)
      }
    } catch (error) {
      this.logError('Failed to copy to clipboard:', error)
    }
  }

  monitorSecureTab (tabId, subscription) {
    // Monitor tab for security purposes - only if Chrome APIs are available
    if (typeof chrome !== 'undefined' && chrome.tabs && chrome.tabs.onUpdated) {
      const tabListener = (changedTabId, changeInfo, tab) => {
        if (changedTabId === tabId) {
          if (changeInfo.status === 'complete') {
            this.log(`Secure tab loaded: ${tab.url}`)

            // Optional: Inject security monitoring script
            if (tab.url && tab.url.startsWith('http') && chrome.scripting) {
              chrome.scripting.executeScript({
                target: { tabId },
                function: () => {
                  // Minimal security monitoring
                  console.log('🔒 Secure unsubscribe tab - monitored by Email Unsubscriber extension')

                  // Prevent some common attacks
                  if (window.opener) {
                    window.opener = null
                  }
                }
              }).catch(() => {
                // Ignore errors - some sites block script injection
              })
            }
          }
        }
      }

      chrome.tabs.onUpdated.addListener(tabListener)

      // Clean up listener after 5 minutes
      setTimeout(() => {
        chrome.tabs.onUpdated.removeListener(tabListener)
      }, 300000)
    } else {
      this.log('Chrome tabs API not available, skipping tab monitoring')
    }
  }

  async comprehensiveUnsubscribe (subscription) {
    this.log(`Starting comprehensive unsubscribe for ${subscription.domain}`)

    // Get all available unsubscribe links for this subscription
    const allUnsubscribeLinks = subscription.allUnsubscribeLinks || subscription.unsubscribeLinks || []

    if (allUnsubscribeLinks.length === 0) {
      this.log('No unsubscribe links found for this subscription')
      return false
    }

    // Sort links by preference: most recent first, then by trust level
    const sortedLinks = this.getSortedUnsubscribeLinks(allUnsubscribeLinks)
    const validLinks = sortedLinks.filter(link => link.href && link.href.startsWith('http'))

    if (validLinks.length === 0) {
      this.log('No valid HTTP unsubscribe links found')
      return false
    }

    this.log(`Found ${validLinks.length} valid unsubscribe links, trying in order of preference`)

    // Show progress modal for multiple links
    if (validLinks.length > 1) {
      this.showUnsubscribeProgress(subscription, validLinks)
    }

    let successCount = 0
    let lastError = null

    // Try each link in order
    for (let i = 0; i < validLinks.length; i++) {
      const link = validLinks[i]
      this.log(`Trying link ${i + 1}/${validLinks.length}: ${link.href} (Trust: ${link.trustLevel || 0}%)`)

      try {
        // Update progress if modal is shown
        this.updateUnsubscribeProgress(i + 1, validLinks.length, link, subscription.senderName)

        // Use the same secure opening method as before
        await this.openLinkSecurely(link, subscription)
        successCount++

        // Small delay between links to prevent overwhelming
        if (i < validLinks.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 1500))
        }
      } catch (error) {
        this.logError(`Failed to open link ${i + 1}:`, error)
        lastError = error
        continue
      }
    }

    // Hide progress modal
    this.hideUnsubscribeProgress()

    if (successCount > 0) {
      this.log(`Comprehensive unsubscribe successful: opened ${successCount}/${validLinks.length} links`)
      return true
    } else {
      this.logError('All unsubscribe attempts failed:', lastError)
      return false
    }
  }

  getSortedUnsubscribeLinks (unsubscribeLinks) {
    return unsubscribeLinks.slice().sort((a, b) => {
      // First priority: Most recent (by extractedAt timestamp)
      const timeA = a.extractedAt ? new Date(a.extractedAt).getTime() : 0
      const timeB = b.extractedAt ? new Date(b.extractedAt).getTime() : 0

      if (timeA !== timeB) {
        return timeB - timeA // Most recent first
      }

      // Second priority: Highest trust level
      const trustA = a.trustLevel || 0
      const trustB = b.trustLevel || 0

      if (trustA !== trustB) {
        return trustB - trustA // Highest trust first
      }

      // Third priority: Domain reliability (favor known domains)
      const reliabilityA = this.getDomainReliabilityScore(a.href)
      const reliabilityB = this.getDomainReliabilityScore(b.href)

      return reliabilityB - reliabilityA // Highest reliability first
    })
  }

  getDomainReliabilityScore (url) {
    try {
      const hostname = new URL(url).hostname.toLowerCase()

      // High reliability domains (major email providers, known services)
      const highReliability = [
        'gmail.com', 'google.com', 'youtube.com', 'facebook.com', 'twitter.com',
        'linkedin.com', 'instagram.com', 'amazon.com', 'microsoft.com', 'apple.com',
        'mailchimp.com', 'constantcontact.com', 'sendgrid.com', 'mailgun.com'
      ]

      // Medium reliability domains (established companies)
      const mediumReliability = [
        'nobroker.in', 'zomato.com', 'swiggy.com', 'flipkart.com', 'myntra.com',
        'paytm.com', 'phonepe.com', 'uber.com', 'ola.com', 'naukri.com'
      ]

      if (highReliability.some(domain => hostname.includes(domain))) {
        return 3
      } else if (mediumReliability.some(domain => hostname.includes(domain))) {
        return 2
      } else if (hostname.match(/\.(com|org|net|edu|gov)$/)) {
        return 1
      }

      return 0
    } catch (error) {
      return 0
    }
  }

  showUnsubscribeProgress (subscription, links) {
    // Create progress modal
    const modal = document.createElement('div')
    modal.id = 'unsubscribeProgressModal'
    modal.className = 'unsubscribe-progress-modal'
    modal.innerHTML = `
      <div class="progress-modal-content">
        <div class="progress-modal-header">
          <h3>🔄 Comprehensive Unsubscribe</h3>
          <p>Trying all available unsubscribe links for ${this.escapeHtml(subscription.senderName)}</p>
        </div>
        <div class="progress-modal-body">
          <div class="progress-bar">
            <div class="progress-fill" id="unsubscribeProgressFill"></div>
          </div>
          <div class="progress-text">
            <span id="unsubscribeProgressText">Preparing...</span>
          </div>
          <div class="progress-details">
            <span id="unsubscribeProgressDetails">Found ${links.length} unsubscribe links</span>
          </div>
        </div>
      </div>
    `

    // Add styles if not already present
    if (!document.getElementById('unsubscribe-progress-styles')) {
      const style = document.createElement('style')
      style.id = 'unsubscribe-progress-styles'
      style.textContent = `
        .unsubscribe-progress-modal {
          position: fixed;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background: rgba(0, 0, 0, 0.7);
          display: flex;
          justify-content: center;
          align-items: center;
          z-index: 10001;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        .progress-modal-content {
          background: white;
          border-radius: 12px;
          padding: 24px;
          width: 90%;
          max-width: 500px;
          box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        .progress-modal-header h3 {
          margin: 0 0 8px 0;
          color: #333;
          font-size: 18px;
        }
        .progress-modal-header p {
          margin: 0 0 20px 0;
          color: #666;
          font-size: 14px;
        }
        .progress-bar {
          width: 100%;
          height: 8px;
          background: #e9ecef;
          border-radius: 4px;
          overflow: hidden;
          margin-bottom: 12px;
        }
        .progress-fill {
          height: 100%;
          background: linear-gradient(90deg, #4285f4 0%, #34a853 100%);
          width: 0%;
          transition: width 0.5s ease;
        }
        .progress-text {
          font-weight: 500;
          color: #333;
          margin-bottom: 8px;
        }
        .progress-details {
          font-size: 12px;
          color: #666;
        }
      `
      document.head.appendChild(style)
    }

    document.body.appendChild(modal)
  }

  updateUnsubscribeProgress (current, total, link, senderName) {
    const progressFill = document.getElementById('unsubscribeProgressFill')
    const progressText = document.getElementById('unsubscribeProgressText')
    const progressDetails = document.getElementById('unsubscribeProgressDetails')

    if (progressFill) {
      const percentage = (current / total) * 100
      progressFill.style.width = `${percentage}%`
    }

    if (progressText) {
      const domain = new URL(link.href).hostname
      progressText.textContent = `Opening link ${current}/${total}: ${domain}`
    }

    if (progressDetails) {
      progressDetails.textContent = `Trust level: ${link.trustLevel || 0}% | Trying for ${senderName}`
    }
  }

  hideUnsubscribeProgress () {
    const modal = document.getElementById('unsubscribeProgressModal')
    if (modal) {
      setTimeout(() => modal.remove(), 1000) // Small delay to show completion
    }
  }

  async openLinkSecurely (link, subscription) {
    // Use the existing secure opening functionality but simplified for multiple links
    try {
      if (typeof chrome !== 'undefined' && chrome.tabs && chrome.tabs.create) {
        const tab = await chrome.tabs.create({
          url: link.href,
          active: false, // Don't focus each tab to avoid overwhelming the user
          openerTabId: null
        })

        this.log(`Securely opened unsubscribe link: ${link.href}`)
        return true
      } else {
        // Fallback for non-extension environments
        const newWindow = window.open(link.href, '_blank', 'noopener,noreferrer')
        if (newWindow) {
          newWindow.opener = null
          return true
        }
        throw new Error('Failed to open window')
      }
    } catch (error) {
      this.logError('Error opening link securely:', error)
      throw error
    }
  }

  async unsubscribeSingle (senderId) {
    this.log('Unsubscribing single:', senderId)
    const subscription = this.subscriptions.find(sub => sub.senderId === senderId)
    if (!subscription) {
      this.logError('Subscription not found:', senderId)
      alert('Subscription not found. Please refresh the dashboard.')
      return
    }

    // Check if this is a dangerous subscription
    const riskLevel = this.determineRiskLevel(subscription.securityStatus, subscription.trustLevel)

    if (riskLevel === 'high' || riskLevel === 'unknown') {
      alert('This subscription requires manual review due to security concerns. Please check the security details first.')
      return
    }

    const success = await this.comprehensiveUnsubscribe(subscription)

    if (success) {
      alert(`Successfully initiated unsubscribe process for ${subscription.senderName}. Please complete the process in the opened tab(s).`)
    } else {
      alert(`No valid unsubscribe links found for ${subscription.senderName}. You may need to unsubscribe manually.`)
    }
  }

  viewDetails (senderId) {
    this.log('Viewing details for:', senderId)
    // Escape the senderId to match how it was created
    const escapedSenderId = this.escapeHtml(senderId)
    const securityDetails = document.getElementById(`security-${escapedSenderId}`)
    if (securityDetails) {
      const isVisible = securityDetails.style.display !== 'none'
      securityDetails.style.display = isVisible ? 'none' : 'block'

      // Update button text
      const detailsButton = securityDetails.parentElement.querySelector('.action-details')
      if (detailsButton) {
        detailsButton.textContent = isVisible ? 'Details' : 'Hide Details'
      }
    } else {
      this.logError('Security details element not found for:', senderId, 'Escaped:', escapedSenderId)
    }
  }

  goBack () {
    // Close the dashboard window/tab
    window.close()
  }
}

// Initialize dashboard when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  try {
    const dashboard = new SubscriptionDashboard()
    // Make it globally accessible for debugging
    window.dashboard = dashboard
  } catch (error) {
    console.error('[Dashboard] Failed to initialize:', error)
  }
})
