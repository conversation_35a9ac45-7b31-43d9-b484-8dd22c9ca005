// Debug script to check what's actually stored in Chrome storage
// Run this in the browser console on Gmail to see subscription data

async function debugStorage () {
  console.log('=== Email Unsubscriber Storage Debug ===')

  try {
    // Check if Chrome storage is available
    if (typeof chrome !== 'undefined' && chrome.storage && chrome.storage.local) {
      console.log('✅ Chrome storage API available')

      // Get all stored data
      const keys = ['subscriptionDatabase', 'subscriptionMetadata', 'unsubscribeExtractions']
      const result = await chrome.storage.local.get(keys)

      console.log('\n📊 Storage Overview:')
      console.log(`- Subscription Database: ${result.subscriptionDatabase ? result.subscriptionDatabase.length : 0} entries`)
      console.log(`- Unsubscribe Extractions: ${result.unsubscribeExtractions ? result.unsubscribeExtractions.length : 0} entries`)
      console.log('- Metadata:', result.subscriptionMetadata)

      // Check subscription database
      if (result.subscriptionDatabase && result.subscriptionDatabase.length > 0) {
        console.log('\n🗃️ Subscription Database Contents:')
        result.subscriptionDatabase.forEach((sub, index) => {
          console.log(`\n${index + 1}. ${sub.senderName} (${sub.senderId})`)
          console.log(`   Email: ${sub.senderEmail}`)
          console.log(`   Domain: ${sub.senderDomain}`)
          console.log(`   Unsubscribe Links: ${sub.unsubscribeLinks ? sub.unsubscribeLinks.length : 0}`)
          if (sub.unsubscribeLinks && sub.unsubscribeLinks.length > 0) {
            sub.unsubscribeLinks.forEach((link, linkIndex) => {
              console.log(`     ${linkIndex + 1}. ${link.href} (${link.text})`)
            })
          }
          console.log(`   Email Count: ${sub.emailCount}`)
          console.log(`   Trust Level: ${sub.trustLevel}%`)
          console.log(`   Security Status: ${sub.securityStatus}`)
        })
      } else {
        console.log('\n❌ No subscription database found or empty')
      }

      // Check extractions
      if (result.unsubscribeExtractions && result.unsubscribeExtractions.length > 0) {
        console.log('\n🔍 Recent Extractions:')
        const recent = result.unsubscribeExtractions.slice(-5) // Last 5 extractions
        recent.forEach((extraction, index) => {
          console.log(`\n${index + 1}. ${extraction.timestamp}`)
          console.log(`   Sender: ${extraction.sender?.name} (${extraction.sender?.email})`)
          console.log(`   Links Found: ${extraction.linksFound}`)
          if (extraction.links && extraction.links.length > 0) {
            extraction.links.forEach((link, linkIndex) => {
              console.log(`     ${linkIndex + 1}. ${link.href} (${link.text})`)
            })
          }
        })
      } else {
        console.log('\n❌ No extraction data found')
      }
    } else {
      console.log('❌ Chrome storage API not available')

      // Try localStorage fallback
      console.log('Checking localStorage fallback...')
      const stored = localStorage.getItem('subscriptionDatabase')
      if (stored) {
        const data = JSON.parse(stored)
        console.log(`Found ${data.length} subscriptions in localStorage:`, data)
      } else {
        console.log('No data in localStorage either')
      }
    }
  } catch (error) {
    console.error('Error checking storage:', error)
  }
}

// Function to clear all storage (for testing)
async function clearStorage () {
  if (confirm('This will clear ALL subscription data. Are you sure?')) {
    try {
      if (typeof chrome !== 'undefined' && chrome.storage && chrome.storage.local) {
        await chrome.storage.local.clear()
        console.log('✅ Chrome storage cleared')
      }
      localStorage.removeItem('subscriptionDatabase')
      console.log('✅ localStorage cleared')
      console.log('All storage cleared. Refresh the page and run email extraction again.')
    } catch (error) {
      console.error('Error clearing storage:', error)
    }
  }
}

// Function to check for duplicate entries
async function checkDuplicates () {
  try {
    if (typeof chrome !== 'undefined' && chrome.storage && chrome.storage.local) {
      const result = await chrome.storage.local.get(['subscriptionDatabase'])
      const subscriptions = result.subscriptionDatabase || []

      console.log('\n🔍 Duplicate Analysis:')

      // Group by senderId
      const groups = {}
      subscriptions.forEach(sub => {
        if (!groups[sub.senderId]) {
          groups[sub.senderId] = []
        }
        groups[sub.senderId].push(sub)
      })

      // Check for duplicates
      Object.keys(groups).forEach(senderId => {
        if (groups[senderId].length > 1) {
          console.log(`⚠️ Found ${groups[senderId].length} entries for senderId: ${senderId}`)
          groups[senderId].forEach((sub, index) => {
            console.log(`  ${index + 1}. ${sub.senderName} - ${sub.senderEmail}`)
          })
        }
      })

      // Check for same domain/URL patterns
      const urlGroups = {}
      subscriptions.forEach(sub => {
        if (sub.unsubscribeLinks) {
          sub.unsubscribeLinks.forEach(link => {
            if (!urlGroups[link.href]) {
              urlGroups[link.href] = []
            }
            urlGroups[link.href].push(sub)
          })
        }
      })

      console.log('\n🔗 URL Analysis:')
      Object.keys(urlGroups).forEach(url => {
        if (urlGroups[url].length > 1) {
          console.log(`⚠️ URL "${url}" appears in ${urlGroups[url].length} different subscriptions:`)
          urlGroups[url].forEach(sub => {
            console.log(`  - ${sub.senderName} (${sub.senderId})`)
          })
        }
      })
    }
  } catch (error) {
    console.error('Error checking duplicates:', error)
  }
}

// Run the debug function
debugStorage()

console.log('\n🛠️ Available debug functions:')
console.log('- debugStorage() - Check current storage state')
console.log('- clearStorage() - Clear all subscription data')
console.log('- checkDuplicates() - Check for duplicate entries')
