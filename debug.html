<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Unsubscriber - Debug Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f5f5;
            color: #333;
            line-height: 1.4;
        }

        .header {
            background: #2c3e50;
            color: white;
            padding: 1rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .header h1 {
            font-size: 1.5rem;
            font-weight: 600;
        }

        .controls {
            display: flex;
            gap: 0.5rem;
        }

        .btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: background-color 0.2s;
        }

        .btn-primary {
            background: #3498db;
            color: white;
        }

        .btn-primary:hover {
            background: #2980b9;
        }

        .btn-secondary {
            background: #95a5a6;
            color: white;
        }

        .btn-secondary:hover {
            background: #7f8c8d;
        }

        .btn-danger {
            background: #e74c3c;
            color: white;
        }

        .btn-danger:hover {
            background: #c0392b;
        }

        .dashboard {
            display: grid;
            grid-template-columns: 300px 1fr;
            height: calc(100vh - 70px);
        }

        .sidebar {
            background: white;
            padding: 1rem;
            border-right: 1px solid #ddd;
            overflow-y: auto;
        }

        .sidebar h3 {
            margin-bottom: 1rem;
            color: #2c3e50;
            font-size: 1.1rem;
        }

        .stats {
            margin-bottom: 2rem;
        }

        .stat-item {
            display: flex;
            justify-content: space-between;
            padding: 0.5rem;
            margin-bottom: 0.5rem;
            background: #f8f9fa;
            border-radius: 4px;
        }

        .stat-value {
            font-weight: 600;
        }

        .filters {
            margin-bottom: 2rem;
        }

        .filter-group {
            margin-bottom: 1rem;
        }

        .filter-group label {
            display: block;
            margin-bottom: 0.25rem;
            font-weight: 500;
        }

        .filter-group select,
        .filter-group input {
            width: 100%;
            padding: 0.5rem;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 0.9rem;
        }

        .main-content {
            padding: 1rem;
            overflow-y: auto;
        }

        .logs-container {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            max-height: 100%;
            overflow-y: auto;
        }

        .log-entry {
            padding: 1rem;
            border-bottom: 1px solid #eee;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 0.9rem;
        }

        .log-entry:last-child {
            border-bottom: none;
        }

        .log-entry.error {
            background: #ffeaea;
            border-left: 4px solid #e74c3c;
        }

        .log-entry.warn {
            background: #fff8e1;
            border-left: 4px solid #f39c12;
        }

        .log-entry.info {
            background: #e8f4fd;
            border-left: 4px solid #3498db;
        }

        .log-entry.debug {
            background: #f8f9fa;
            border-left: 4px solid #95a5a6;
        }

        .log-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.5rem;
        }

        .log-level {
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        .log-level.error {
            background: #e74c3c;
            color: white;
        }

        .log-level.warn {
            background: #f39c12;
            color: white;
        }

        .log-level.info {
            background: #3498db;
            color: white;
        }

        .log-level.debug {
            background: #95a5a6;
            color: white;
        }

        .log-timestamp {
            font-size: 0.8rem;
            color: #666;
        }

        .log-component {
            background: #34495e;
            color: white;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .log-message {
            margin: 0.5rem 0;
            font-weight: 500;
        }

        .log-data {
            background: #f8f9fa;
            padding: 0.5rem;
            border-radius: 4px;
            font-size: 0.8rem;
            color: #666;
            white-space: pre-wrap;
            word-break: break-all;
        }

        .no-logs {
            padding: 2rem;
            text-align: center;
            color: #666;
        }

        .auto-scroll {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: #3498db;
            color: white;
            border: none;
            padding: 0.75rem;
            border-radius: 50%;
            cursor: pointer;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
            transition: background-color 0.2s;
        }

        .auto-scroll:hover {
            background: #2980b9;
        }

        .auto-scroll.active {
            background: #27ae60;
        }

        .loading {
            text-align: center;
            padding: 2rem;
            color: #666;
        }

        .loading::after {
            content: '...';
            animation: loading 1s infinite;
        }

        @keyframes loading {
            0% { content: '.'; }
            33% { content: '..'; }
            66% { content: '...'; }
        }

        .status-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 0.5rem;
        }

        .status-indicator.connected {
            background: #27ae60;
            animation: pulse 2s infinite;
        }

        .status-indicator.disconnected {
            background: #e74c3c;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>
            <span class="status-indicator" id="statusIndicator"></span>
            Email Unsubscriber Debug Dashboard
        </h1>
        <div class="controls">
            <button class="btn btn-secondary" id="refreshBtn">Refresh</button>
            <button class="btn btn-primary" id="exportBtn">Export Logs</button>
            <button class="btn btn-danger" id="clearBtn">Clear Logs</button>
        </div>
    </div>

    <div class="dashboard">
        <div class="sidebar">
            <div class="stats">
                <h3>Statistics</h3>
                <div class="stat-item">
                    <span>Total Logs:</span>
                    <span class="stat-value" id="totalLogs">0</span>
                </div>
                <div class="stat-item">
                    <span>Errors:</span>
                    <span class="stat-value" id="errorCount">0</span>
                </div>
                <div class="stat-item">
                    <span>Warnings:</span>
                    <span class="stat-value" id="warnCount">0</span>
                </div>
                <div class="stat-item">
                    <span>Info:</span>
                    <span class="stat-value" id="infoCount">0</span>
                </div>
                <div class="stat-item">
                    <span>Debug:</span>
                    <span class="stat-value" id="debugCount">0</span>
                </div>
            </div>

            <div class="filters">
                <h3>Filters</h3>
                <div class="filter-group">
                    <label for="levelFilter">Log Level:</label>
                    <select id="levelFilter">
                        <option value="">All Levels</option>
                        <option value="error">Error</option>
                        <option value="warn">Warning</option>
                        <option value="info">Info</option>
                        <option value="debug">Debug</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label for="componentFilter">Component:</label>
                    <select id="componentFilter">
                        <option value="">All Components</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label for="searchFilter">Search:</label>
                    <input type="text" id="searchFilter" placeholder="Search messages...">
                </div>
                <div class="filter-group">
                    <label for="sinceFilter">Since:</label>
                    <input type="datetime-local" id="sinceFilter">
                </div>
            </div>
        </div>

        <div class="main-content">
            <div class="logs-container">
                <div id="logsContainer">
                    <div class="loading">Loading logs</div>
                </div>
            </div>
        </div>
    </div>

    <button class="auto-scroll" id="autoScrollBtn" title="Auto-scroll to bottom">
        ↓
    </button>

    <script src="utils/logger.js"></script>
    <script src="debug.js"></script>
</body>
</html>