/**
 * Debug Dashboard JavaScript
 * Real-time log viewer with filtering and export capabilities
 */

class DebugDashboard {
  constructor () {
    this.logs = []
    this.filteredLogs = []
    this.autoScroll = true
    this.isConnected = false
    this.refreshInterval = null

    this.initializeElements()
    this.setupEventListeners()
    this.setupMessageListener()
    this.loadLogs()
    this.startAutoRefresh()
  }

  initializeElements () {
    this.elements = {
      statusIndicator: document.getElementById('statusIndicator'),
      refreshBtn: document.getElementById('refreshBtn'),
      exportBtn: document.getElementById('exportBtn'),
      clearBtn: document.getElementById('clearBtn'),
      autoScrollBtn: document.getElementById('autoScrollBtn'),
      logsContainer: document.getElementById('logsContainer'),

      // Stats
      totalLogs: document.getElementById('totalLogs'),
      errorCount: document.getElementById('errorCount'),
      warnCount: document.getElementById('warnCount'),
      infoCount: document.getElementById('infoCount'),
      debugCount: document.getElementById('debugCount'),

      // Filters
      levelFilter: document.getElementById('levelFilter'),
      componentFilter: document.getElementById('componentFilter'),
      searchFilter: document.getElementById('searchFilter'),
      sinceFilter: document.getElementById('sinceFilter')
    }
  }

  setupEventListeners () {
    // Control buttons
    this.elements.refreshBtn.addEventListener('click', () => this.loadLogs())
    this.elements.exportBtn.addEventListener('click', () => this.exportLogs())
    this.elements.clearBtn.addEventListener('click', () => this.clearLogs())
    this.elements.autoScrollBtn.addEventListener('click', () => this.toggleAutoScroll())

    // Filters
    this.elements.levelFilter.addEventListener('change', () => this.applyFilters())
    this.elements.componentFilter.addEventListener('change', () => this.applyFilters())
    this.elements.searchFilter.addEventListener('input', () => this.applyFilters())
    this.elements.sinceFilter.addEventListener('change', () => this.applyFilters())

    // Scroll detection
    this.elements.logsContainer.addEventListener('scroll', () => this.handleScroll())
  }

  setupMessageListener () {
    // Listen for real-time log updates
    if (typeof chrome !== 'undefined' && chrome.runtime) {
      chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
        if (message.type === 'DEBUG_LOG') {
          this.addLogEntry(message.logEntry)
          this.updateConnectionStatus(true)
        }
      })
    }
  }

  updateConnectionStatus (connected) {
    this.isConnected = connected
    this.elements.statusIndicator.className = `status-indicator ${connected ? 'connected' : 'disconnected'}`
  }

  startAutoRefresh () {
    // Refresh logs every 5 seconds
    this.refreshInterval = setInterval(() => {
      this.loadLogs()
    }, 5000)
  }

  loadLogs () {
    if (typeof ExtensionLogger !== 'undefined') {
      ExtensionLogger.getLogs((logs) => {
        this.logs = logs
        this.updateComponentFilter()
        this.applyFilters()
        this.updateStats()
        this.updateConnectionStatus(true)
      })
    } else {
      this.updateConnectionStatus(false)
      this.showNoLogs('ExtensionLogger not available')
    }
  }

  updateComponentFilter () {
    const components = [...new Set(this.logs.map(log => log.component))]
    const currentValue = this.elements.componentFilter.value

    this.elements.componentFilter.innerHTML = '<option value="">All Components</option>'

    components.forEach(component => {
      const option = document.createElement('option')
      option.value = component
      option.textContent = component
      this.elements.componentFilter.appendChild(option)
    })

    // Restore previous selection
    this.elements.componentFilter.value = currentValue
  }

  applyFilters () {
    const filters = {
      level: this.elements.levelFilter.value,
      component: this.elements.componentFilter.value,
      search: this.elements.searchFilter.value,
      since: this.elements.sinceFilter.value
    }

    this.filteredLogs = ExtensionLogger.filterLogs(this.logs, filters)
    this.renderLogs()
  }

  renderLogs () {
    if (this.filteredLogs.length === 0) {
      this.showNoLogs('No logs match current filters')
      return
    }

    const container = this.elements.logsContainer
    container.innerHTML = ''

    this.filteredLogs.forEach(log => {
      const logElement = this.createLogElement(log)
      container.appendChild(logElement)
    })

    if (this.autoScroll) {
      this.scrollToBottom()
    }
  }

  createLogElement (log) {
    const logDiv = document.createElement('div')
    logDiv.className = `log-entry ${log.level}`

    const timestamp = new Date(log.timestamp).toLocaleString()

    logDiv.innerHTML = `
      <div class="log-header">
        <div>
          <span class="log-level ${log.level}">${log.level}</span>
          <span class="log-component">${log.component}</span>
        </div>
        <span class="log-timestamp">${timestamp}</span>
      </div>
      <div class="log-message">${this.escapeHtml(log.message)}</div>
      ${log.data ? `<div class="log-data">${this.escapeHtml(log.data)}</div>` : ''}
    `

    return logDiv
  }

  addLogEntry (logEntry) {
    this.logs.push(logEntry)

    // Keep only recent logs in memory
    if (this.logs.length > ExtensionLogger.MAX_LOGS) {
      this.logs = this.logs.slice(-ExtensionLogger.MAX_LOGS)
    }

    this.updateComponentFilter()
    this.applyFilters()
    this.updateStats()
  }

  updateStats () {
    const stats = {
      total: this.logs.length,
      error: 0,
      warn: 0,
      info: 0,
      debug: 0
    }

    this.logs.forEach(log => {
      stats[log.level] = (stats[log.level] || 0) + 1
    })

    this.elements.totalLogs.textContent = stats.total
    this.elements.errorCount.textContent = stats.error
    this.elements.warnCount.textContent = stats.warn
    this.elements.infoCount.textContent = stats.info
    this.elements.debugCount.textContent = stats.debug
  }

  showNoLogs (message) {
    this.elements.logsContainer.innerHTML = `
      <div class="no-logs">
        <p>${message}</p>
      </div>
    `
  }

  exportLogs () {
    if (typeof ExtensionLogger !== 'undefined') {
      ExtensionLogger.exportLogs((exportData) => {
        const blob = new Blob([exportData], { type: 'application/json' })
        const url = URL.createObjectURL(blob)

        const a = document.createElement('a')
        a.href = url
        a.download = `extension-logs-${new Date().toISOString().split('T')[0]}.json`
        document.body.appendChild(a)
        a.click()
        document.body.removeChild(a)

        URL.revokeObjectURL(url)
      })
    }
  }

  clearLogs () {
    if (confirm('Are you sure you want to clear all logs?')) {
      if (typeof ExtensionLogger !== 'undefined') {
        ExtensionLogger.clearLogs(() => {
          this.logs = []
          this.filteredLogs = []
          this.renderLogs()
          this.updateStats()
        })
      }
    }
  }

  toggleAutoScroll () {
    this.autoScroll = !this.autoScroll
    this.elements.autoScrollBtn.classList.toggle('active', this.autoScroll)

    if (this.autoScroll) {
      this.scrollToBottom()
    }
  }

  handleScroll () {
    const container = this.elements.logsContainer
    const isAtBottom = container.scrollTop + container.clientHeight >= container.scrollHeight - 10

    if (!isAtBottom && this.autoScroll) {
      this.autoScroll = false
      this.elements.autoScrollBtn.classList.remove('active')
    }
  }

  scrollToBottom () {
    const container = this.elements.logsContainer
    container.scrollTop = container.scrollHeight
  }

  escapeHtml (unsafe) {
    return unsafe
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#039;')
  }
}

// Initialize dashboard when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  new DebugDashboard()
})

// Global functions for console access
window.DebugDashboard = DebugDashboard

// Test functions for development
window.testDebugger = {
  generateTestLogs: () => {
    const components = ['Gmail', 'Content', 'Popup', 'Background']
    const levels = ['error', 'warn', 'info', 'debug']
    const messages = [
      'Test message',
      'Function executed successfully',
      'Error occurred during processing',
      'Warning: deprecated function used',
      'Debug: variable value changed'
    ]

    for (let i = 0; i < 20; i++) {
      const level = levels[Math.floor(Math.random() * levels.length)]
      const component = components[Math.floor(Math.random() * components.length)]
      const message = messages[Math.floor(Math.random() * messages.length)]

      ExtensionLogger.log(level, component, `${message} ${i + 1}`, {
        test: true,
        index: i,
        timestamp: Date.now()
      })
    }
  },

  simulateError: () => {
    ExtensionLogger.error('Test', 'Simulated error for testing', {
      error: 'Test error',
      stack: 'Error: Test error\n    at testDebugger.simulateError'
    })
  }
}
