<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Email Unsubscriber DevTools</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;
            background: #f5f5f5;
        }
        
        .container {
            padding: 20px;
        }
        
        .header {
            background: #2c3e50;
            color: white;
            padding: 15px;
            margin: -20px -20px 20px -20px;
            border-radius: 0 0 8px 8px;
        }
        
        .header h1 {
            margin: 0;
            font-size: 18px;
            font-weight: 600;
        }
        
        .section {
            background: white;
            padding: 20px;
            margin-bottom: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .section h2 {
            margin: 0 0 15px 0;
            font-size: 16px;
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 5px;
        }
        
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
            transition: background-color 0.2s;
        }
        
        .btn-primary {
            background: #3498db;
            color: white;
        }
        
        .btn-primary:hover {
            background: #2980b9;
        }
        
        .btn-secondary {
            background: #95a5a6;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #7f8c8d;
        }
        
        .btn-danger {
            background: #e74c3c;
            color: white;
        }
        
        .btn-danger:hover {
            background: #c0392b;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        
        .info-item {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            border-left: 4px solid #3498db;
        }
        
        .info-item h3 {
            margin: 0 0 10px 0;
            font-size: 14px;
            color: #2c3e50;
        }
        
        .info-item p {
            margin: 0;
            font-size: 13px;
            color: #666;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-online {
            background: #27ae60;
            animation: pulse 2s infinite;
        }
        
        .status-offline {
            background: #e74c3c;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .log-viewer {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 4px;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 15px;
        }
        
        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
        }
        
        .log-error {
            color: #e74c3c;
        }
        
        .log-warn {
            color: #f39c12;
        }
        
        .log-info {
            color: #3498db;
        }
        
        .log-debug {
            color: #95a5a6;
        }
        
        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }
        
        .loading::after {
            content: '...';
            animation: loading 1s infinite;
        }
        
        @keyframes loading {
            0% { content: '.'; }
            33% { content: '..'; }
            66% { content: '...'; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 Email Unsubscriber Developer Tools</h1>
        </div>
        
        <div class="section">
            <h2>Extension Status</h2>
            <div class="info-grid">
                <div class="info-item">
                    <h3>
                        <span class="status-indicator" id="extensionStatus"></span>
                        Extension Status
                    </h3>
                    <p id="extensionStatusText">Checking...</p>
                </div>
                <div class="info-item">
                    <h3>Background Script</h3>
                    <p id="backgroundStatus">Checking...</p>
                </div>
                <div class="info-item">
                    <h3>Content Script</h3>
                    <p id="contentStatus">Checking...</p>
                </div>
                <div class="info-item">
                    <h3>Storage Usage</h3>
                    <p id="storageUsage">Checking...</p>
                </div>
            </div>
        </div>
        
        <div class="section">
            <h2>Debug Actions</h2>
            <button class="btn btn-primary" onclick="refreshStatus()">Refresh Status</button>
            <button class="btn btn-primary" onclick="openDebugDashboard()">Open Debug Dashboard</button>
            <button class="btn btn-secondary" onclick="exportLogs()">Export Logs</button>
            <button class="btn btn-secondary" onclick="testGmailIntegration()">Test Gmail Integration</button>
            <button class="btn btn-danger" onclick="clearAllLogs()">Clear All Logs</button>
            <button class="btn btn-danger" onclick="simulateError()">Simulate Error</button>
        </div>
        
        <div class="section">
            <h2>Extension Information</h2>
            <div class="info-grid">
                <div class="info-item">
                    <h3>Version</h3>
                    <p id="extensionVersion">Loading...</p>
                </div>
                <div class="info-item">
                    <h3>Extension ID</h3>
                    <p id="extensionId">Loading...</p>
                </div>
                <div class="info-item">
                    <h3>Platform</h3>
                    <p id="platformInfo">Loading...</p>
                </div>
                <div class="info-item">
                    <h3>Active Tab</h3>
                    <p id="activeTab">Loading...</p>
                </div>
            </div>
        </div>
        
        <div class="section">
            <h2>Recent Logs</h2>
            <button class="btn btn-secondary" onclick="refreshLogs()">Refresh Logs</button>
            <div class="log-viewer" id="logViewer">
                <div class="loading">Loading logs</div>
            </div>
        </div>
    </div>
    
    <script src="devtools.js"></script>
</body>
</html>