/**
 * Chrome DevTools integration for Email Unsubscriber extension
 * Provides debugging interface within Chrome DevTools
 */

class ExtensionDevTools {
  constructor () {
    this.backgroundConnection = null
    this.contentConnection = null
    this.isInitialized = false

    this.init()
  }

  async init () {
    console.log('DevTools initializing...')

    // Initialize UI
    this.initializeUI()

    // Set up connections
    await this.setupConnections()

    // Start monitoring
    this.startMonitoring()

    this.isInitialized = true
    console.log('DevTools initialized')
  }

  initializeUI () {
    // Load initial extension info
    this.loadExtensionInfo()

    // Set up auto-refresh
    setInterval(() => {
      this.refreshStatus()
    }, 5000)
  }

  async setupConnections () {
    try {
      // Test background connection
      const backgroundResponse = await chrome.runtime.sendMessage({ action: 'ping' })
      this.backgroundConnection = backgroundResponse?.success || false

      // Test content script connection
      const tabs = await chrome.tabs.query({ active: true, currentWindow: true })
      if (tabs[0]) {
        try {
          const contentResponse = await chrome.tabs.sendMessage(tabs[0].id, { action: 'ping' })
          this.contentConnection = contentResponse?.success || false
        } catch (error) {
          this.contentConnection = false
        }
      }
    } catch (error) {
      console.error('Failed to setup connections:', error)
    }
  }

  startMonitoring () {
    // Listen for runtime messages
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      if (message.type === 'DEBUG_LOG') {
        this.handleNewLog(message.logEntry)
      }
    })
  }

  async loadExtensionInfo () {
    try {
      const manifest = chrome.runtime.getManifest()

      // Update extension info
      document.getElementById('extensionVersion').textContent = manifest.version
      document.getElementById('extensionId').textContent = chrome.runtime.id

      // Get platform info
      if (chrome.runtime.getPlatformInfo) {
        const platformInfo = await chrome.runtime.getPlatformInfo()
        document.getElementById('platformInfo').textContent =
          `${platformInfo.os} ${platformInfo.arch}`
      }

      // Get active tab info
      const tabs = await chrome.tabs.query({ active: true, currentWindow: true })
      if (tabs[0]) {
        const tab = tabs[0]
        document.getElementById('activeTab').textContent =
          `${tab.title?.substring(0, 30)}...`
      }
    } catch (error) {
      console.error('Failed to load extension info:', error)
    }
  }

  async refreshStatus () {
    // Update extension status
    this.updateExtensionStatus()

    // Update background status
    this.updateBackgroundStatus()

    // Update content status
    this.updateContentStatus()

    // Update storage usage
    this.updateStorageUsage()
  }

  updateExtensionStatus () {
    const statusIndicator = document.getElementById('extensionStatus')
    const statusText = document.getElementById('extensionStatusText')

    const isValid = !!(chrome && chrome.runtime && chrome.runtime.id)

    if (isValid) {
      statusIndicator.className = 'status-indicator status-online'
      statusText.textContent = 'Extension context valid'
    } else {
      statusIndicator.className = 'status-indicator status-offline'
      statusText.textContent = 'Extension context invalid'
    }
  }

  async updateBackgroundStatus () {
    const statusElement = document.getElementById('backgroundStatus')

    try {
      const response = await chrome.runtime.sendMessage({ action: 'ping' })
      if (response?.success) {
        statusElement.textContent = 'Connected and responding'
        statusElement.style.color = '#27ae60'
      } else {
        statusElement.textContent = 'Not responding'
        statusElement.style.color = '#e74c3c'
      }
    } catch (error) {
      statusElement.textContent = 'Connection failed'
      statusElement.style.color = '#e74c3c'
    }
  }

  async updateContentStatus () {
    const statusElement = document.getElementById('contentStatus')

    try {
      const tabs = await chrome.tabs.query({ active: true, currentWindow: true })
      if (tabs[0]) {
        const response = await chrome.tabs.sendMessage(tabs[0].id, { action: 'ping' })
        if (response?.success) {
          statusElement.textContent = 'Active and responding'
          statusElement.style.color = '#27ae60'
        } else {
          statusElement.textContent = 'Not responding'
          statusElement.style.color = '#e74c3c'
        }
      } else {
        statusElement.textContent = 'No active tab'
        statusElement.style.color = '#f39c12'
      }
    } catch (error) {
      statusElement.textContent = 'Not injected or not Gmail'
      statusElement.style.color = '#95a5a6'
    }
  }

  async updateStorageUsage () {
    const statusElement = document.getElementById('storageUsage')

    try {
      const usage = await chrome.storage.local.getBytesInUse()
      const mbUsage = (usage / 1024 / 1024).toFixed(2)
      const quota = chrome.storage.local.QUOTA_BYTES
      const percentUsed = ((usage / quota) * 100).toFixed(1)

      statusElement.textContent = `${mbUsage} MB (${percentUsed}%)`

      if (percentUsed > 80) {
        statusElement.style.color = '#e74c3c'
      } else if (percentUsed > 60) {
        statusElement.style.color = '#f39c12'
      } else {
        statusElement.style.color = '#27ae60'
      }
    } catch (error) {
      statusElement.textContent = 'Unable to check'
      statusElement.style.color = '#95a5a6'
    }
  }

  handleNewLog (logEntry) {
    this.addLogToViewer(logEntry)
  }

  addLogToViewer (logEntry) {
    const logViewer = document.getElementById('logViewer')
    const logElement = document.createElement('div')
    logElement.className = `log-entry log-${logEntry.level}`

    const timestamp = new Date(logEntry.timestamp).toLocaleTimeString()
    logElement.textContent = `[${timestamp}] ${logEntry.component}: ${logEntry.message}`

    logViewer.appendChild(logElement)

    // Keep only last 100 log entries
    while (logViewer.children.length > 100) {
      logViewer.removeChild(logViewer.firstChild)
    }

    // Auto-scroll to bottom
    logViewer.scrollTop = logViewer.scrollHeight
  }

  async refreshLogs () {
    const logViewer = document.getElementById('logViewer')
    logViewer.innerHTML = '<div class="loading">Loading logs</div>'

    try {
      const logs = await this.getLogs()
      logViewer.innerHTML = ''

      if (logs.length === 0) {
        logViewer.innerHTML = '<div style="text-align: center; color: #666;">No logs available</div>'
        return
      }

      // Show last 50 logs
      const recentLogs = logs.slice(-50)
      recentLogs.forEach(log => {
        this.addLogToViewer(log)
      })
    } catch (error) {
      logViewer.innerHTML = '<div style="color: #e74c3c;">Failed to load logs</div>'
      console.error('Failed to refresh logs:', error)
    }
  }

  async getLogs () {
    return new Promise((resolve) => {
      chrome.storage.local.get(['debug_logs'], (result) => {
        resolve(result.debug_logs || [])
      })
    })
  }
}

// Global functions for button handlers
async function refreshStatus () {
  if (window.devTools) {
    await window.devTools.refreshStatus()
  }
}

async function openDebugDashboard () {
  chrome.tabs.create({ url: chrome.runtime.getURL('debug.html') })
}

async function exportLogs () {
  try {
    const logs = await window.devTools.getLogs()
    const exportData = {
      timestamp: new Date().toISOString(),
      version: chrome.runtime.getManifest().version,
      extensionId: chrome.runtime.id,
      logs
    }

    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' })
    const url = URL.createObjectURL(blob)

    const a = document.createElement('a')
    a.href = url
    a.download = `devtools-logs-${new Date().toISOString().split('T')[0]}.json`
    a.click()

    URL.revokeObjectURL(url)
  } catch (error) {
    console.error('Failed to export logs:', error)
  }
}

async function testGmailIntegration () {
  try {
    const tabs = await chrome.tabs.query({ active: true, currentWindow: true })
    if (tabs[0]) {
      // Test if Gmail is detected
      const response = await chrome.tabs.sendMessage(tabs[0].id, {
        action: 'testGmailIntegration'
      })

      if (response?.success) {
        alert('Gmail integration test successful!')
      } else {
        alert('Gmail integration test failed: ' + (response?.error || 'Unknown error'))
      }
    } else {
      alert('No active tab found')
    }
  } catch (error) {
    alert('Failed to test Gmail integration: ' + error.message)
  }
}

async function clearAllLogs () {
  if (confirm('Are you sure you want to clear all logs?')) {
    try {
      await chrome.storage.local.set({ debug_logs: [] })
      await window.devTools.refreshLogs()
      alert('All logs cleared successfully')
    } catch (error) {
      alert('Failed to clear logs: ' + error.message)
    }
  }
}

async function simulateError () {
  try {
    await chrome.runtime.sendMessage({ action: 'simulateError' })
    alert('Error simulated - check logs for details')
  } catch (error) {
    alert('Failed to simulate error: ' + error.message)
  }
}

async function refreshLogs () {
  if (window.devTools) {
    await window.devTools.refreshLogs()
  }
}

// Initialize DevTools when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  window.devTools = new ExtensionDevTools()
})

// Handle DevTools panel creation
if (chrome.devtools) {
  chrome.devtools.panels.create(
    'Email Unsubscriber',
    'icon.png',
    'devtools.html'
  )
}
