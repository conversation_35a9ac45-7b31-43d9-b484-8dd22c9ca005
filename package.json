{"name": "email-unsubscriber", "version": "1.0.0", "description": "Privacy-focused Chrome extension for managing email subscriptions in Gmail", "main": "background.js", "scripts": {"dev": "webpack --mode development --watch", "build": "webpack --mode production", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint *.js --ext .js", "lint:fix": "eslint *.js --ext .js --fix", "clean": "rm -rf dist/", "zip": "npm run build && cd dist && zip -r ../email-unsubscriber.zip .", "validate": "npm run lint && npm run test", "production": "node production-deploy.js", "restore-dev": "git checkout -- popup.js popup.html manifest.json || echo 'No git repository or changes to restore'"}, "keywords": ["chrome-extension", "gmail", "email", "unsubscribe", "privacy", "productivity"], "author": "Email Unsubscriber", "license": "MIT", "devDependencies": {"@babel/core": "^7.22.0", "@babel/preset-env": "^7.22.0", "babel-loader": "^9.1.0", "clean-webpack-plugin": "^4.0.0", "copy-webpack-plugin": "^11.0.0", "css-loader": "^6.8.0", "eslint": "^8.42.0", "eslint-config-standard": "^17.1.0", "eslint-plugin-import": "^2.27.0", "eslint-plugin-n": "^16.0.0", "eslint-plugin-promise": "^6.1.0", "html-webpack-plugin": "^5.5.0", "jest": "^27.5.1", "jest-chrome": "^0.8.0", "jest-environment-jsdom": "^27.5.1", "mini-css-extract-plugin": "^2.7.0", "style-loader": "^3.3.0", "webpack": "^5.88.0", "webpack-cli": "^5.1.0"}, "dependencies": {"dompurify": "^3.0.0"}, "jest": {"testEnvironment": "jsdom", "setupFilesAfterEnv": ["jest-chrome/object"], "collectCoverageFrom": ["*.js", "!*.test.js", "!webpack.config.js"], "coverageThreshold": {"global": {"branches": 80, "functions": 80, "lines": 80, "statements": 80}}}, "eslintConfig": {"extends": ["standard"], "env": {"browser": true, "es2021": true, "jest": true, "webextensions": true}, "globals": {"chrome": "readonly", "DOMPurify": "readonly"}, "rules": {"no-console": "warn", "prefer-const": "error", "no-var": "error"}}, "browserslist": ["Chrome >= 88"]}