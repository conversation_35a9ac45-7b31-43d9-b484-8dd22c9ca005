* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Arial, sans-serif;
    font-size: 14px;
    line-height: 1.4;
    color: #333;
    background-color: #ffffff;
}

.container {
    width: 350px;
    min-height: 400px;
    display: flex;
    flex-direction: column;
    position: relative;
}

.header {
    background: linear-gradient(135deg, #4285f4 0%, #34a853 100%);
    color: white;
    padding: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.title {
    font-size: 18px;
    font-weight: 600;
    margin: 0;
}

.version {
    font-size: 12px;
    opacity: 0.8;
}

.main-content {
    flex: 1;
    padding: 20px;
}

.stats-section {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 20px;
}

.stat-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 12px;
}

.stat-label {
    font-weight: 500;
    color: #5f6368;
}

.stat-value {
    font-size: 24px;
    font-weight: 600;
    color: #4285f4;
}

.stat-limit {
    font-size: 14px;
    color: #5f6368;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: #e8eaed;
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: #4CAF50;
    transition: width 0.3s ease, background-color 0.3s ease;
    border-radius: 4px;
}

.usage-details {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 12px;
    font-size: 12px;
    color: #5f6368;
}

.usage-info {
    font-weight: 500;
}

.reset-info {
    font-size: 11px;
    opacity: 0.8;
}

.upgrade-section {
    background: linear-gradient(135deg, #ff6b6b 0%, #ff8e8e 100%);
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 20px;
    text-align: center;
}

.upgrade-message h3 {
    color: white;
    font-size: 16px;
    margin-bottom: 8px;
}

.upgrade-message p {
    color: rgba(255, 255, 255, 0.9);
    font-size: 13px;
    margin-bottom: 16px;
}

.upgrade-btn {
    background: white;
    color: #ff6b6b;
    border: none;
    padding: 10px 20px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
}

.upgrade-btn:hover {
    background: #f8f9fa;
    transform: translateY(-1px);
}

.actions-section {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-bottom: 20px;
}

.action-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 12px 16px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    min-height: 44px;
}

.action-btn.primary {
    background: #4285f4;
    color: white;
}

.action-btn.primary:hover {
    background: #3367d6;
    transform: translateY(-1px);
}

.action-btn.secondary {
    background: #f8f9fa;
    color: #5f6368;
    border: 1px solid #dadce0;
}

.action-btn.secondary:hover {
    background: #e8eaed;
    transform: translateY(-1px);
}

.action-btn:active {
    transform: translateY(0);
}

.action-btn:disabled {
    background: #f1f3f4;
    color: #9aa0a6;
    cursor: not-allowed;
    transform: none;
}

.action-btn:disabled:hover {
    background: #f1f3f4;
    transform: none;
}

.btn-icon {
    font-size: 16px;
}

.status-section {
    background: #f8f9fa;
    border-radius: 6px;
    padding: 12px;
    margin-bottom: 20px;
}

.status-item {
    font-size: 13px;
    color: #5f6368;
    text-align: center;
}

.status-item.success {
    color: #137333;
    background: #e6f4ea;
    padding: 8px;
    border-radius: 4px;
}

.status-item.error {
    color: #d93025;
    background: #fce8e6;
    padding: 8px;
    border-radius: 4px;
}

.status-item.warning {
    color: #e8710a;
    background: #fef7e0;
    padding: 8px;
    border-radius: 4px;
}

.status-item.info {
    color: #1a73e8;
    background: #e8f0fe;
    padding: 8px;
    border-radius: 4px;
}

.status-item.loading {
    color: #5f6368;
    background: #f1f3f4;
    padding: 8px;
    border-radius: 4px;
}

.footer {
    border-top: 1px solid #e8eaed;
    padding: 16px;
    background: #f8f9fa;
}

.footer-links {
    display: flex;
    justify-content: space-around;
    gap: 16px;
}

.footer-links a {
    color: #5f6368;
    text-decoration: none;
    font-size: 12px;
    transition: color 0.2s ease;
}

.footer-links a:hover {
    color: #4285f4;
}

/* Responsive adjustments */
@media (max-width: 400px) {
    .container {
        width: 100vw;
        min-width: 300px;
    }
    
    .main-content {
        padding: 16px;
    }
    
    .action-btn {
        padding: 10px 14px;
        font-size: 13px;
    }
}

/* User Feedback System Styles */
.feedback-section {
    margin-top: 16px;
    padding: 16px;
    background-color: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e8eaed;
}

.feedback-header h3 {
    font-size: 16px;
    font-weight: 600;
    color: #3c4043;
    margin-bottom: 4px;
}

.feedback-header p {
    font-size: 13px;
    color: #5f6368;
    margin-bottom: 12px;
}

.feedback-actions {
    display: flex;
    gap: 8px;
    margin-bottom: 12px;
}

.feedback-btn {
    flex: 1;
    padding: 8px 12px;
    border: none;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
}

.feedback-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.report-btn {
    background-color: #ea4335;
    color: white;
}

.report-btn:hover {
    background-color: #d93025;
}

.override-btn {
    background-color: #1a73e8;
    color: white;
}

.override-btn:hover {
    background-color: #1557b0;
}

.feedback-info {
    font-size: 12px;
    color: #5f6368;
    background-color: white;
    padding: 8px;
    border-radius: 4px;
    border: 1px solid #e8eaed;
}

.feedback-info p {
    margin: 2px 0;
}

.feedback-info span {
    font-weight: 500;
    color: #3c4043;
}

/* Manual Override Modal */
.override-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal-content {
    background-color: white;
    padding: 24px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    width: 300px;
    max-width: 90%;
}

.modal-content h3 {
    font-size: 18px;
    font-weight: 600;
    color: #3c4043;
    margin-bottom: 8px;
}

.modal-content p {
    font-size: 14px;
    color: #5f6368;
    margin-bottom: 16px;
}

.trust-slider-container {
    margin-bottom: 20px;
}

.trust-slider-container label {
    display: block;
    font-size: 14px;
    font-weight: 500;
    color: #3c4043;
    margin-bottom: 8px;
}

.trust-slider {
    width: 100%;
    height: 6px;
    border-radius: 3px;
    background: #e8eaed;
    outline: none;
    margin-bottom: 8px;
    -webkit-appearance: none;
}

.trust-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #1a73e8;
    cursor: pointer;
}

.trust-slider::-moz-range-thumb {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #1a73e8;
    cursor: pointer;
    border: none;
}

.trust-labels {
    display: flex;
    justify-content: space-between;
    font-size: 12px;
    color: #5f6368;
}

.modal-actions {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
}

.modal-btn {
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.cancel-btn {
    background-color: #f8f9fa;
    color: #3c4043;
    border: 1px solid #dadce0;
}

.cancel-btn:hover {
    background-color: #e8eaed;
}

.confirm-btn {
    background-color: #1a73e8;
    color: white;
}

.confirm-btn:hover {
    background-color: #1557b0;
}

/* Bulk Scan Section */
.bulk-scan-section {
    margin-top: 16px;
    padding: 16px;
    background-color: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e8eaed;
}

.bulk-scan-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.bulk-scan-header h3 {
    font-size: 16px;
    font-weight: 600;
    color: #3c4043;
    margin: 0;
}

.bulk-scan-controls {
    display: flex;
    gap: 8px;
}

.control-btn {
    padding: 6px 12px;
    border: 1px solid #dadce0;
    border-radius: 4px;
    background-color: white;
    color: #3c4043;
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.control-btn:hover {
    background-color: #f8f9fa;
    border-color: #5f6368;
}

.bulk-progress-bar {
    width: 100%;
    height: 8px;
    background-color: #e8eaed;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 12px;
}

.bulk-progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #4285f4 0%, #34a853 100%);
    width: 0%;
    transition: width 0.5s ease-out;
    border-radius: 4px;
    min-width: 0;
    display: block;
}

.bulk-progress-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 12px;
    color: #5f6368;
}

/* Bulk Scan Results Completion UI */
.bulk-scan-results {
    margin-top: 16px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e8eaed;
}

.results-summary {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 16px;
    margin-bottom: 20px;
    text-align: center;
}

.result-stat {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.stat-number {
    font-size: 24px;
    font-weight: 600;
    color: #1976d2;
    line-height: 1;
    margin-bottom: 4px;
}

.stat-label {
    font-size: 11px;
    color: #5f6368;
    text-transform: uppercase;
    font-weight: 500;
    letter-spacing: 0.5px;
}

.next-steps h4 {
    margin: 0 0 12px 0;
    font-size: 14px;
    font-weight: 600;
    color: #202124;
}

.next-steps-buttons {
    display: flex;
    gap: 8px;
    margin-bottom: 12px;
}

.next-steps-buttons .action-btn {
    flex: 1;
    font-size: 12px;
    padding: 8px 12px;
}

.next-steps-description {
    margin: 0;
    font-size: 11px;
    color: #5f6368;
    line-height: 1.4;
    text-align: center;
}

.progress-stats {
    font-weight: 500;
    color: #3c4043;
}

.progress-details {
    font-size: 11px;
    color: #5f6368;
}

/* Dashboard Button Enhancement */
.action-btn.dashboard {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.action-btn.dashboard:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

/* Bulk Scan Button */
.action-btn.bulk-scan {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
}

.action-btn.bulk-scan:hover {
    background: linear-gradient(135deg, #e681f0 0%, #e84965 100%);
}

.action-btn.bulk-scan:disabled {
    background: #dadce0;
    color: #5f6368;
    cursor: not-allowed;
}

.action-btn.bulk-scan:disabled:hover {
    transform: none;
    box-shadow: none;
}

/* Subscription tier badge styling */
.tier-badge {
    display: inline-block;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-top: 4px;
}

.tier-badge.tier-free {
    background: #e8f5e8;
    color: #2e7d32;
    border: 1px solid #a5d6a7;
}

.tier-badge.tier-premium {
    background: #e3f2fd;
    color: #1565c0;
    border: 1px solid #90caf9;
}

.tier-badge.tier-unlimited {
    background: linear-gradient(45deg, #ffd700, #ff8c00);
    color: #fff;
    border: 1px solid #ff8c00;
    box-shadow: 0 2px 4px rgba(255, 140, 0, 0.3);
}

.subscription-info {
    display: flex;
    justify-content: center;
    margin-top: 8px;
}

.subscription-status {
    font-size: 11px;
    color: #888;
    font-weight: 500;
    margin-left: auto;
}

/* Testing Section Styles */
.testing-section {
    background: #fff3cd;
    border: 2px dashed #ffc107;
    border-radius: 8px;
    padding: 16px;
    margin: 16px 0;
}

.testing-header h3 {
    margin: 0 0 4px 0;
    font-size: 14px;
    color: #856404;
}

.testing-header p {
    margin: 0 0 12px 0;
    font-size: 11px;
    color: #856404;
    opacity: 0.8;
}

.testing-buttons {
    display: grid;
    grid-template-columns: 1fr;
    gap: 8px;
    margin-bottom: 12px;
}

.test-btn {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    background: white;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 12px;
    font-weight: 500;
}

.test-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.test-btn.free-btn {
    border-color: #28a745;
    color: #28a745;
}

.test-btn.free-btn:hover {
    background: #28a745;
    color: white;
}

.test-btn.premium-btn {
    border-color: #007bff;
    color: #007bff;
}

.test-btn.premium-btn:hover {
    background: #007bff;
    color: white;
}

.test-btn.unlimited-btn {
    border-color: #ffc107;
    color: #856404;
    background: linear-gradient(45deg, #fff9e6, #fffbf0);
}

.test-btn.unlimited-btn:hover {
    background: linear-gradient(45deg, #ffc107, #ffb300);
    color: white;
}

.tier-icon {
    font-size: 14px;
    margin-right: 8px;
}

.test-btn small {
    font-size: 10px;
    opacity: 0.7;
    font-weight: normal;
}

.testing-actions {
    display: flex;
    gap: 6px;
    flex-wrap: wrap;
}

.test-action-btn {
    flex: 1;
    padding: 6px 8px;
    font-size: 11px;
    border: 1px solid #6c757d;
    border-radius: 4px;
    background: white;
    color: #6c757d;
    cursor: pointer;
    transition: all 0.2s ease;
}

.test-action-btn:hover {
    background: #6c757d;
    color: white;
}

.test-btn.active {
    background: #28a745;
    color: white;
    border-color: #28a745;
}

.test-btn.premium-btn.active {
    background: #007bff;
    border-color: #007bff;
}

.test-btn.unlimited-btn.active {
    background: linear-gradient(45deg, #ffc107, #ffb300);
    border-color: #ffc107;
    color: white;
}