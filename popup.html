<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Unsubscriber</title>
    <link rel="stylesheet" href="popup.css">
</head>
<body>
    <div class="container">
        <header class="header">
            <h1 class="title">Email Unsubscriber</h1>
            <div class="version">v1.0.0</div>
        </header>
        
        <main class="main-content">
            <div class="stats-section">
                <div class="stat-item">
                    <span class="stat-label">This Month</span>
                    <span class="stat-value" id="monthlyCount">0</span>
                    <span class="stat-limit" id="statLimit">/ 20</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
                <div class="usage-details">
                    <div class="usage-info">
                        <span id="remainingCount">20</span> remaining
                    </div>
                    <div class="reset-info">
                        Resets in <span id="daysUntilReset">0</span> days
                    </div>
                    <div class="subscription-info">
                        <span class="tier-badge" id="tierBadge">Free</span>
                    </div>
                </div>
            </div>
            
            <div class="upgrade-section" id="upgradeSection" style="display: none;">
                <div class="upgrade-message">
                    <h3>Monthly Limit Reached</h3>
                    <p id="upgradeLimitMessage">You've used all 20 free unsubscribes this month.</p>
                    <button class="upgrade-btn" id="upgradeBtn">
                        ✨ Upgrade to Premium
                    </button>
                </div>
            </div>
            
            <div class="actions-section">
                <button class="action-btn primary" id="scanBtn">
                    <span class="btn-icon">🔍</span>
                    Scan Current Email
                </button>
                
                <button class="action-btn secondary" id="bulkScanBtn">
                    <span class="btn-icon">🔍</span>
                    Bulk Scan Inbox
                </button>
                
                <button class="action-btn secondary" id="dashboardBtn">
                    <span class="btn-icon">📊</span>
                    View Dashboard
                </button>
            </div>
            
            <div class="bulk-scan-section" id="bulkScanSection" style="display: none;">
                <div class="bulk-scan-header">
                    <h3>Bulk Scan Progress</h3>
                    <div class="bulk-scan-controls">
                        <button class="control-btn" id="cancelBulkScan">Cancel</button>
                    </div>
                </div>
                <div class="bulk-progress-bar">
                    <div class="bulk-progress-fill" id="bulkProgressFill"></div>
                </div>
                <div class="bulk-progress-info">
                    <div class="progress-stats">
                        <span id="bulkProgressText">Preparing scan...</span>
                    </div>
                    <div class="progress-details">
                        <span id="bulkProgressDetails">0/0 emails processed</span>
                    </div>
                </div>
                
                <!-- Scan Completion Results -->
                <div class="bulk-scan-results" id="bulkScanResults" style="display: none;">
                    <div class="results-summary">
                        <div class="result-stat">
                            <span class="stat-number" id="totalEmailsScanned">0</span>
                            <span class="stat-label">Emails Scanned</span>
                        </div>
                        <div class="result-stat">
                            <span class="stat-number" id="emailsWithUnsubscribe">0</span>
                            <span class="stat-label">With Unsubscribe Links</span>
                        </div>
                        <div class="result-stat">
                            <span class="stat-number" id="uniqueSubscriptions">0</span>
                            <span class="stat-label">Unique Subscriptions</span>
                        </div>
                    </div>
                    
                    <div class="next-steps">
                        <h4>What's Next?</h4>
                        <div class="next-steps-buttons">
                            <button class="action-btn primary" id="viewDashboardBtn">
                                <span class="btn-icon">📊</span>
                                View Dashboard
                            </button>
                            <button class="action-btn secondary" id="runAnotherScanBtn">
                                <span class="btn-icon">🔍</span>
                                Scan Again
                            </button>
                        </div>
                        <p class="next-steps-description">
                            View your subscriptions in the dashboard to manage and unsubscribe from unwanted emails.
                        </p>
                    </div>
                </div>
            </div>
            
            <div class="status-section">
                <div class="status-item" id="statusMessage">
                    Ready to scan for subscription emails
                </div>
            </div>

            <!-- Testing Section - Only visible in debug mode -->
            <div class="testing-section" id="testingSection" style="display: none;">
                <div class="testing-header">
                    <h3>🧪 Testing Controls</h3>
                    <p>Change subscription tiers for testing</p>
                </div>
                <div class="testing-buttons">
                    <button class="test-btn free-btn" id="testFreeTier">
                        <span class="tier-icon">🆓</span>
                        Free Tier
                        <small>20/month</small>
                    </button>
                    <button class="test-btn premium-btn" id="testPremiumTier">
                        <span class="tier-icon">⭐</span>
                        Premium Tier
                        <small>500/month</small>
                    </button>
                    <button class="test-btn unlimited-btn" id="testUnlimitedTier">
                        <span class="tier-icon">🚀</span>
                        Unlimited Tier
                        <small>∞/month</small>
                    </button>
                </div>
                <div class="testing-actions">
                    <button class="test-action-btn" id="testIncrementUsage">
                        +1 Usage
                    </button>
                    <button class="test-action-btn" id="testResetUsage">
                        Reset Count
                    </button>
                    <button class="test-action-btn" id="testResetStorage">
                        Reset Storage
                    </button>
                    <button class="test-action-btn" id="testShowStats">
                        Show Stats
                    </button>
                </div>
            </div>

            <div class="feedback-section" id="feedbackSection" style="display: none;">
                <div class="feedback-header">
                    <h3>Trust Score Issue?</h3>
                    <p>Help us improve by reporting false positives</p>
                </div>
                <div class="feedback-actions">
                    <button class="feedback-btn report-btn" id="reportFalsePositive">
                        <span class="btn-icon">🚫</span>
                        Report False Positive
                    </button>
                    <button class="feedback-btn override-btn" id="setManualOverride">
                        <span class="btn-icon">⚙️</span>
                        Set Manual Override
                    </button>
                </div>
                <div class="feedback-info">
                    <p>Domain: <span id="currentDomain">-</span></p>
                    <p>Current Score: <span id="currentScore">-</span>%</p>
                </div>
            </div>

            <div class="override-modal" id="overrideModal" style="display: none;">
                <div class="modal-content">
                    <h3>Set Manual Trust Override</h3>
                    <p>Set a custom trust score for this domain:</p>
                    <div class="trust-slider-container">
                        <label for="trustSlider">Trust Level: <span id="trustValue">70</span>%</label>
                        <input type="range" id="trustSlider" min="0" max="100" value="70" class="trust-slider">
                        <div class="trust-labels">
                            <span>Unsafe</span>
                            <span>Safe</span>
                        </div>
                    </div>
                    <div class="modal-actions">
                        <button class="modal-btn cancel-btn" id="cancelOverride">Cancel</button>
                        <button class="modal-btn confirm-btn" id="confirmOverride">Set Override</button>
                    </div>
                </div>
            </div>
        </main>
        
        <footer class="footer">
            <div class="footer-links">
                <a href="#" id="settingsLink">Settings</a>
                <a href="#" id="helpLink">Help</a>
                <a href="#" id="privacyLink">Privacy</a>
                <span class="subscription-status" id="subscriptionStatus">Free Plan</span>
            </div>
        </footer>
    </div>
    
    <script src="popup.js"></script>
</body>
</html>