/**
 * Email Unsubscriber Popup Entry Point
 * Initializes the modular popup components
 */

// Import modular components (these will be bundled by webpack)
import { EmailUnsubscriberPopup } from './src/popup/popup-manager.js'
import { ExtensionLogger } from './src/shared/logger.js'
import { COMPONENTS } from './src/shared/constants.js'

// Initialize popup when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  try {
    ExtensionLogger.info(COMPONENTS.POPUP, 'Initializing popup')
    const emailUnsubscriberPopup = new EmailUnsubscriberPopup()
    window.emailUnsubscriberPopup = emailUnsubscriberPopup
  } catch (error) {
    ExtensionLogger.error(COMPONENTS.POPUP, 'Failed to initialize popup', error)

    // Show error in UI
    const statusEl = document.getElementById('statusMessage')
    if (statusEl) {
      statusEl.textContent = 'Failed to initialize popup: ' + error.message
      statusEl.className = 'status-item error'
    }
  }
})
