#!/usr/bin/env node

/**
 * Production Deployment Script
 *
 * This script automatically prepares the extension for production by:
 * 1. Disabling debug mode
 * 2. Removing testing sections
 * 3. Cleaning up console logs
 * 4. Creating a production build
 *
 * Usage: node production-deploy.js
 */

const fs = require('fs')
const path = require('path')
const { execSync } = require('child_process')

console.log('🚀 Starting Production Deployment Process...\n')

// File paths
const popupJsPath = path.join(__dirname, 'popup.js')
const popupHtmlPath = path.join(__dirname, 'popup.html')
const manifestPath = path.join(__dirname, 'manifest.json')

// Backup original files
console.log('📋 Creating backups...')
fs.copyFileSync(popupJsPath, popupJsPath + '.backup')
fs.copyFileSync(popupHtmlPath, popupHtmlPath + '.backup')
fs.copyFileSync(manifestPath, manifestPath + '.backup')

try {
  // Step 1: Disable debug mode in popup.js
  console.log('🔧 Disabling debug mode...')
  let popupJs = fs.readFileSync(popupJsPath, 'utf8')

  // Change debugMode = true to debugMode = false
  popupJs = popupJs.replace(/this\.debugMode = true/g, 'this.debugMode = false')

  // Remove excessive console.log statements (keep errors and warnings)
  popupJs = popupJs.replace(/console\.log\('🧪[^']*'\);?\n?/g, '')
  popupJs = popupJs.replace(/console\.log\('✅[^']*'\);?\n?/g, '')
  popupJs = popupJs.replace(/console\.log\('📊[^']*'\);?\n?/g, '')

  fs.writeFileSync(popupJsPath, popupJs)

  // Step 2: Remove testing section from HTML (optional - it's hidden when debugMode = false)
  console.log('🎨 Cleaning up HTML...')
  let popupHtml = fs.readFileSync(popupHtmlPath, 'utf8')

  // Optional: Remove testing section entirely
  const testingSectionRegex = /<!-- Testing Section[^>]*>.*?<\/div>\s*(?=<div class="feedback-section")/s
  popupHtml = popupHtml.replace(testingSectionRegex, '')

  fs.writeFileSync(popupHtmlPath, popupHtml)

  // Step 3: Update version number
  console.log('📝 Updating version number...')
  const manifest = JSON.parse(fs.readFileSync(manifestPath, 'utf8'))
  const currentVersion = manifest.version
  const versionParts = currentVersion.split('.')
  versionParts[2] = (parseInt(versionParts[2]) + 1).toString() // Increment patch version
  const newVersion = versionParts.join('.')

  manifest.version = newVersion
  fs.writeFileSync(manifestPath, JSON.stringify(manifest, null, 2))

  console.log(`📦 Version updated: ${currentVersion} → ${newVersion}`)

  // Step 4: Create production build
  console.log('🏗️  Building production version...')
  execSync('npm run build', { stdio: 'inherit' })

  // Step 5: Create production distribution folder
  console.log('📦 Creating production distribution...')
  const productionDir = path.join(__dirname, 'production-dist')

  if (fs.existsSync(productionDir)) {
    fs.rmSync(productionDir, { recursive: true })
  }

  // Copy dist folder to production-dist
  execSync('cp -r dist production-dist', { stdio: 'inherit' })

  console.log('\n✅ Production build completed successfully!')
  console.log(`\n📁 Production files are in: ${productionDir}`)
  console.log('\n🎯 Ready for Chrome Web Store upload:')
  console.log('   1. Zip the production-dist folder')
  console.log('   2. Upload to Chrome Web Store')
  console.log('   3. Test the uploaded extension thoroughly')

  console.log('\n📋 Changes made:')
  console.log('   ✅ Debug mode disabled')
  console.log('   ✅ Testing sections removed')
  console.log('   ✅ Console logs cleaned up')
  console.log(`   ✅ Version bumped to ${newVersion}`)
  console.log('   ✅ Production build created')
} catch (error) {
  console.error('\n❌ Production build failed:', error.message)

  // Restore backups on failure
  console.log('🔄 Restoring backup files...')
  fs.copyFileSync(popupJsPath + '.backup', popupJsPath)
  fs.copyFileSync(popupHtmlPath + '.backup', popupHtmlPath)
  fs.copyFileSync(manifestPath + '.backup', manifestPath)

  process.exit(1)
} finally {
  // Clean up backup files
  console.log('\n🧹 Cleaning up backup files...')
  try {
    fs.unlinkSync(popupJsPath + '.backup')
    fs.unlinkSync(popupHtmlPath + '.backup')
    fs.unlinkSync(manifestPath + '.backup')
  } catch (e) {
    // Ignore cleanup errors
  }
}

console.log('\n🎉 Production deployment process complete!')
