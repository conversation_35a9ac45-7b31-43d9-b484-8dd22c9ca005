/**
 * Error tracker module for background script
 * Extracted from background.js to provide focused error tracking functionality
 */

import { COMPONENTS } from '../shared/constants.js'
import { StorageUtils } from '../shared/storage-utils.js'

export class ErrorTracker {
  constructor(logger) {
    this.logger = logger
    this.errorReports = []
    this.maxReports = 50
  }

  /**
   * Initialize error tracking
   */
  initializeErrorTracking() {
    this.logger.info(COMPONENTS.BACKGROUND, 'Initializing error tracking')

    // Set up global error handlers
    if (typeof self !== 'undefined') {
      self.addEventListener('error', (event) => {
        this.handleGlobalError(event)
      })

      self.addEventListener('unhandledrejection', (event) => {
        this.handleUnhandledRejection(event)
      })
    }

    // Track extension lifecycle events
    if (chrome?.runtime?.onSuspend) {
      chrome.runtime.onSuspend.addListener(() => {
        this.logger.info(COMPONENTS.RUNTIME, 'Extension being suspended')
      })
    }

    if (chrome?.runtime?.onSuspendCanceled) {
      chrome.runtime.onSuspendCanceled.addListener(() => {
        this.logger.info(COMPONENTS.RUNTIME, 'Extension suspension canceled')
      })
    }

    // Track startup and shutdown
    if (chrome?.runtime?.onStartup) {
      chrome.runtime.onStartup.addListener(() => {
        this.logger.info(COMPONENTS.RUNTIME, 'Extension started')
      })
    }

    if (chrome?.runtime?.onInstalled) {
      chrome.runtime.onInstalled.addListener((details) => {
        this.logger.info(COMPONENTS.RUNTIME, 'Extension installed/updated', details)
      })
    }

    this.logger.info(COMPONENTS.BACKGROUND, 'Error tracking initialized')
  }

  /**
   * Handle global errors
   * @param {ErrorEvent} event - Error event
   */
  handleGlobalError(event) {
    const error = {
      message: event.message,
      filename: event.filename,
      lineno: event.lineno,
      colno: event.colno,
      stack: event.error?.stack,
      timestamp: new Date().toISOString(),
      type: 'global_error'
    }

    this.logger.error(COMPONENTS.RUNTIME, 'Global error caught', error)
    this.reportError(new Error(event.message), { 
      filename: event.filename,
      lineno: event.lineno,
      colno: event.colno,
      type: 'global_error'
    })
  }

  /**
   * Handle unhandled promise rejections
   * @param {PromiseRejectionEvent} event - Promise rejection event
   */
  handleUnhandledRejection(event) {
    const error = {
      reason: event.reason,
      stack: event.reason?.stack,
      timestamp: new Date().toISOString(),
      type: 'unhandled_rejection'
    }

    this.logger.error(COMPONENTS.RUNTIME, 'Unhandled promise rejection', error)
    
    // Prevent default browser behavior
    event.preventDefault()
    
    // Report as error
    const errorToReport = event.reason instanceof Error 
      ? event.reason 
      : new Error(String(event.reason))
    
    this.reportError(errorToReport, { type: 'unhandled_rejection' })
  }

  /**
   * Report error with context
   * @param {Error} error - Error object
   * @param {Object} context - Additional context
   * @returns {Object} Error report
   */
  reportError(error, context = {}) {
    const errorReport = {
      timestamp: new Date().toISOString(),
      message: error.message,
      stack: error.stack,
      name: error.name,
      context,
      extensionId: chrome?.runtime?.id,
      version: chrome?.runtime?.getManifest()?.version,
      userAgent: navigator.userAgent
    }

    this.logger.error(COMPONENTS.RUNTIME, 'Error reported', errorReport)

    // Store error report for debugging
    this.storeErrorReport(errorReport)

    return errorReport
  }

  /**
   * Store error report in storage
   * @param {Object} errorReport - Error report to store
   */
  async storeErrorReport(errorReport) {
    try {
      const stored = await StorageUtils.get(['errorReports'])
      const reports = stored.errorReports || []
      reports.push(errorReport)

      // Keep only last N error reports
      const recentReports = reports.slice(-this.maxReports)

      await StorageUtils.set({ errorReports: recentReports })
      this.errorReports = recentReports
    } catch (error) {
      this.logger.error(COMPONENTS.STORAGE, 'Failed to store error report', error)
    }
  }

  /**
   * Get error reports from storage
   * @returns {Promise<Array>} Array of error reports
   */
  async getErrorReports() {
    try {
      const stored = await StorageUtils.get(['errorReports'])
      return stored.errorReports || []
    } catch (error) {
      this.logger.error(COMPONENTS.STORAGE, 'Failed to get error reports', error)
      return []
    }
  }

  /**
   * Clear error reports
   * @returns {Promise<void>}
   */
  async clearErrorReports() {
    try {
      await StorageUtils.set({ errorReports: [] })
      this.errorReports = []
      this.logger.info(COMPONENTS.STORAGE, 'Error reports cleared')
    } catch (error) {
      this.logger.error(COMPONENTS.STORAGE, 'Failed to clear error reports', error)
    }
  }

  /**
   * Get error statistics
   * @returns {Promise<Object>} Error statistics
   */
  async getErrorStats() {
    try {
      const reports = await this.getErrorReports()
      const stats = {
        total: reports.length,
        byType: {},
        byComponent: {},
        recent: [],
        timeRange: {
          oldest: null,
          newest: null
        }
      }

      reports.forEach(report => {
        // Count by type
        const type = report.context?.type || 'unknown'
        stats.byType[type] = (stats.byType[type] || 0) + 1

        // Count by component (if available in context)
        const component = report.context?.component || 'unknown'
        stats.byComponent[component] = (stats.byComponent[component] || 0) + 1

        // Track time range
        const timestamp = new Date(report.timestamp)
        if (!stats.timeRange.oldest || timestamp < stats.timeRange.oldest) {
          stats.timeRange.oldest = timestamp
        }
        if (!stats.timeRange.newest || timestamp > stats.timeRange.newest) {
          stats.timeRange.newest = timestamp
        }
      })

      // Get recent errors (last 10)
      stats.recent = reports.slice(-10)

      return stats
    } catch (error) {
      this.logger.error(COMPONENTS.STORAGE, 'Failed to get error stats', error)
      return {
        total: 0,
        byType: {},
        byComponent: {},
        recent: [],
        timeRange: { oldest: null, newest: null }
      }
    }
  }

  /**
   * Track operation with error handling
   * @param {string} operationName - Name of the operation
   * @param {Function} operation - Operation function
   * @param {Object} context - Additional context
   * @returns {Promise<any>} Operation result
   */
  async trackOperation(operationName, operation, context = {}) {
    const startTime = Date.now()
    
    try {
      this.logger.info(COMPONENTS.RUNTIME, `Starting operation: ${operationName}`, context)
      
      const result = await operation()
      
      const duration = Date.now() - startTime
      this.logger.info(COMPONENTS.RUNTIME, `Operation completed: ${operationName}`, {
        ...context,
        duration,
        success: true
      })
      
      return result
    } catch (error) {
      const duration = Date.now() - startTime
      this.logger.error(COMPONENTS.RUNTIME, `Operation failed: ${operationName}`, {
        ...context,
        duration,
        error: error.message,
        success: false
      })
      
      this.reportError(error, {
        ...context,
        operation: operationName,
        duration
      })
      
      throw error
    }
  }

  /**
   * Create error boundary wrapper
   * @param {Function} func - Function to wrap
   * @param {string} functionName - Name of the function
   * @param {Object} defaultReturn - Default return value on error
   * @returns {Function} Wrapped function
   */
  createErrorBoundary(func, functionName, defaultReturn = null) {
    return async (...args) => {
      try {
        return await func(...args)
      } catch (error) {
        this.logger.error(COMPONENTS.RUNTIME, `Error in ${functionName}`, {
          error: error.message,
          stack: error.stack,
          args: args.length
        })
        
        this.reportError(error, {
          function: functionName,
          args: args.length
        })
        
        return defaultReturn
      }
    }
  }

  /**
   * Monitor chrome runtime errors
   */
  monitorRuntimeErrors() {
    if (chrome?.runtime?.onMessage) {
      const originalAddListener = chrome.runtime.onMessage.addListener.bind(chrome.runtime.onMessage)
      
      chrome.runtime.onMessage.addListener = (callback) => {
        const wrappedCallback = this.createErrorBoundary(callback, 'message_handler')
        return originalAddListener(wrappedCallback)
      }
    }

    if (chrome?.tabs?.onUpdated) {
      const originalAddListener = chrome.tabs.onUpdated.addListener.bind(chrome.tabs.onUpdated)
      
      chrome.tabs.onUpdated.addListener = (callback) => {
        const wrappedCallback = this.createErrorBoundary(callback, 'tab_update_handler')
        return originalAddListener(wrappedCallback)
      }
    }
  }

  /**
   * Check system health
   * @returns {Promise<Object>} System health status
   */
  async checkSystemHealth() {
    const health = {
      timestamp: new Date().toISOString(),
      chrome: {
        runtime: !!(chrome?.runtime),
        storage: !!(chrome?.storage),
        tabs: !!(chrome?.tabs),
        extensionId: chrome?.runtime?.id || null
      },
      errors: {
        total: 0,
        recent: 0
      },
      memory: {
        used: 0,
        available: 0
      }
    }

    try {
      // Check error reports
      const errorStats = await this.getErrorStats()
      health.errors.total = errorStats.total
      health.errors.recent = errorStats.recent.length

      // Check memory usage (if available)
      if (performance?.memory) {
        health.memory.used = performance.memory.usedJSHeapSize
        health.memory.available = performance.memory.totalJSHeapSize
      }

      // Test storage access
      try {
        await StorageUtils.get(['test'])
        health.storage_accessible = true
      } catch (error) {
        health.storage_accessible = false
        health.storage_error = error.message
      }

      this.logger.info(COMPONENTS.RUNTIME, 'System health check completed', health)
      return health
    } catch (error) {
      this.logger.error(COMPONENTS.RUNTIME, 'System health check failed', error)
      this.reportError(error, { operation: 'health_check' })
      return health
    }
  }

  /**
   * Setup debug tools for error tracking
   * @returns {Object} Debug tools
   */
  setupDebugTools() {
    return {
      reportError: this.reportError.bind(this),
      getErrorReports: this.getErrorReports.bind(this),
      clearErrorReports: this.clearErrorReports.bind(this),
      getErrorStats: this.getErrorStats.bind(this),
      checkSystemHealth: this.checkSystemHealth.bind(this),
      trackOperation: this.trackOperation.bind(this),
      createErrorBoundary: this.createErrorBoundary.bind(this),
      
      // Test functions
      simulateError: () => {
        throw new Error('Test error for debugging')
      },
      
      simulateAsyncError: async () => {
        await new Promise(resolve => setTimeout(resolve, 100))
        throw new Error('Test async error for debugging')
      },
      
      simulatePromiseRejection: () => {
        Promise.reject(new Error('Test promise rejection'))
      }
    }
  }
}

export default ErrorTracker