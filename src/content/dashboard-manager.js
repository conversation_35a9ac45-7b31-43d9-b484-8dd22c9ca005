/**
 * Dashboard Manager Module for Gmail Unsubscriber
 * Handles subscription dashboard display and management
 */

import { ExtensionLogger } from '../shared/logger.js';
import { COMPONENTS, UI_CONSTANTS } from '../shared/constants.js';
import { DOMUtils } from '../shared/dom-utils.js';
import { UIUtils } from '../shared/ui-utils.js';

/**
 * Manages the subscription dashboard overlay
 */
export class DashboardManager {
  constructor(parentInstance) {
    this.parent = parentInstance;
    this.activeDashboard = null;
  }

  async init() {
    ExtensionLogger.info(COMPONENTS.CONTENT, 'Dashboard manager initialized');
    
    // Inject dashboard styles if not already present
    this.injectDashboardStyles();
  }

  /**
   * Inject dashboard styles into the page
   */
  injectDashboardStyles() {
    const existingStyle = document.querySelector('#dashboard-overlay-styles');
    if (existingStyle) {
      return; // Already injected
    }

    const style = document.createElement('style');
    style.id = 'dashboard-overlay-styles';
    style.textContent = `
      .subscription-dashboard-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.8);
        z-index: 10001;
        display: flex;
        justify-content: center;
        align-items: center;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      }

      .dashboard-overlay-content {
        background: white;
        border-radius: 12px;
        max-width: 800px;
        width: 90%;
        max-height: 90vh;
        overflow-y: auto;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
      }

      .dashboard-overlay-header {
        padding: 20px;
        border-bottom: 1px solid #e1e3e1;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .dashboard-overlay-header h2 {
        margin: 0;
        font-size: 24px;
        color: #1a73e8;
      }

      .dashboard-overlay-controls {
        display: flex;
        align-items: center;
        gap: 12px;
      }

      .close-dashboard-btn {
        background: none;
        border: none;
        font-size: 20px;
        cursor: pointer;
        padding: 8px;
        border-radius: 4px;
        color: #5f6368;
        transition: background-color 0.2s;
      }

      .close-dashboard-btn:hover {
        background: #f1f3f4;
      }

      .dashboard-overlay-body {
        padding: 20px;
      }

      .loading-dashboard {
        text-align: center;
        padding: 40px;
      }

      .loading-spinner {
        border: 3px solid #f3f3f3;
        border-top: 3px solid #1a73e8;
        border-radius: 50%;
        width: 40px;
        height: 40px;
        animation: spin 1s linear infinite;
        margin: 0 auto 20px;
      }

      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }

      .empty-state {
        text-align: center;
        padding: 40px;
        color: #5f6368;
      }

      .empty-state h3 {
        margin-bottom: 12px;
        color: #202124;
      }

      .subscription-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 16px;
        margin-top: 20px;
      }

      .subscription-card {
        background: white;
        border: 1px solid #e1e3e1;
        border-radius: 8px;
        padding: 16px;
        transition: box-shadow 0.2s;
      }

      .subscription-card:hover {
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }

      .subscription-card.highlighted {
        border-color: #1a73e8;
        box-shadow: 0 2px 8px rgba(26, 115, 232, 0.2);
      }

      .provider-group .subscription-info h3 {
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 4px;
      }

      .grouped-subscriptions {
        margin: 16px 0;
        padding: 12px;
        background: #f8f9fa;
        border-radius: 8px;
      }

      .grouped-subscription {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 0;
        border-bottom: 1px solid #e8eaed;
      }

      .grouped-subscription:last-child {
        border-bottom: none;
      }

      .grouped-subscription-info {
        flex: 1;
      }

      .grouped-email {
        display: block;
        font-size: 14px;
        color: #3c4043;
        margin-bottom: 2px;
      }

      .grouped-count {
        font-size: 12px;
        color: #5f6368;
      }

      .grouped-actions {
        margin-left: 12px;
      }

      .action-btn.small {
        padding: 6px 12px;
        font-size: 12px;
        min-width: auto;
      }

      .subscription-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 12px;
      }

      .subscription-info h3 {
        margin: 0 0 4px 0;
        font-size: 16px;
        color: #202124;
      }

      .subscription-info p {
        margin: 0;
        font-size: 14px;
        color: #5f6368;
      }

      .security-indicator {
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 12px;
        font-weight: 500;
        text-transform: uppercase;
      }

      .security-safe {
        background: #e8f5e8;
        color: #137333;
      }

      .security-caution {
        background: #fef7e0;
        color: #b06000;
      }

      .security-dangerous {
        background: #fce8e6;
        color: #d93025;
      }

      .subscription-actions {
        display: flex;
        gap: 8px;
        margin-top: 12px;
      }

      .action-btn {
        padding: 8px 16px;
        border: 1px solid #dadce0;
        border-radius: 4px;
        background: white;
        cursor: pointer;
        font-size: 14px;
        transition: all 0.2s;
      }

      .action-btn:hover {
        background: #f8f9fa;
      }

      .action-btn.primary {
        background: #1a73e8;
        color: white;
        border-color: #1a73e8;
      }

      .action-btn.primary:hover {
        background: #1557b0;
      }

      .context-info {
        font-size: 12px;
        color: #5f6368;
        background: #f8f9fa;
        padding: 4px 8px;
        border-radius: 4px;
      }
    `;

    document.head.appendChild(style);
  }

  /**
   * Show subscription dashboard
   */
  async showDashboard(options = {}) {
    try {
      // Close existing dashboard if open
      if (this.activeDashboard) {
        this.closeDashboard();
      }

      // Get subscriptions from memory database
      let subscriptions = Array.from(this.parent.subscriptionDatabase.values());
      
      // If no subscriptions in memory, try to load from Chrome storage
      if (subscriptions.length === 0) {
        try {
          const { StorageUtils } = await import('../shared/storage-utils.js');
          const storedData = await StorageUtils.get('subscriptions');
          subscriptions = storedData.subscriptions || [];
          
          // Update in-memory database with loaded subscriptions
          if (subscriptions.length > 0) {
            this.parent.updateSubscriptionDatabase(subscriptions);
          }
          
          ExtensionLogger.info(COMPONENTS.CONTENT, `Loaded ${subscriptions.length} subscriptions from storage`);
        } catch (storageError) {
          ExtensionLogger.warn(COMPONENTS.CONTENT, 'Failed to load subscriptions from storage', storageError);
        }
      }
      
      // Create dashboard overlay
      const overlay = this.createDashboardOverlay(options);
      this.activeDashboard = overlay;
      
      // Load dashboard content
      await this.loadDashboardContent(overlay, subscriptions, options);
      
      // Set up event listeners
      this.setupDashboardEventListeners(overlay);
      
      document.body.appendChild(overlay);
      
      ExtensionLogger.info(COMPONENTS.CONTENT, 'Dashboard displayed', {
        subscriptionCount: subscriptions.length
      });
      
    } catch (error) {
      ExtensionLogger.error(COMPONENTS.CONTENT, 'Failed to show dashboard', error);
    }
  }

  /**
   * Create dashboard overlay structure
   */
  createDashboardOverlay(options) {
    const overlay = DOMUtils.createElement('div', { 
      className: 'subscription-dashboard-overlay',
      style: `z-index: ${UI_CONSTANTS.DASHBOARD_Z_INDEX}`
    });

    overlay.innerHTML = `
      <div class="dashboard-overlay-content">
        <div class="dashboard-overlay-header">
          <h2>📊 Subscription Dashboard</h2>
          <div class="dashboard-overlay-controls">
            ${options.fromEmail ? '<span class="context-info">Managing subscriptions from this email</span>' : ''}
            <button class="close-dashboard-btn" id="closeDashboardBtn">✕</button>
          </div>
        </div>
        
        <div class="dashboard-overlay-body" id="dashboardOverlayBody">
          <div class="loading-dashboard">
            <div class="loading-spinner"></div>
            <p>Loading your subscriptions...</p>
          </div>
        </div>
      </div>
    `;

    return overlay;
  }

  /**
   * Load dashboard content with subscriptions
   */
  async loadDashboardContent(overlay, subscriptions, options) {
    try {
      const bodyElement = overlay.querySelector('#dashboardOverlayBody');
      
      if (subscriptions.length === 0) {
        bodyElement.innerHTML = this.createEmptyStateHTML();
        return;
      }

      // Group subscriptions by provider for better UX
      const groupedSubscriptions = this.groupSubscriptionsByProvider(subscriptions);
      
      ExtensionLogger.info(COMPONENTS.CONTENT, `Grouped ${subscriptions.length} subscriptions into ${groupedSubscriptions.length} provider groups`);
      
      // Create subscription cards
      const subscriptionCards = groupedSubscriptions.map(group => 
        this.createProviderGroupHTML(group, options)
      ).join('');

      // Create stats summary
      const statsHTML = this.createStatsHTML(subscriptions);

      bodyElement.innerHTML = `
        ${statsHTML}
        <div class="subscription-grid">
          ${subscriptionCards}
        </div>
      `;

      // Store instance reference for button handlers
      window.unsubscriberInstance = this.parent;
      
    } catch (error) {
      ExtensionLogger.error(COMPONENTS.CONTENT, 'Failed to load dashboard content', error);
      
      const bodyElement = overlay.querySelector('#dashboardOverlayBody');
      bodyElement.innerHTML = this.createErrorStateHTML();
    }
  }

  /**
   * Create HTML for empty state
   */
  createEmptyStateHTML() {
    return `
      <div class="empty-state">
        <h3>No Subscriptions Found</h3>
        <p>Run a bulk scan to discover your email subscriptions</p>
        <button class="action-btn primary" onclick="window.location.reload()">
          Scan Current Emails
        </button>
      </div>
    `;
  }

  /**
   * Create HTML for error state
   */
  createErrorStateHTML() {
    return `
      <div class="empty-state">
        <h3>Error Loading Subscriptions</h3>
        <p>Please try again or run a new scan</p>
        <button class="action-btn primary" onclick="window.location.reload()">
          Refresh
        </button>
      </div>
    `;
  }

  /**
   * Create stats summary HTML
   */
  createStatsHTML(subscriptions) {
    const totalSubscriptions = subscriptions.length;
    const highRiskCount = subscriptions.filter(s => s.securityStatus === 'dangerous').length;
    const recentCount = subscriptions.filter(s => 
      Date.now() - s.lastSeen < 7 * 24 * 60 * 60 * 1000
    ).length;

    return `
      <div class="dashboard-stats" style="display: flex; gap: 16px; margin-bottom: 24px;">
        <div style="background: white; padding: 16px; border-radius: 8px; text-align: center; flex: 1;">
          <div style="font-size: 24px; font-weight: 600; color: #1a73e8;">${totalSubscriptions}</div>
          <div style="font-size: 14px; color: #5f6368;">Total Subscriptions</div>
        </div>
        <div style="background: white; padding: 16px; border-radius: 8px; text-align: center; flex: 1;">
          <div style="font-size: 24px; font-weight: 600; color: #ea4335;">${highRiskCount}</div>
          <div style="font-size: 14px; color: #5f6368;">High Risk</div>
        </div>
        <div style="background: white; padding: 16px; border-radius: 8px; text-align: center; flex: 1;">
          <div style="font-size: 24px; font-weight: 600; color: #34a853;">${recentCount}</div>
          <div style="font-size: 14px; color: #5f6368;">Recent (7 days)</div>
        </div>
      </div>
    `;
  }

  /**
   * Group subscriptions by provider for better UX
   */
  groupSubscriptionsByProvider(subscriptions) {
    const providerGroups = new Map();
    
    for (const subscription of subscriptions) {
      const domain = subscription.domain || 'unknown';
      const providerName = subscription.providerName || subscription.senderName || 'Unknown Provider';
      
      // Use domain as the grouping key for business emails
      const groupKey = domain;
      
      if (!providerGroups.has(groupKey)) {
        providerGroups.set(groupKey, {
          domain: domain,
          providerName: providerName,
          subscriptions: [],
          totalEmails: 0,
          lastSeen: 0,
          securityStatus: 'safe'
        });
      }
      
      const group = providerGroups.get(groupKey);
      group.subscriptions.push(subscription);
      group.totalEmails += subscription.emailCount || 1;
      group.lastSeen = Math.max(group.lastSeen, subscription.lastSeen || 0);
      
      // Use the worst security status in the group
      if (subscription.securityStatus === 'dangerous') {
        group.securityStatus = 'dangerous';
      } else if (subscription.securityStatus === 'caution' && group.securityStatus === 'safe') {
        group.securityStatus = 'caution';
      }
    }
    
    // Convert to array and sort by last seen date
    const groups = Array.from(providerGroups.values());
    groups.sort((a, b) => b.lastSeen - a.lastSeen);
    
    return groups;
  }

  /**
   * Create provider group HTML
   */
  createProviderGroupHTML(group, options = {}) {
    const isHighlighted = group.subscriptions.some(sub => 
      options.highlightSenderId === sub.senderId
    );
    
    // If group has only one subscription, show it as individual card
    if (group.subscriptions.length === 1) {
      return this.createSubscriptionCardHTML(group.subscriptions[0], options);
    }
    
    // Multi-subscription group
    const subscriptionsList = group.subscriptions.map(sub => `
      <div class="grouped-subscription">
        <div class="grouped-subscription-info">
          <span class="grouped-email">${sub.senderEmail}</span>
          <span class="grouped-count">${sub.emailCount || 1} emails</span>
        </div>
        <div class="grouped-actions">
          <button class="action-btn small" onclick="window.unsubscriberInstance.handleUnsubscribe('${sub.senderId}')">
            Unsubscribe
          </button>
        </div>
      </div>
    `).join('');
    
    return `
      <div class="subscription-card provider-group ${isHighlighted ? 'highlighted' : ''}" data-provider="${group.domain}">
        <div class="subscription-header">
          <div class="subscription-info">
            <h3>${group.providerName}</h3>
            <p>${group.domain}</p>
            <p style="font-size: 12px; margin-top: 4px;">
              ${group.subscriptions.length} subscriptions • ${group.totalEmails} total emails
            </p>
          </div>
          <div class="security-indicator security-${group.securityStatus}">
            ${group.securityStatus.replace('_', ' ')}
          </div>
        </div>
        
        <div class="grouped-subscriptions">
          ${subscriptionsList}
        </div>
        
        <div class="subscription-actions">
          <button class="action-btn primary" onclick="window.unsubscriberInstance.handleBulkUnsubscribe('${group.domain}')">
            Unsubscribe All
          </button>
          <button class="action-btn" onclick="window.unsubscriberInstance.showProviderDetails('${group.domain}')">
            Details
          </button>
        </div>
      </div>
    `;
  }

  /**
   * Create subscription card HTML
   */
  createSubscriptionCardHTML(subscription, options) {
    const isHighlighted = options.highlightSenderId === subscription.senderId;
    
    const securityAnalysisHTML = options.showSecurityAnalysis && subscription.needsSecurityAnalysis
      ? `<div class="security-analysis">
          <p>This subscription has potentially risky unsubscribe links.</p>
          <button class="action-btn" onclick="window.unsubscriberInstance.showSecurityDetails('${subscription.senderId}')">
            View Security Details
          </button>
        </div>`
      : '';
    
    const reportButtonHTML = subscription.securityStatus === 'high_risk'
      ? `<button class="action-btn" onclick="window.unsubscriberInstance.reportSuspicious('${subscription.senderId}')">
          Report
        </button>`
      : '';
    
    return `
      <div class="subscription-card ${isHighlighted ? 'highlighted' : ''}" data-sender-id="${subscription.senderId}">
        <div class="subscription-header">
          <div class="subscription-info">
            <h3>${subscription.senderName}</h3>
            <p>${subscription.senderEmail}</p>
            <p style="font-size: 12px; margin-top: 4px;">
              ${subscription.frequency} • ${subscription.emailCount} emails
            </p>
          </div>
          <div class="security-indicator security-${subscription.securityStatus}">
            ${subscription.securityStatus.replace('_', ' ')}
          </div>
        </div>
        
        ${securityAnalysisHTML}
        
        <div class="subscription-actions">
          <button class="action-btn primary" onclick="window.unsubscriberInstance.handleUnsubscribe('${subscription.senderId}')">
            Unsubscribe
          </button>
          <button class="action-btn" onclick="window.unsubscriberInstance.showSubscriptionDetails('${subscription.senderId}')">
            Details
          </button>
          ${reportButtonHTML}
        </div>
      </div>
    `;
  }

  /**
   * Set up dashboard event listeners
   */
  setupDashboardEventListeners(overlay) {
    // Close button
    const closeBtn = overlay.querySelector('#closeDashboardBtn');
    closeBtn.addEventListener('click', () => {
      this.closeDashboard();
    });

    // Close on escape key
    document.addEventListener('keydown', this.handleKeyDown = (e) => {
      if (e.key === 'Escape') {
        this.closeDashboard();
      }
    });

    // Close on overlay click (outside content)
    overlay.addEventListener('click', (e) => {
      if (e.target === overlay) {
        this.closeDashboard();
      }
    });
  }

  /**
   * Close dashboard and clean up
   */
  closeDashboard() {
    try {
      if (this.activeDashboard) {
        if (this.activeDashboard.parentNode) {
          this.activeDashboard.parentNode.removeChild(this.activeDashboard);
        }
        this.activeDashboard = null;
      }
      
      // Remove global reference
      window.unsubscriberInstance = null;
      
      // Remove event listener
      document.removeEventListener('keydown', this.handleKeyDown);
      
      ExtensionLogger.info(COMPONENTS.CONTENT, 'Dashboard closed');
    } catch (error) {
      ExtensionLogger.error(COMPONENTS.CONTENT, 'Failed to close dashboard', error);
    }
  }

  /**
   * Clean up resources
   */
  cleanup() {
    this.closeDashboard();
    
    // Remove injected styles
    const style = document.querySelector('#dashboard-overlay-styles');
    if (style) {
      style.remove();
    }
    
    ExtensionLogger.info(COMPONENTS.CONTENT, 'Dashboard manager cleaned up');
  }
}
