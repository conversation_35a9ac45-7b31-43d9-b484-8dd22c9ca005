/**
 * Debug tools module for content script
 * Extracted from content.js to provide focused debugging functionality
 */

import { COMPONENTS, UNSUBSCRIBE_PATTERNS } from '../shared/constants.js'
import { DOMUtils } from '../shared/dom-utils.js'

export class DebugTools {
  constructor(logger) {
    this.logger = logger
    this.unsubscribePatterns = UNSUBSCRIBE_PATTERNS.COMMON_TEXTS.map(text => new RegExp(text, 'i'))
    this.dangerousPatterns = UNSUBSCRIBE_PATTERNS.SUSPICIOUS_PATTERNS.map(text => new RegExp(text, 'i'))
  }

  /**
   * Create visual debugger for highlighting elements
   * @returns {Object} Visual debugger methods
   */
  createVisualDebugger() {
    return {
      highlightDetectedEmails: () => {
        const emails = DOMUtils.querySelectorAll('[data-message-id]')
        let count = 0

        emails.forEach(email => {
          email.style.border = '2px solid #27ae60'
          email.style.boxShadow = '0 0 10px rgba(39, 174, 96, 0.3)'
          email.style.position = 'relative'

          // Add debug badge
          const badge = DOMUtils.createElement('div', {
            style: `
              position: absolute;
              top: 5px;
              right: 5px;
              background: #27ae60;
              color: white;
              padding: 2px 6px;
              border-radius: 3px;
              font-size: 12px;
              z-index: 9999;
            `
          }, '📧')
          email.appendChild(badge)
          count++
        })

        this.logger.info(COMPONENTS.GMAIL, `Highlighted ${count} detected emails`)
        return count
      },

      highlightUnsubscribeLinks: () => {
        // Use the same comprehensive pattern matching as the main scan function
        const allLinks = DOMUtils.querySelectorAll('a[href]')
        let count = 0

        allLinks.forEach(link => {
          const href = link.getAttribute('href')
          const text = DOMUtils.getTextContent(link)
          const title = link.getAttribute('title') || ''
          const parentText = link.parentElement ? DOMUtils.getTextContent(link.parentElement) : ''

          // Use the same unsubscribe patterns as the main scan function
          const matchesPattern = this.unsubscribePatterns.some(pattern => 
            pattern.test(text) || pattern.test(title) || pattern.test(href) || pattern.test(parentText)
          ) || this.isLikelyUnsubscribeLink(href, text, title, parentText)

          // Check for dangerous patterns (avoid highlighting suspicious links)
          const isDangerous = this.dangerousPatterns.some(pattern =>
            pattern.test(text) || pattern.test(title) || pattern.test(href) || pattern.test(parentText)
          )

          // Only highlight if it matches unsubscribe patterns and isn't dangerous
          if (matchesPattern && !isDangerous && this.isLegitimateUnsubscribeLink(href, text, title, parentText)) {
            link.style.backgroundColor = '#f1c40f'
            link.style.border = '2px solid #e67e22'
            link.style.padding = '2px 4px'
            link.style.borderRadius = '3px'
            link.style.position = 'relative'

            // Add debug badge
            const badge = DOMUtils.createElement('span', {
              style: `
                position: absolute;
                top: -8px;
                right: -8px;
                background: #e67e22;
                color: white;
                padding: 2px 4px;
                border-radius: 50%;
                font-size: 10px;
                z-index: 9999;
              `
            }, '🔗')
            link.appendChild(badge)

            // Add comprehensive tooltip
            link.title = `🔗 Unsubscribe Link\nText: "${text}"\nURL: ${href}`
            count++
          }
        })

        this.logger.info(COMPONENTS.EXTRACTION, `Highlighted ${count} unsubscribe links`)
        return count
      },

      showEmailStructure: () => {
        const openEmail = DOMUtils.querySelector('.ii.gt div[data-message-id]')
        if (!openEmail) {
          this.logger.warn(COMPONENTS.GMAIL, 'No open email to analyze')
          return
        }

        // Create structure overlay
        const overlay = DOMUtils.createElement('div', {
          style: `
            position: fixed;
            top: 20px;
            right: 20px;
            width: 300px;
            max-height: 500px;
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 10000;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
          `
        })

        const structure = this.analyzeEmailStructure(openEmail)
        overlay.innerHTML = `
          <h3>Email Structure</h3>
          <button onclick="this.parentElement.remove()" style="float: right;">×</button>
          <pre>${JSON.stringify(structure, null, 2)}</pre>
        `

        document.body.appendChild(overlay)
        this.logger.info(COMPONENTS.GMAIL, 'Email structure overlay displayed')
      },

      clearVisualDebugger: () => {
        // Remove all debug styling from emails
        DOMUtils.querySelectorAll('[data-message-id]').forEach(email => {
          email.style.border = ''
          email.style.boxShadow = ''
          const emailBadge = email.querySelector('div[style*="position: absolute"]')
          if (emailBadge) emailBadge.remove()
        })

        // Remove all debug styling from links (broader selector to catch all highlighted links)
        DOMUtils.querySelectorAll('a[href]').forEach(link => {
          // Check if this link has our debug styling
          if (link.style.backgroundColor === 'rgb(241, 196, 15)' || link.style.backgroundColor === '#f1c40f') {
            link.style.backgroundColor = ''
            link.style.border = ''
            link.style.padding = ''
            link.style.borderRadius = ''
            link.style.position = ''

            // Remove debug badge
            const linkBadge = link.querySelector('span[style*="position: absolute"]')
            if (linkBadge) linkBadge.remove()

            // Remove debug tooltip if it's our format
            if (link.title && link.title.startsWith('🔗 Unsubscribe Link')) {
              link.removeAttribute('title')
            }
          }
        })

        // Remove overlays
        DOMUtils.querySelectorAll('[style*="position: fixed"][style*="z-index: 10000"]').forEach(overlay => {
          overlay.remove()
        })

        this.logger.info(COMPONENTS.GMAIL, 'Visual debugger cleared')
      }
    }
  }

  /**
   * Create Gmail-specific debugger
   * @returns {Object} Gmail debugger methods
   */
  createGmailDebugger() {
    return {
      testEmailDetection: () => {
        this.logger.info(COMPONENTS.GMAIL, 'Testing email detection')

        const emails = DOMUtils.querySelectorAll('[data-message-id]')
        this.logger.info(COMPONENTS.GMAIL, `Found ${emails.length} emails with message IDs`)

        const threadRows = DOMUtils.querySelectorAll('[data-thread-id]')
        this.logger.info(COMPONENTS.GMAIL, `Found ${threadRows.length} thread rows`)

        const conversationElements = DOMUtils.querySelectorAll('.aDP, .ads, .aCS')
        this.logger.info(COMPONENTS.GMAIL, `Found ${conversationElements.length} conversation elements`)

        emails.forEach((email, index) => {
          const messageId = email.getAttribute('data-message-id')
          const threadId = email.getAttribute('data-thread-id')
          const subject = DOMUtils.getTextContent(DOMUtils.querySelector('.bog', email)) || 'No subject found'

          this.logger.debug(COMPONENTS.GMAIL, `Email ${index + 1}`, {
            messageId,
            threadId,
            subject: subject.substring(0, 100)
          })
        })

        return { emails: emails.length, threads: threadRows.length, conversations: conversationElements.length }
      },

      getCurrentEmailContent: () => {
        const openEmail = DOMUtils.querySelector('.ii.gt div[data-message-id]')
        if (!openEmail) {
          this.logger.warn(COMPONENTS.GMAIL, 'No open email found')
          return null
        }

        const messageId = openEmail.getAttribute('data-message-id')
        const content = openEmail.innerHTML

        this.logger.info(COMPONENTS.GMAIL, 'Current email content retrieved', {
          messageId,
          contentLength: content.length
        })

        return { messageId, content }
      },

      getEmailListState: () => {
        const inboxRows = DOMUtils.querySelectorAll('tr.zA')
        const selectedRows = DOMUtils.querySelectorAll('tr.zA.x7')
        const readRows = DOMUtils.querySelectorAll('tr.zA.yW')
        const unreadRows = DOMUtils.querySelectorAll('tr.zA:not(.yW)')

        const state = {
          total: inboxRows.length,
          selected: selectedRows.length,
          read: readRows.length,
          unread: unreadRows.length
        }

        this.logger.info(COMPONENTS.GMAIL, 'Email list state', state)
        return state
      },

      simulateEmailOpen: (messageId) => {
        const email = DOMUtils.querySelector(`[data-message-id="${messageId}"]`)
        if (email) {
          email.click()
          this.logger.info(COMPONENTS.GMAIL, 'Simulated email open', { messageId })
        } else {
          this.logger.warn(COMPONENTS.GMAIL, 'Email not found for simulation', { messageId })
        }
      }
    }
  }

  /**
   * Create extraction debugger
   * @returns {Object} Extraction debugger methods
   */
  createExtractionDebugger() {
    return {
      testPatternMatching: (text, patterns) => {
        const results = []
        patterns.forEach((pattern, index) => {
          const matches = text.match(pattern)
          if (matches) {
            results.push({
              patternIndex: index,
              pattern: pattern.toString(),
              matches: matches
            })
          }
        })
        
        this.logger.info(COMPONENTS.EXTRACTION, 'Pattern matching results', { results, textLength: text.length })
        return results
      },

      analyzeUnsubscribeLinks: (container) => {
        const links = DOMUtils.querySelectorAll('a[href]', container)
        const analysis = []

        links.forEach(link => {
          const href = link.getAttribute('href')
          const text = DOMUtils.getTextContent(link)
          const title = link.getAttribute('title') || ''

          const isUnsubscribe = this.unsubscribePatterns.some(pattern => 
            pattern.test(text) || pattern.test(title) || pattern.test(href)
          )

          const isDangerous = this.dangerousPatterns.some(pattern =>
            pattern.test(text) || pattern.test(title) || pattern.test(href)
          )

          if (isUnsubscribe || isDangerous) {
            analysis.push({
              href,
              text,
              title,
              isUnsubscribe,
              isDangerous,
              element: link
            })
          }
        })

        this.logger.info(COMPONENTS.EXTRACTION, 'Unsubscribe link analysis', { 
          total: links.length, 
          unsubscribe: analysis.filter(a => a.isUnsubscribe).length,
          dangerous: analysis.filter(a => a.isDangerous).length
        })

        return analysis
      },

      validateLinkSafety: (href, text, title) => {
        try {
          const url = new URL(href)
          const isHttps = url.protocol === 'https:'
          const domain = url.hostname
          const path = url.pathname
          
          const validation = {
            isHttps,
            domain,
            path,
            isValid: true,
            warnings: []
          }

          if (!isHttps) {
            validation.warnings.push('Not using HTTPS')
          }

          if (this.dangerousPatterns.some(pattern => pattern.test(text))) {
            validation.warnings.push('Dangerous pattern in text')
          }

          this.logger.info(COMPONENTS.EXTRACTION, 'Link safety validation', validation)
          return validation
        } catch (error) {
          this.logger.error(COMPONENTS.EXTRACTION, 'Invalid URL in safety validation', { href, error: error.message })
          return {
            isValid: false,
            error: error.message
          }
        }
      }
    }
  }

  /**
   * Analyze email structure for debugging
   * @param {Element} emailElement - Email element to analyze
   * @returns {Object} Email structure analysis
   */
  analyzeEmailStructure(emailElement) {
    const structure = {
      messageId: emailElement.getAttribute('data-message-id'),
      threadId: emailElement.getAttribute('data-thread-id'),
      classes: emailElement.className,
      childElements: [],
      links: [],
      images: [],
      text: DOMUtils.getTextContent(emailElement).substring(0, 500) + '...'
    }

    // Analyze child elements
    const children = emailElement.children
    for (let i = 0; i < Math.min(children.length, 10); i++) {
      const child = children[i]
      structure.childElements.push({
        tagName: child.tagName,
        className: child.className,
        textContent: DOMUtils.getTextContent(child).substring(0, 100)
      })
    }

    // Analyze links
    const links = DOMUtils.querySelectorAll('a[href]', emailElement)
    links.forEach(link => {
      structure.links.push({
        href: link.getAttribute('href'),
        text: DOMUtils.getTextContent(link),
        title: link.getAttribute('title') || ''
      })
    })

    // Analyze images
    const images = DOMUtils.querySelectorAll('img', emailElement)
    images.forEach(img => {
      structure.images.push({
        src: img.getAttribute('src'),
        alt: img.getAttribute('alt') || '',
        width: img.width,
        height: img.height
      })
    })

    return structure
  }

  /**
   * Check if link is likely an unsubscribe link
   * @param {string} href - Link URL
   * @param {string} text - Link text
   * @param {string} title - Link title
   * @param {string} parentText - Parent element text
   * @returns {boolean} True if likely unsubscribe link
   */
  isLikelyUnsubscribeLink(href, text, title, parentText) {
    const allText = [text, title, href, parentText].join(' ').toLowerCase()
    
    // Check for unsubscribe patterns
    const hasUnsubscribePattern = this.unsubscribePatterns.some(pattern => pattern.test(allText))
    
    // Check for dangerous patterns
    const hasDangerousPattern = this.dangerousPatterns.some(pattern => pattern.test(allText))
    
    return hasUnsubscribePattern && !hasDangerousPattern
  }

  /**
   * Check if unsubscribe link is legitimate
   * @param {string} href - Link URL
   * @param {string} text - Link text
   * @param {string} title - Link title
   * @param {string} parentText - Parent element text
   * @returns {boolean} True if legitimate
   */
  isLegitimateUnsubscribeLink(href, text, title, parentText) {
    try {
      const url = new URL(href)
      
      // Basic URL validation
      if (!['http:', 'https:'].includes(url.protocol)) {
        return false
      }

      // Check for suspicious patterns
      const allText = [text, title, parentText].join(' ').toLowerCase()
      const hasSuspiciousPattern = this.dangerousPatterns.some(pattern => pattern.test(allText))
      
      if (hasSuspiciousPattern) {
        return false
      }

      // Check for legitimate unsubscribe patterns
      const hasLegitimatePattern = this.unsubscribePatterns.some(pattern => 
        pattern.test(text) || pattern.test(url.pathname) || pattern.test(url.search)
      )

      return hasLegitimatePattern
    } catch (error) {
      return false
    }
  }

  /**
   * Add visual debugging indicators to elements
   * @param {Element} element - Element to add indicator to
   * @param {string} type - Type of indicator
   * @param {string} message - Message to display
   */
  addVisualDebuggingIndicators(element, type, message) {
    if (!element) return

    const colors = {
      email: '#27ae60',
      link: '#e67e22',
      suspicious: '#e74c3c',
      safe: '#2ecc71'
    }

    const indicator = DOMUtils.createElement('div', {
      style: `
        position: absolute;
        top: 0;
        right: 0;
        background: ${colors[type] || '#3498db'};
        color: white;
        padding: 2px 6px;
        border-radius: 0 0 0 4px;
        font-size: 10px;
        z-index: 9999;
        font-family: monospace;
      `
    }, message)

    element.style.position = 'relative'
    element.appendChild(indicator)
  }

  /**
   * Initialize debugging tools
   * @returns {Object} All debugging tools
   */
  initializeDebuggingTools() {
    return {
      visual: this.createVisualDebugger(),
      gmail: this.createGmailDebugger(),
      extraction: this.createExtractionDebugger(),
      addIndicators: this.addVisualDebuggingIndicators.bind(this),
      analyzeStructure: this.analyzeEmailStructure.bind(this)
    }
  }

  /**
   * Safe debug function call with error handling
   * @param {Function} debugFunction - Debug function to call
   * @param {string} functionName - Name of the function for logging
   * @param {...any} args - Arguments to pass to the function
   * @returns {any} Function result or null if error
   */
  safeDebugCall(debugFunction, functionName, ...args) {
    try {
      const result = debugFunction(...args)
      this.logger.info(COMPONENTS.RUNTIME, `Debug function ${functionName} completed successfully`)
      return result
    } catch (error) {
      this.logger.error(COMPONENTS.RUNTIME, `Debug function ${functionName} failed`, { 
        error: error.message, 
        stack: error.stack 
      })
      return null
    }
  }

  /**
   * Show debug feedback to user
   * @param {string} message - Feedback message
   * @param {string} type - Type of feedback (success, error, info)
   */
  showDebugFeedback(message, type = 'info') {
    const colors = {
      success: '#2ecc71',
      error: '#e74c3c',
      info: '#3498db',
      warning: '#f39c12'
    }

    const feedback = DOMUtils.createElement('div', {
      style: `
        position: fixed;
        top: 20px;
        left: 50%;
        transform: translateX(-50%);
        background: ${colors[type] || colors.info};
        color: white;
        padding: 10px 20px;
        border-radius: 4px;
        z-index: 10001;
        font-family: Arial, sans-serif;
        font-size: 14px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.2);
      `
    }, message)

    document.body.appendChild(feedback)

    // Auto-remove after 3 seconds
    setTimeout(() => {
      if (feedback.parentNode) {
        feedback.parentNode.removeChild(feedback)
      }
    }, 3000)
  }
}

export default DebugTools