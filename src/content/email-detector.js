/**
 * Email Detection Module for Gmail Unsubscriber
 * Handles email scanning and unsubscribe link detection
 */

import { ExtensionLogger } from '../shared/logger.js';
import { COMPONENTS, GMAIL_SELECTORS, UNSUBSCRIBE_PATTERNS } from '../shared/constants.js';
import { GmailUtils } from '../shared/gmail-utils.js';
import { StorageUtils } from '../shared/storage-utils.js';
import { MessageUtils } from '../shared/message-utils.js';

/**
 * Email detector for finding and analyzing emails
 */
export class EmailDetector {
  constructor(parentInstance) {
    this.parent = parentInstance;
    this.unsubscribePatterns = UNSUBSCRIBE_PATTERNS.COMMON_TEXTS.map(text => new RegExp(text, 'i'));
    this.dangerousPatterns = UNSUBSCRIBE_PATTERNS.SUSPICIOUS_PATTERNS.map(text => new RegExp(text, 'i'));
  }

  async init() {
    ExtensionLogger.info(COMPONENTS.CONTENT, 'Email detector initialized');
  }

  /**
   * <PERSON>an current email for unsubscribe links
   */
  async scanCurrentEmail() {
    try {
      // First check if we're in an expanded email view (conversation view)
      const expandedEmail = document.querySelector('.ii.gt');
      
      if (expandedEmail) {
        // We're in conversation view with an expanded email
        ExtensionLogger.info(COMPONENTS.CONTENT, 'Scanning expanded email in conversation view');
        return await this.scanExpandedEmail(expandedEmail);
      }

      // Check if we're in inbox list view
      const emailElements = GmailUtils.getEmailElements();
      
      if (emailElements.length === 0) {
        return { success: false, error: 'No emails found on current page' };
      }

      // If we're in inbox view, we need to find and expand the selected/current email
      let currentEmail = null;
      
      // Look for selected email (highlighted in blue)
      for (const element of emailElements) {
        if (element.classList.contains('yW') || element.classList.contains('yO')) {
          currentEmail = element;
          break;
        }
      }

      if (!currentEmail) {
        // If no selected email, use first email
        currentEmail = emailElements[0];
      }

      // If we found a list item, we need to expand it first
      if (GmailUtils.isEmailListItem(currentEmail)) {
        ExtensionLogger.info(COMPONENTS.CONTENT, 'Found email list item, expanding for content scan');
        
        // Expand the email
        const expandedContent = await this.expandAndExtractEmail(currentEmail, 0);
        
        if (!expandedContent) {
          return { success: false, error: 'Could not expand email to access content' };
        }

        // Now scan the expanded email
        const expandedEmailElement = document.querySelector('.ii.gt');
        if (expandedEmailElement) {
          return await this.scanExpandedEmail(expandedEmailElement);
        }
      }

      return { success: false, error: 'Could not find current email to scan' };

    } catch (error) {
      ExtensionLogger.error(COMPONENTS.CONTENT, 'Current email scan failed', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Scan an expanded email for unsubscribe links
   */
  async scanExpandedEmail(expandedEmailElement) {
    try {
      const emailContent = this.extractEmailContent(expandedEmailElement);
      if (!emailContent) {
        return { success: false, error: 'Could not extract email content' };
      }

      const unsubscribeLinks = this.findUnsubscribeLinks(expandedEmailElement);
      
      if (unsubscribeLinks.length === 0) {
        return { 
          success: true, 
          found: false, 
          message: 'No unsubscribe links found in current email',
          emailContent
        };
      }

      // Analyze security of found links
      const analyzedLinks = await this.analyzeUnsubscribeLinks(unsubscribeLinks);

      // Create subscription entry for current email
      const subscription = this.createSubscriptionEntry(emailContent, analyzedLinks);
      
      ExtensionLogger.info(COMPONENTS.CONTENT, `Created subscription entry for: ${subscription.senderEmail}`, {
        senderId: subscription.senderId,
        domain: subscription.domain,
        unsubscribeLinksCount: subscription.unsubscribeLinks.length
      });
      
      // Update parent's subscription database with single entry
      this.parent.updateSubscriptionDatabase([subscription]);

      // Store subscription in Chrome storage
      await this.storeSubscriptionDatabase([subscription]);

      // Track usage for URL extraction
      try {
        await MessageUtils.sendToBackground('incrementUsage');
        ExtensionLogger.info(COMPONENTS.CONTENT, 'Usage incremented for email URL extraction');
      } catch (error) {
        ExtensionLogger.warn(COMPONENTS.CONTENT, 'Failed to increment usage', error);
      }

      return {
        success: true,
        found: true,
        count: analyzedLinks.length,
        links: analyzedLinks,
        emailContent,
        senderInfo: GmailUtils.extractSenderInfo(expandedEmailElement),
        subscription
      };

    } catch (error) {
      ExtensionLogger.error(COMPONENTS.CONTENT, 'Expanded email scan failed', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Perform bulk scan of multiple emails
   */
  async bulkScanEmails(options = {}) {
    try {
      const { maxEmails = 50, includeRead = true } = options;
      
      // Debug: Analyze Gmail page structure
      const pageStructure = GmailUtils.debugGmailStructure();
      ExtensionLogger.info(COMPONENTS.CONTENT, `Gmail page structure analyzed`, pageStructure);
      
      // Get initial email elements from the page
      let emailElements = Array.from(GmailUtils.getEmailElements());
      
      // Log initial findings
      ExtensionLogger.info(COMPONENTS.CONTENT, `Initial scan found ${emailElements.length} email elements`);
      
      // Try to load more emails if we have fewer than requested
      if (emailElements.length < maxEmails && emailElements.length > 0) {
        ExtensionLogger.info(COMPONENTS.CONTENT, `Attempting to load more emails (current: ${emailElements.length}, requested: ${maxEmails})`);
        
        const totalEmails = await GmailUtils.scrollToLoadMoreEmails(2);
        emailElements = Array.from(GmailUtils.getEmailElements());
        
        ExtensionLogger.info(COMPONENTS.CONTENT, `After scrolling: ${emailElements.length} email elements found`);
      }
      
      if (emailElements.length === 0) {
        return { success: false, error: 'No emails found to scan. Make sure you are in Gmail inbox or conversation view.' };
      }

      // Check if user is in conversation view with only 1 email - suggest going to inbox
      if (emailElements.length === 1 && GmailUtils.getGmailViewType() === 'conversation') {
        return { 
          success: false, 
          error: 'You are viewing a single email conversation. Please go back to Gmail inbox to scan multiple emails.',
          suggestion: 'Click the back arrow or go to Gmail inbox to see your email list.'
        };
      }

      // Filter out read emails if requested
      if (!includeRead) {
        emailElements = emailElements.filter(el => {
          // Check if email is unread (has unread indicators)
          return el.classList.contains('zE') || 
                 el.querySelector('.yW') ||
                 el.querySelector('.zE') ||
                 !el.classList.contains('yW');
        });
      }

      // Limit to maximum emails
      emailElements = emailElements.slice(0, maxEmails);

      ExtensionLogger.info(COMPONENTS.CONTENT, `Starting bulk scan of ${emailElements.length} emails (includeRead: ${includeRead})`);

      const results = [];
      const subscriptions = new Map();
      
      // Process emails in batches to avoid overwhelming the browser
      const batchSize = 3; // Reduced batch size for better performance
      for (let i = 0; i < emailElements.length; i += batchSize) {
        const batch = emailElements.slice(i, i + batchSize);
        
        // Process batch sequentially for better reliability with email expansion
        const batchResults = [];
        
        for (let batchIndex = 0; batchIndex < batch.length; batchIndex++) {
          const emailElement = batch[batchIndex];
          const globalIndex = i + batchIndex;
          
          try {
            ExtensionLogger.info(COMPONENTS.CONTENT, `Processing email ${globalIndex + 1}/${emailElements.length}`);
            
            const result = await this.processEmailForBulkScan(emailElement, globalIndex);
            
            if (result.subscription) {
              subscriptions.set(result.subscription.senderId, result.subscription);
              ExtensionLogger.info(COMPONENTS.CONTENT, `Found subscription in email ${globalIndex + 1}: ${result.subscription.senderId}`);
            }
            
            batchResults.push(result);
            
          } catch (error) {
            ExtensionLogger.warn(COMPONENTS.CONTENT, `Error processing email ${globalIndex}`, error);
            batchResults.push({ 
              index: globalIndex, 
              error: error.message,
              success: false 
            });
          }
          
          // Small delay between individual emails for better expansion
          await new Promise(resolve => setTimeout(resolve, 200));
        }
        
        // Add batch results to main results
        results.push(...batchResults);
        
        // Progress logging
        ExtensionLogger.info(COMPONENTS.CONTENT, `Processed batch ${Math.floor(i/batchSize) + 1}/${Math.ceil(emailElements.length/batchSize)}`);
        
        // Small delay between batches
        await new Promise(resolve => setTimeout(resolve, 150));
      }

      // Update parent's subscription database
      this.parent.updateSubscriptionDatabase(Array.from(subscriptions.values()));

      // Store results
      await this.storeSubscriptionDatabase(Array.from(subscriptions.values()));

      return {
        success: true,
        totalScanned: results.length,
        subscriptionsFound: subscriptions.size,
        results,
        subscriptions: Array.from(subscriptions.values())
      };

    } catch (error) {
      ExtensionLogger.error(COMPONENTS.CONTENT, 'Bulk scan failed', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Process a single email for bulk scanning
   */
  async processEmailForBulkScan(emailElement, index) {
    try {
      let emailContent;
      let wasExpanded = false;

      // Check if this is a list item that needs expansion
      if (GmailUtils.isEmailListItem(emailElement)) {
        emailContent = await this.expandAndExtractEmail(emailElement, index);
        wasExpanded = true;
      } else {
        // This is already expanded content
        emailContent = this.extractEmailContent(emailElement);
      }

      if (!emailContent) {
        return { index, error: 'Could not extract email content', success: false };
      }

      // Find unsubscribe links in the expanded content
      let unsubscribeLinks = [];
      
      if (wasExpanded) {
        // For expanded emails, search in the expanded content
        const expandedElement = document.querySelector('.ii.gt, .a3s.aiL, .ii');
        if (expandedElement) {
          unsubscribeLinks = this.findUnsubscribeLinks(expandedElement);
        }
        
        // If no links found in expanded content, also check the original element
        if (unsubscribeLinks.length === 0) {
          unsubscribeLinks = this.findUnsubscribeLinks(emailElement);
        }
      } else {
        // For already expanded content, search directly
        unsubscribeLinks = this.findUnsubscribeLinks(emailElement);
      }
      
      if (unsubscribeLinks.length === 0) {
        return { 
          index, 
          success: true, 
          hasUnsubscribe: false,
          emailContent 
        };
      }

      // Create subscription entry
      const subscription = this.createSubscriptionEntry(emailContent, unsubscribeLinks);

      return {
        index,
        success: true,
        hasUnsubscribe: true,
        unsubscribeCount: unsubscribeLinks.length,
        subscription,
        emailContent,
        wasExpanded
      };

    } catch (error) {
      return { index, error: error.message, success: false };
    }
  }

  /**
   * Extract email content from element
   */
  extractEmailContent(emailElement) {
    try {
      const senderInfo = GmailUtils.extractSenderInfo(emailElement);
      const subject = GmailUtils.extractSubject(emailElement);
      const threadId = GmailUtils.getThreadId(emailElement);

      // Extract body content
      let bodyText = '';
      const bodySelectors = ['.ii.gt', '.adn.ads', '.ii', '.a3s.aiL', '.a3s'];
      
      for (const selector of bodySelectors) {
        const bodyElement = emailElement.querySelector(selector);
        if (bodyElement) {
          bodyText = bodyElement.textContent || bodyElement.innerText || '';
          break;
        }
      }

      return {
        sender: senderInfo,
        subject,
        body: bodyText.trim(),
        threadId,
        timestamp: Date.now(),
        element: emailElement
      };

    } catch (error) {
      ExtensionLogger.error(COMPONENTS.CONTENT, 'Failed to extract email content', error);
      return null;
    }
  }

  /**
   * Find unsubscribe links in email element
   */
  findUnsubscribeLinks(emailElement) {
    const links = [];
    
    // Look for links in the email element and its expanded content
    const searchElements = [emailElement];
    
    // Also check for expanded content in the document
    const expandedContent = document.querySelector('.ii.gt');
    if (expandedContent && expandedContent !== emailElement) {
      searchElements.push(expandedContent);
    }
    
    // Check all email content divs
    const contentDivs = document.querySelectorAll('.a3s.aiL, .ii, .adn.ads');
    for (const div of contentDivs) {
      if (!searchElements.includes(div)) {
        searchElements.push(div);
      }
    }

    for (const element of searchElements) {
      const linkElements = element.querySelectorAll('a[href]');
      
      ExtensionLogger.debug(COMPONENTS.CONTENT, `Checking ${linkElements.length} links in element`, element.className);

      for (const linkElement of linkElements) {
        const href = linkElement.href;
        const text = linkElement.textContent.trim();
        const title = linkElement.getAttribute('title') || '';
        const parentText = linkElement.parentElement ? linkElement.parentElement.textContent.trim() : '';
        
        // Skip if we already have this link
        if (links.find(link => link.href === href)) {
          continue;
        }
        
        // Enhanced unsubscribe pattern matching with more flexible regex (from working version)
        const isUnsubscribeLink = this.unsubscribePatterns.some(pattern =>
          pattern.test(text) || pattern.test(title) || pattern.test(href) || pattern.test(parentText)
        ) || this.isLikelyUnsubscribeLink(href, text, title, parentText);
        
        // Enhanced debug logging (from working version)
        if (text.toLowerCase().includes('unsubscribe') || href.toLowerCase().includes('unsubscribe') ||
            text.toLowerCase().includes('opt') || href.toLowerCase().includes('opt') ||
            text.toLowerCase().includes('manage') || href.toLowerCase().includes('preferences')) {
          ExtensionLogger.debug(COMPONENTS.CONTENT, `🔍 Found potential unsubscribe link: "${text}" | href: "${href}" | matches: ${isUnsubscribeLink}`);
        }
        
        // Check for dangerous patterns
        const isDangerous = this.dangerousPatterns.some(pattern =>
          pattern.test(text) || pattern.test(title) || pattern.test(href) || pattern.test(parentText)
        );
        
        if (isUnsubscribeLink && !isDangerous && this.isLegitimateUnsubscribeLink(href, text, title, parentText)) {
          ExtensionLogger.info(COMPONENTS.CONTENT, `Found unsubscribe link: ${text} -> ${href}`);
          ExtensionLogger.debug(COMPONENTS.CONTENT, `Match reasons: pattern match or isLikelyUnsubscribeLink`);
          
          links.push({
            href,
            text,
            title,
            parentText,
            element: linkElement,
            type: href.startsWith('mailto:') ? 'EMAIL' : 'HTTP',
            matchReason: this.unsubscribePatterns.some(pattern => pattern.test(text)) ? 'text' : 'pattern'
          });
        }
      }
    }

    // Also check for List-Unsubscribe headers and mailto links (from working version)
    const listUnsubscribeLinks = this.extractListUnsubscribeLinks(emailElement);
    links.push(...listUnsubscribeLinks);

    ExtensionLogger.info(COMPONENTS.CONTENT, `Total unsubscribe links found: ${links.length}`);
    return links;
  }

  /**
   * Extract List-Unsubscribe headers and mailto unsubscribe links (from working version)
   */
  extractListUnsubscribeLinks(container) {
    const listUnsubscribeLinks = [];
    
    // Look for mailto unsubscribe links
    const mailtoLinks = container.querySelectorAll('a[href^="mailto:"]');
    mailtoLinks.forEach(link => {
      const href = link.getAttribute('href');
      const text = link.textContent.trim();
      
      if (href.toLowerCase().includes('unsubscribe') ||
          this.unsubscribePatterns.some(pattern => pattern.test(text))) {
        listUnsubscribeLinks.push({
          href,
          text,
          title: link.getAttribute('title') || '',
          parentText: link.parentElement ? link.parentElement.textContent.trim() : '',
          element: link,
          type: 'EMAIL',
          matchReason: 'mailto'
        });
      }
    });
    
    return listUnsubscribeLinks;
  }
  
  /**
   * Check if link is in email footer area
   */
  isLinkInEmailFooter(linkElement) {
    // Look for common footer indicators
    const parentText = linkElement.parentElement?.textContent?.toLowerCase() || '';
    const containerText = linkElement.closest('div, p, td')?.textContent?.toLowerCase() || '';
    
    const footerIndicators = [
      'footer', 'unsubscribe', 'privacy', 'terms', 'contact',
      'manage preferences', 'email preferences', 'subscription',
      'opt out', 'stop receiving', 'remove me'
    ];
    
    return footerIndicators.some(indicator => 
      parentText.includes(indicator) || containerText.includes(indicator)
    );
  }
  
  /**
   * Check if link has unsubscribe context around it
   */
  hasUnsubscribeContext(linkElement) {
    // Check surrounding text for unsubscribe-related keywords
    const contextElement = linkElement.closest('p, div, td, tr') || linkElement.parentElement;
    if (!contextElement) return false;
    
    const contextText = contextElement.textContent?.toLowerCase() || '';
    
    const contextKeywords = [
      'unsubscribe', 'opt out', 'remove', 'stop emails', 'manage preferences',
      'email preferences', 'subscription', 'newsletter', 'mailing list',
      'no longer', 'don\'t want', 'turn off', 'disable', 'cancel'
    ];
    
    return contextKeywords.some(keyword => contextText.includes(keyword));
  }

  /**
   * Check if link is definitely NOT an unsubscribe link
   */
  isDefinitelyNotUnsubscribeLink(href, text) {
    const hrefLower = href.toLowerCase();
    const textLower = text.toLowerCase();
    
    // Exclude obvious non-unsubscribe patterns
    const excludePatterns = [
      // Payment and transaction links
      /transaction|payment|billing|invoice|receipt|checkout|purchase|order/i,
      // Property and product listings
      /property|apartment|house|rent|buy|listing|product|catalog/i,
      // Social media and sharing
      /facebook|twitter|instagram|linkedin|youtube|share|follow/i,
      // General website navigation
      /homepage|about|contact|support|help|faq|terms|policy/i,
      // Amazon specific patterns
      /amazon\..*\/.*transaction|amazon\..*\/pay/i,
      // Real estate specific patterns
      /nobroker\.in\/property|detail\?utm_source/i
    ];
    
    // Check URL patterns
    for (const pattern of excludePatterns) {
      if (pattern.test(hrefLower)) {
        return true;
      }
    }
    
    // Exclude tracking-only URLs (those with only tracking parameters)
    if (hrefLower.includes('utm_medium=email') && !hrefLower.includes('unsubscribe')) {
      return true;
    }
    
    // Exclude links with navigation text
    const navigationTexts = [
      'view details', 'view more', 'see more', 'learn more', 'read more',
      'get started', 'shop now', 'buy now', 'download', 'visit website',
      'home', 'main page', 'dashboard', 'account', 'profile'
    ];
    
    return navigationTexts.some(navText => textLower.includes(navText));
  }

  /**
   * Check for strong unsubscribe indicators in link context
   */
  hasStrongUnsubscribeIndicators(linkElement) {
    const containerElement = linkElement.closest('div, p, td, table') || linkElement.parentElement;
    if (!containerElement) return false;
    
    const containerText = containerElement.textContent?.toLowerCase() || '';
    
    // Strong indicators that this is actually an unsubscribe section
    const strongIndicators = [
      'unsubscribe from this list',
      'remove from mailing list',
      'stop receiving these emails',
      'manage email preferences',
      'email subscription preferences',
      'if you no longer wish to receive',
      'to opt out',
      'to remove yourself',
      'update your preferences',
      'manage your subscription'
    ];
    
    return strongIndicators.some(indicator => containerText.includes(indicator));
  }

  /**
   * Enhanced unsubscribe link detection with flexible patterns (from working version)
   */
  isLikelyUnsubscribeLink(href, text, title, parentText) {
    // Convert to lowercase for case-insensitive matching
    const lowerHref = href.toLowerCase();
    const lowerText = text.toLowerCase();
    const lowerTitle = title.toLowerCase();
    const lowerParentText = parentText.toLowerCase();
    
    // URL-based detection patterns (from working version)
    const urlPatterns = [
      /\/unsubscribe/i,
      /\/optout/i,
      /\/opt-out/i,
      /\/remove/i,
      /\/preferences/i,
      /\/settings/i,
      /\/manage/i,
      /\/stop/i,
      /\/cancel/i,
      /\/unsub/i,
      /unsubscribe/i,
      /optout/i,
      /preferences/i,
      /emailsettings/i,
      /email-settings/i,
      /mailinglist/i,
      /mailing-list/i,
      /newsletter/i,
      /communication/i,
      /notifications/i
    ];
    
    // Text-based detection patterns (from working version)
    const textPatterns = [
      'unsubscribe',
      'opt out',
      'optout',
      'opt-out',
      'remove me',
      'remove',
      'stop emails',
      'stop receiving',
      'manage preferences',
      'email preferences',
      'email settings',
      'notification settings',
      'update preferences',
      'communication preferences',
      'manage subscriptions',
      'manage subscription',
      'cancel subscription',
      'leave list',
      'remove from list',
      'turn off',
      'disable emails',
      'disable notifications',
      'stop sending',
      'no more emails',
      'unsubscribe here'
    ];
    
    // Check URL patterns
    if (urlPatterns.some(pattern => pattern.test(lowerHref))) {
      return true;
    }
    
    // Check text patterns
    if (textPatterns.some(pattern =>
      lowerText.includes(pattern) ||
      lowerTitle.includes(pattern) ||
      lowerParentText.includes(pattern)
    )) {
      return true;
    }
    
    // Check for mailto unsubscribe links
    if (lowerHref.startsWith('mailto:') && (
      lowerHref.includes('unsubscribe') ||
      lowerHref.includes('optout') ||
      lowerHref.includes('remove')
    )) {
      return true;
    }
    
    return false;
  }

  /**
   * Filter out unwanted links that match unsubscribe patterns but aren't actual unsubscribe links (from working version)
   */
  isLegitimateUnsubscribeLink(href, text, title, parentText) {
    const lowerHref = href.toLowerCase();
    const lowerText = text.toLowerCase();
    const lowerTitle = title.toLowerCase();
    
    // Filter out common non-unsubscribe links that match patterns
    const unwantedPatterns = [
      /update.*billing/i,
      /billing.*information/i,
      /payment.*method/i,
      /account.*settings/i,
      /profile.*settings/i,
      /privacy.*policy/i,
      /terms.*service/i,
      /help.*center/i,
      /customer.*support/i,
      /contact.*us/i,
      /about.*us/i,
      /our.*company/i,
      /social.*media/i,
      /follow.*us/i,
      /download.*app/i,
      /mobile.*app/i,
      /view.*in.*browser/i,
      /forward.*friend/i,
      /share.*email/i,
      /facebook|twitter|instagram|linkedin|youtube/i,
      /google.*play|app.*store/i
    ];
    
    // Check if it matches unwanted patterns
    const textContent = [text, title, parentText].join(' ').toLowerCase();
    if (unwantedPatterns.some(pattern => pattern.test(textContent) || pattern.test(lowerHref))) {
      return false;
    }
    
    // Must contain clear unsubscribe intent
    const legitUnsubscribePatterns = [
      /^unsubscribe$/i,
      /^opt[\s\-]?out$/i,
      /^remove[\s\-]?me$/i,
      /unsubscribe[\s\-]?(here|now|link)?$/i,
      /stop[\s\-]?emails?$/i,
      /cancel[\s\-]?subscription$/i,
      /list[\s\-]?unsubscribe$/i,
      /email[\s\-]?preferences$/i,
      /manage[\s\-]?subscription$/i,
      /notification[\s\-]?settings$/i
    ];
    
    const hasLegitPattern = legitUnsubscribePatterns.some(pattern =>
      pattern.test(text) || pattern.test(title)
    ) || lowerHref.includes('/unsubscribe') || lowerHref.includes('/optout') || lowerHref.includes('/opt-out');
    
    return hasLegitPattern;
  }

  /**
   * Analyze unsubscribe links for security
   */
  async analyzeUnsubscribeLinks(links) {
    const analyzedLinks = [];

    for (const link of links) {
      try {
        const analysis = await this.parent.securityAnalyzer.analyzeLink(link.href, link.text);
        
        analyzedLinks.push({
          ...link,
          trustLevel: analysis.trustLevel,
          securityStatus: analysis.status,
          securityWarnings: analysis.warnings,
          phishingCheck: analysis.phishingCheck
        });
      } catch (error) {
        ExtensionLogger.warn(COMPONENTS.CONTENT, 'Failed to analyze link security', error);
        analyzedLinks.push({
          ...link,
          trustLevel: 50,
          securityStatus: 'unknown',
          securityWarnings: [],
          phishingCheck: null
        });
      }
    }

    return analyzedLinks;
  }

  /**
   * Create subscription entry from email content and links
   */
  createSubscriptionEntry(emailContent, unsubscribeLinks) {
    const senderId = this.generateSenderId(emailContent.sender.email);
    const domain = this.extractDomainFromEmail(emailContent.sender.email);
    const providerName = this.extractProviderName(emailContent.sender.name, emailContent.sender.email, domain);
    
    return {
      senderId,
      senderName: providerName,
      senderEmail: emailContent.sender.email,
      domain: domain,
      senderDomain: domain, // For backward compatibility
      providerName: providerName, // Clear provider identification
      subject: emailContent.subject,
      unsubscribeLinks,
      lastSeen: Date.now(),
      emailCount: 1,
      frequency: 'Unknown',
      securityStatus: this.calculateOverallSecurity(unsubscribeLinks),
      needsSecurityAnalysis: unsubscribeLinks.some(link => link.trustLevel < 70)
    };
  }

  /**
   * Extract provider name from sender information with enhanced mapping
   */
  extractProviderName(senderName, senderEmail, domain) {
    ExtensionLogger.debug(COMPONENTS.CONTENT, `Extracting provider name from:`, { senderName, senderEmail, domain });
    
    // Clean up sender name
    const cleanSenderName = senderName ? senderName.trim() : '';
    
    // If sender name exists and is meaningful, use it
    if (cleanSenderName && 
        cleanSenderName !== senderEmail && 
        !cleanSenderName.includes('noreply') && 
        !cleanSenderName.includes('no-reply') &&
        !cleanSenderName.includes('@') &&
        cleanSenderName.length > 2) {
      ExtensionLogger.debug(COMPONENTS.CONTENT, `Using sender name: ${cleanSenderName}`);
      return cleanSenderName;
    }
    
    // Extract company name from domain
    if (domain && domain !== 'unknown') {
      // Enhanced domain mappings with more companies
      const domainMappings = {
        // E-commerce
        'nobroker.in': 'NoBroker',
        'nobroker.com': 'NoBroker', 
        'amazon.in': 'Amazon India',
        'amazon.com': 'Amazon',
        'flipkart.com': 'Flipkart',
        'myntra.com': 'Myntra',
        'ajio.com': 'AJIO',
        'nykaa.com': 'Nykaa',
        'shopify.com': 'Shopify',
        'ebay.com': 'eBay',
        'snapdeal.com': 'Snapdeal',
        'jabong.com': 'Jabong',
        'bigbasket.com': 'BigBasket',
        'grofers.com': 'Grofers',
        'firstcry.com': 'FirstCry',
        
        // Food delivery
        'zomato.com': 'Zomato',
        'swiggy.com': 'Swiggy',
        'ubereats.com': 'Uber Eats',
        'foodpanda.com': 'FoodPanda',
        
        // Financial services
        'paytm.com': 'Paytm',
        'phonepe.com': 'PhonePe',
        'googlepay.com': 'Google Pay',
        'razorpay.com': 'Razorpay',
        'cred.club': 'CRED',
        'slice.it': 'Slice',
        'jupiter.money': 'Jupiter',
        'navi.com': 'Navi',
        'upstox.com': 'Upstox',
        'zerodha.com': 'Zerodha',
        'groww.in': 'Groww',
        'kuvera.in': 'Kuvera',
        
        // Travel
        'makemytrip.com': 'MakeMyTrip',
        'cleartrip.com': 'Cleartrip',
        'yatra.com': 'Yatra',
        'goibibo.com': 'Goibibo',
        'redbus.in': 'RedBus',
        'uber.com': 'Uber',
        'ola.com': 'Ola',
        'rapido.bike': 'Rapido',
        'booking.com': 'Booking.com',
        'airbnb.com': 'Airbnb',
        'expedia.com': 'Expedia',
        
        // Social media
        'linkedin.com': 'LinkedIn',
        'facebook.com': 'Facebook',
        'twitter.com': 'Twitter',
        'instagram.com': 'Instagram',
        'youtube.com': 'YouTube',
        'whatsapp.com': 'WhatsApp',
        'telegram.org': 'Telegram',
        'discord.com': 'Discord',
        'reddit.com': 'Reddit',
        'quora.com': 'Quora',
        
        // Tech companies
        'google.com': 'Google',
        'microsoft.com': 'Microsoft',
        'apple.com': 'Apple',
        'amazon.com': 'Amazon',
        'netflix.com': 'Netflix',
        'spotify.com': 'Spotify',
        'dropbox.com': 'Dropbox',
        'slack.com': 'Slack',
        'zoom.us': 'Zoom',
        'notion.so': 'Notion',
        'github.com': 'GitHub',
        'gitlab.com': 'GitLab',
        'atlassian.com': 'Atlassian',
        'salesforce.com': 'Salesforce',
        'hubspot.com': 'HubSpot',
        'stripe.com': 'Stripe',
        
        // Email providers
        'gmail.com': 'Gmail',
        'outlook.com': 'Outlook',
        'yahoo.com': 'Yahoo',
        'hotmail.com': 'Hotmail',
        'icloud.com': 'iCloud',
        
        // Job portals
        'naukri.com': 'Naukri',
        'monster.com': 'Monster',
        'shine.com': 'Shine',
        'indeed.com': 'Indeed',
        'glassdoor.com': 'Glassdoor',
        'angellist.com': 'AngelList',
        'unstop.com': 'Unstop',
        
        // Education
        'udemy.com': 'Udemy',
        'coursera.org': 'Coursera',
        'edx.org': 'edX',
        'skillshare.com': 'Skillshare',
        'pluralsight.com': 'Pluralsight',
        'khanacademy.org': 'Khan Academy',
        'byjus.com': 'BYJU\'S',
        'unacademy.com': 'Unacademy',
        'vedantu.com': 'Vedantu',
        
        // News and media
        'timesofindia.com': 'Times of India',
        'hindustantimes.com': 'Hindustan Times',
        'thehindu.com': 'The Hindu',
        'ndtv.com': 'NDTV',
        'india.com': 'India.com',
        'cnn.com': 'CNN',
        'bbc.com': 'BBC',
        'reuters.com': 'Reuters',
        'medium.com': 'Medium',
        'substack.com': 'Substack',
        
        // Real estate
        'housing.com': 'Housing.com',
        'magicbricks.com': 'MagicBricks',
        '99acres.com': '99acres',
        'proptiger.com': 'PropTiger',
        'commonfloor.com': 'CommonFloor',
        
        // Healthcare
        'practo.com': 'Practo',
        'netmeds.com': 'Netmeds',
        'pharmeasy.in': 'PharmEasy',
        'apollo247.com': 'Apollo 247',
        'docprime.com': 'DocPrime',
        
        // Entertainment
        'hotstar.com': 'Hotstar',
        'voot.com': 'Voot',
        'sonyliv.com': 'SonyLIV',
        'zee5.com': 'ZEE5',
        'altbalaji.com': 'ALTBalaji',
        'primevideo.com': 'Prime Video',
        'netflix.com': 'Netflix',
        'hulu.com': 'Hulu',
        'disneyplus.com': 'Disney+',
        
        // Gaming
        'steam.com': 'Steam',
        'epicgames.com': 'Epic Games',
        'battle.net': 'Battle.net',
        'origin.com': 'Origin',
        'playstation.com': 'PlayStation',
        'xbox.com': 'Xbox',
        'nintendo.com': 'Nintendo'
      };
      
      if (domainMappings[domain]) {
        ExtensionLogger.debug(COMPONENTS.CONTENT, `Using domain mapping: ${domainMappings[domain]}`);
        return domainMappings[domain];
      }
      
      // Extract company name from domain (remove common suffixes)
      const companyName = domain
        .replace(/\.(com|in|org|net|co\.in|co\.uk|io|ai|co)$/, '')
        .replace(/[\.-]/g, ' ')
        .split(' ')
        .map(word => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' ');
        
      ExtensionLogger.debug(COMPONENTS.CONTENT, `Using extracted company name: ${companyName}`);
      return companyName;
    }
    
    // Try to extract from email prefix
    if (senderEmail && senderEmail.includes('@')) {
      const emailPrefix = senderEmail.split('@')[0];
      if (emailPrefix && 
          !emailPrefix.includes('noreply') && 
          !emailPrefix.includes('no-reply') &&
          !emailPrefix.includes('support') &&
          !emailPrefix.includes('notification') &&
          emailPrefix.length > 2) {
        const cleanPrefix = emailPrefix
          .replace(/[\.-_]/g, ' ')
          .split(' ')
          .map(word => word.charAt(0).toUpperCase() + word.slice(1))
          .join(' ');
        ExtensionLogger.debug(COMPONENTS.CONTENT, `Using email prefix: ${cleanPrefix}`);
        return cleanPrefix;
      }
    }
    
    // Fallback to email or unknown
    const fallback = senderEmail || 'Unknown Provider';
    ExtensionLogger.debug(COMPONENTS.CONTENT, `Using fallback: ${fallback}`);
    return fallback;
  }

  /**
   * Extract domain from email address
   */
  extractDomainFromEmail(email) {
    if (!email || !email.includes('@')) {
      return 'unknown';
    }
    return email.split('@')[1].toLowerCase();
  }

  /**
   * Generate unique sender ID using backup system logic
   */
  generateSenderId(email) {
    if (!email || !email.includes('@')) {
      // For unknown emails, use a consistent ID instead of timestamp
      return 'unknown_sender';
    }
    
    const [localPart, domain] = email.split('@');
    
    // Generic email providers - use email-specific IDs
    const genericDomains = ['gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com', 'aol.com'];
    
    let senderId;
    if (genericDomains.includes(domain.toLowerCase())) {
      // For personal email providers, use domain_localpart format to differentiate individual users
      senderId = `${domain}_${localPart || 'unknown'}`.replace(/[^a-zA-Z0-9_]/g, '_');
    } else {
      // For business domains, use just the domain to consolidate multiple emails from same company
      senderId = domain.replace(/[^a-zA-Z0-9_]/g, '_');
    }
    
    ExtensionLogger.debug(COMPONENTS.CONTENT, `Generated senderId: ${senderId} for email: ${email} (domain: ${domain})`);
    return senderId;
  }

  /**
   * Calculate overall security status for subscription
   */
  calculateOverallSecurity(links) {
    if (links.length === 0) return 'unknown';
    
    const avgTrustLevel = links.reduce((sum, link) => sum + (link.trustLevel || 50), 0) / links.length;
    
    if (avgTrustLevel >= 80) return 'safe';
    if (avgTrustLevel >= 60) return 'caution';
    return 'dangerous';
  }

  /**
   * Store subscription database
   */
  async storeSubscriptionDatabase(subscriptions) {
    try {
      ExtensionLogger.info(COMPONENTS.CONTENT, `Attempting to store ${subscriptions.length} subscriptions`);
      
      // Log what we're trying to store
      for (const subscription of subscriptions) {
        ExtensionLogger.debug(COMPONENTS.CONTENT, `Storing subscription: ${subscription.senderEmail} (${subscription.senderId})`);
      }
      
      // Load existing subscriptions from storage
      const existingData = await StorageUtils.get('subscriptions');
      const existingSubscriptions = existingData.subscriptions || [];
      
      ExtensionLogger.info(COMPONENTS.CONTENT, `Found ${existingSubscriptions.length} existing subscriptions in storage`);
      
      // Create a map of existing subscriptions by senderId
      const existingMap = new Map();
      for (const subscription of existingSubscriptions) {
        existingMap.set(subscription.senderId, subscription);
      }
      
      // Add new subscriptions, updating existing ones
      for (const subscription of subscriptions) {
        // Check for potential collisions
        if (existingMap.has(subscription.senderId)) {
          const existing = existingMap.get(subscription.senderId);
          if (existing.senderEmail !== subscription.senderEmail) {
            ExtensionLogger.warn(COMPONENTS.CONTENT, `🚨 COLLISION DETECTED! SenderId collision between:`, {
              existing: { email: existing.senderEmail, provider: existing.senderName },
              new: { email: subscription.senderEmail, provider: subscription.senderName },
              senderId: subscription.senderId
            });
          } else {
            ExtensionLogger.info(COMPONENTS.CONTENT, `Updating existing subscription for: ${subscription.senderEmail} (${subscription.senderName})`);
          }
        } else {
          ExtensionLogger.info(COMPONENTS.CONTENT, `Adding new subscription for: ${subscription.senderEmail} (${subscription.senderName})`);
        }
        
        existingMap.set(subscription.senderId, subscription);
      }
      
      // Convert back to array and store
      const updatedSubscriptions = Array.from(existingMap.values());
      
      // Store to both keys for dashboard compatibility
      await StorageUtils.set({ 
        subscriptions: updatedSubscriptions,
        subscriptionDatabase: updatedSubscriptions 
      });
      
      ExtensionLogger.info(COMPONENTS.CONTENT, `✅ Successfully stored ${subscriptions.length} new subscriptions, total: ${updatedSubscriptions.length}`);
      
      // Verify storage worked
      const verifyData = await StorageUtils.get(['subscriptions', 'subscriptionDatabase']);
      ExtensionLogger.info(COMPONENTS.CONTENT, `Verification: subscriptions=${verifyData.subscriptions?.length || 0}, subscriptionDatabase=${verifyData.subscriptionDatabase?.length || 0}`);
      
    } catch (error) {
      ExtensionLogger.error(COMPONENTS.CONTENT, '❌ Failed to store subscriptions', error);
      ExtensionLogger.error(COMPONENTS.CONTENT, 'Error details:', {
        message: error.message,
        stack: error.stack,
        subscriptionsLength: subscriptions.length
      });
    }
  }

  /**
   * Expand email list item and extract content
   */
  async expandAndExtractEmail(emailListItem, index) {
    try {
      ExtensionLogger.info(COMPONENTS.CONTENT, `Expanding email ${index + 1} for full content extraction`);
      
      // Store original state
      const threadId = emailListItem.getAttribute('data-thread-id') || 
                      emailListItem.getAttribute('data-legacy-thread-id');
      
      // Scroll into view first
      GmailUtils.scrollIntoView(emailListItem);
      await new Promise(resolve => setTimeout(resolve, 100));
      
      // Click to expand the email
      emailListItem.click();
      
      // Wait for Gmail to expand the email and load full content
      await new Promise(resolve => setTimeout(resolve, 1200));
      
      // Wait for content to be fully loaded by checking for links
      let attempts = 0;
      const maxAttempts = 5;
      while (attempts < maxAttempts) {
        const tempExpandedContent = document.querySelector('.ii.gt, .a3s.aiL, .ii');
        if (tempExpandedContent && tempExpandedContent.querySelectorAll('a[href]').length > 0) {
          break;
        }
        await new Promise(resolve => setTimeout(resolve, 300));
        attempts++;
      }
      
      // After expansion, look for the expanded email content
      let expandedContent = null;
      
      if (threadId) {
        // Try to find expanded content by thread ID
        expandedContent = document.querySelector(`.ii.gt[data-thread-id="${threadId}"]`) ||
                         document.querySelector(`.ii.gt[data-legacy-thread-id="${threadId}"]`);
      }
      
      // If we can't find by thread ID, look for any expanded content that appeared
      if (!expandedContent) {
        expandedContent = document.querySelector('.ii.gt') ||
                         document.querySelector('.a3s.aiL') ||
                         document.querySelector('.ii');
      }
      
      // Extract content from expanded view
      if (expandedContent) {
        ExtensionLogger.info(COMPONENTS.CONTENT, `Found expanded content for email ${index + 1}`);
        return this.extractEmailContent(expandedContent);
      } else {
        // Fallback: extract what we can from list item
        ExtensionLogger.warn(COMPONENTS.CONTENT, `Could not find expanded content for email ${index + 1}, using list item`);
        return this.extractEmailContent(emailListItem);
      }
      
    } catch (error) {
      ExtensionLogger.warn(COMPONENTS.CONTENT, `Failed to expand email ${index}`, error);
      return this.extractEmailContent(emailListItem);
    }
  }

  cleanup() {
    // Cleanup any observers or event listeners
    ExtensionLogger.info(COMPONENTS.CONTENT, 'Email detector cleaned up');
  }
}
