/**
 * Main Gmail Unsubscriber Content Script Class
 * Coordinates all content script functionality
 */

import { ExtensionLogger } from '../shared/logger.js';
import { COMPONENTS, UNSUBSCRIBE_PATTERNS } from '../shared/constants.js';
import { GmailUtils } from '../shared/gmail-utils.js';
import { MessageUtils } from '../shared/message-utils.js';
import { EmailDetector } from './email-detector.js';
import { UnsubscribeHandler } from './unsubscribe-handler.js';
import { DashboardManager } from './dashboard-manager.js';
import { SecurityAnalyzer } from './security-analyzer.js';
import { DebugTools } from './debug-tools.js';

/**
 * Main Gmail Unsubscriber Content Script
 */
export class GmailUnsubscriberContent {
  constructor() {
    ExtensionLogger.info(COMPONENTS.CONTENT, 'Content script initializing');

    this.debugMode = true; // Enable debug features
    this.unsubscribePatterns = UNSUBSCRIBE_PATTERNS.COMMON_TEXTS.map(text => new RegExp(text, 'i'));
    this.dangerousPatterns = UNSUBSCRIBE_PATTERNS.SUSPICIOUS_PATTERNS.map(text => new RegExp(text, 'i'));
    
    // Initialize components
    this.emailDetector = new EmailDetector(this);
    this.unsubscribeHandler = new UnsubscribeHandler(this);
    this.dashboardManager = new DashboardManager(this);
    this.securityAnalyzer = new SecurityAnalyzer();
    this.debugTools = new DebugTools(ExtensionLogger);
    
    // State management
    this.isScanning = false;
    this.scanResults = [];
    this.subscriptionDatabase = new Map();
    
    this.init();
  }

  async init() {
    try {
      // Wait for Gmail to load
      const gmailLoaded = await GmailUtils.waitForGmailLoad();
      if (!gmailLoaded) {
        ExtensionLogger.warn(COMPONENTS.CONTENT, 'Gmail did not load within timeout');
        return;
      }

      // Check if we're actually on Gmail
      if (!GmailUtils.isGmailPage()) {
        ExtensionLogger.info(COMPONENTS.CONTENT, 'Not on Gmail page, content script inactive');
        return;
      }

      ExtensionLogger.info(COMPONENTS.CONTENT, 'Gmail detected, initializing content script');

      // Set up message listeners
      this.setupMessageListeners();

      // Initialize components
      await this.initializeComponents();

      // Add visual debugging if enabled
      if (this.debugMode) {
        this.debugTools.addVisualDebuggingIndicators();
      }

      ExtensionLogger.info(COMPONENTS.CONTENT, 'Content script initialization complete');
    } catch (error) {
      ExtensionLogger.error(COMPONENTS.CONTENT, 'Failed to initialize content script', error);
    }
  }

  /**
   * Initialize all components
   */
  async initializeComponents() {
    try {
      await this.emailDetector.init();
      await this.unsubscribeHandler.init();
      await this.dashboardManager.init();
      
      ExtensionLogger.info(COMPONENTS.CONTENT, 'All components initialized');
    } catch (error) {
      ExtensionLogger.error(COMPONENTS.CONTENT, 'Failed to initialize components', error);
    }
  }

  /**
   * Set up Chrome extension message listeners
   */
  setupMessageListeners() {
    if (!chrome?.runtime?.onMessage) {
      ExtensionLogger.warn(COMPONENTS.CONTENT, 'Chrome runtime not available');
      return;
    }

    chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
      this.handleMessage(request, sender, sendResponse);
      return true; // Keep message channel open for async responses
    });

    ExtensionLogger.info(COMPONENTS.CONTENT, 'Message listeners set up');
  }

  /**
   * Handle incoming messages from popup and background
   */
  async handleMessage(request, sender, sendResponse) {
    try {
      ExtensionLogger.debug(COMPONENTS.CONTENT, 'Received message', request);

      // Get the action from either action or type field (for compatibility)
      const action = request.action || request.type;

      // Log the action for debugging
      ExtensionLogger.log(COMPONENTS.CONTENT, 'Log data:', request);

      switch (action) {
        case 'ping':
          sendResponse({ success: true, message: 'Content script is active' });
          break;

        case 'scanCurrentEmail':
          const scanResult = await this.scanCurrentEmail();
          sendResponse(scanResult);
          break;

        case 'bulkScanEmails':
          // Get options from either options or directly from request
          const options = request.options || {
            maxEmails: request.maxEmails || 50,
            includeRead: request.includeRead !== undefined ? request.includeRead : true
          };
          const bulkResult = await this.bulkScanEmails(options);
          sendResponse(bulkResult);
          break;

        case 'showDashboard':
          await this.dashboardManager.showDashboard(request.options);
          sendResponse({ success: true });
          break;

        case 'getSubscriptions':
          const subscriptions = await this.getStoredSubscriptions();
          sendResponse({ success: true, subscriptions });
          break;

        case 'unsubscribe':
          const unsubResult = await this.unsubscribeHandler.handleUnsubscribe(request.senderId);
          sendResponse(unsubResult);
          break;

        default:
          ExtensionLogger.warn(COMPONENTS.CONTENT, 'Unknown message action', action);
          sendResponse({ success: false, error: 'Unknown action' });
      }
    } catch (error) {
      ExtensionLogger.error(COMPONENTS.CONTENT, 'Error handling message', error);
      sendResponse({ success: false, error: error.message });
    }
  }

  /**
   * Scan current email for unsubscribe links
   */
  async scanCurrentEmail() {
    try {
      ExtensionLogger.info(COMPONENTS.CONTENT, 'Starting current email scan');
      
      if (this.isScanning) {
        return { success: false, error: 'Scan already in progress' };
      }

      this.isScanning = true;
      
      const result = await this.emailDetector.scanCurrentEmail();
      
      this.isScanning = false;
      return result;
    } catch (error) {
      this.isScanning = false;
      ExtensionLogger.error(COMPONENTS.CONTENT, 'Current email scan failed', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Perform bulk scan of emails
   */
  async bulkScanEmails(options = {}) {
    try {
      ExtensionLogger.info(COMPONENTS.CONTENT, 'Starting bulk email scan', options);
      
      if (this.isScanning) {
        return { success: false, error: 'Scan already in progress' };
      }

      this.isScanning = true;
      
      const result = await this.emailDetector.bulkScanEmails(options);
      
      this.isScanning = false;
      return result;
    } catch (error) {
      this.isScanning = false;
      ExtensionLogger.error(COMPONENTS.CONTENT, 'Bulk email scan failed', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Get stored subscriptions from local database
   */
  async getStoredSubscriptions() {
    try {
      const subscriptions = Array.from(this.subscriptionDatabase.values());
      return subscriptions;
    } catch (error) {
      ExtensionLogger.error(COMPONENTS.CONTENT, 'Failed to get subscriptions', error);
      return [];
    }
  }

  /**
   * Update subscription database
   */
  updateSubscriptionDatabase(subscriptions) {
    try {
      this.subscriptionDatabase.clear();
      
      for (const subscription of subscriptions) {
        this.subscriptionDatabase.set(subscription.senderId, subscription);
      }
      
      ExtensionLogger.info(COMPONENTS.CONTENT, `Updated subscription database with ${subscriptions.length} entries`);
    } catch (error) {
      ExtensionLogger.error(COMPONENTS.CONTENT, 'Failed to update subscription database', error);
    }
  }

  /**
   * Clean up when extension context is invalidated
   */
  cleanup() {
    try {
      ExtensionLogger.info(COMPONENTS.CONTENT, 'Cleaning up content script');
      
      // Clean up components
      if (this.emailDetector) this.emailDetector.cleanup();
      if (this.unsubscribeHandler) this.unsubscribeHandler.cleanup();
      if (this.dashboardManager) this.dashboardManager.cleanup();
      
      // Clear state
      this.isScanning = false;
      this.scanResults = [];
      this.subscriptionDatabase.clear();
      
    } catch (error) {
      ExtensionLogger.error(COMPONENTS.CONTENT, 'Error during cleanup', error);
    }
  }
}
