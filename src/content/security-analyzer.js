/**
 * Security analyzer module for email unsubscribe links
 * Extracted from content.js to provide focused security analysis functionality
 */

import { SECURITY_STATUS, SECURITY_PATTERNS } from '../shared/constants.js'
import { Validators } from '../shared/validators.js'
import { StorageUtils } from '../shared/storage-utils.js'

export class SecurityAnalyzer {
  constructor() {
    this.trustedDomains = [
      'mailchimp.com',
      'constantcontact.com',
      'campaignmonitor.com',
      'sendgrid.com',
      'mailgun.com',
      'amazonses.com',
      'mandrill.com',
      'postmarkapp.com',
      'sparkpost.com',
      'email.amazon.com',
      'unsubscribe.apple.com',
      'unsubscribe.microsoft.com',
      'unsubscribe.google.com',
      'unsubscribe.facebook.com',
      'unsubscribe.linkedin.com',
      'unsubscribe.twitter.com',
      'unsubscribe.instagram.com',
      'unsubscribe.youtube.com',
      'unsubscribe.github.com',
      'unsubscribe.slack.com'
    ]

    this.trustedDomainPatterns = [
      /\.mailchimp\.com$/,
      /\.constantcontact\.com$/,
      /\.campaignmonitor\.com$/,
      /\.sendgrid\.com$/,
      /\.mailgun\.com$/,
      /\.amazonses\.com$/,
      /\.mandrill\.com$/,
      /\.postmarkapp\.com$/,
      /\.sparkpost\.com$/,
      /unsubscribe\..*\.(com|org|net)$/
    ]

    this.maliciousDomains = [
      'suspicious-domain.com',
      'phishing-site.net',
      'fake-unsubscribe.org'
    ]

    this.emailServiceProviders = [
      'mailchimp.com',
      'constantcontact.com',
      'campaignmonitor.com',
      'sendgrid.com',
      'mailgun.com',
      'amazonses.com',
      'mandrill.com',
      'postmarkapp.com',
      'sparkpost.com'
    ]
  }

  /**
   * Calculate trust level for an unsubscribe link
   * @param {string} href - Link URL
   * @param {string} text - Link text
   * @param {string} title - Link title
   * @returns {Promise<number>} Trust score (0-100)
   */
  async calculateTrustLevel(href, text, title) {
    let trustScore = 30 // Start with base score instead of 0

    try {
      const url = new URL(href)

      // Check local blacklist first
      const blacklistEntry = await this.isInLocalBlacklist(url.hostname)
      if (blacklistEntry) {
        return 0 // Blacklisted domains get 0 trust
      }

      // Check manual overrides first
      const override = await this.getManualOverride(url.hostname)
      if (override) {
        return override.trustLevel
      }

      // Simple SSL check (no certificate validation for performance)
      if (url.protocol === 'https:') {
        trustScore += 20
      }

      // Enhanced phishing pattern detection
      const phishingCheck = this.detectPhishingPatterns(href, text, title)
      if (phishingCheck.isPhishing) {
        trustScore -= phishingCheck.riskScore
      }

      // High trust for known domains (exact match)
      if (this.trustedDomains.includes(url.hostname)) {
        trustScore += 50
      }

      // Check trusted domain patterns (for subdomains)
      const matchesTrustedPattern = this.trustedDomainPatterns.some(pattern =>
        pattern.test(url.hostname)
      )
      if (matchesTrustedPattern && !this.trustedDomains.includes(url.hostname)) {
        trustScore += 45 // Slightly less than exact match
      }

      // Clear unsubscribe text bonus
      const clearPatterns = [
        /^unsubscribe$/i,
        /^opt[- ]?out$/i,
        /^remove[- ]?me$/i,
        /unsubscribe/i,
        /opt[- ]?out/i,
        /remove[- ]?subscription/i,
        /manage[- ]?preferences/i,
        /email[- ]?preferences/i,
        /notification[- ]?settings/i
      ]

      if (clearPatterns.some(pattern => pattern.test(text))) {
        trustScore += 15
      }

      // Domain reputation bonuses
      if (this.hasGoodDomainReputation(url.hostname)) {
        trustScore += 10
      }

      // Email service provider bonus
      if (this.isEmailServiceProvider(url.hostname)) {
        trustScore += 25
      }

      // Penalty for suspicious characteristics
      if (this.isLinkSuspicious(href)) {
        trustScore -= 30
      }

      // Penalty for malicious domains
      if (this.maliciousDomains.includes(url.hostname)) {
        trustScore -= 40
      }

      // Bonus for legitimate unsubscribe URL patterns
      if (this.hasLegitimateUnsubscribePattern(href)) {
        trustScore += 10
      }

      // Normalize to 0-100 scale
      return Math.max(0, Math.min(100, trustScore))
    } catch (error) {
      return 0
    }
  }

  /**
   * Detect phishing patterns in link content
   * @param {string} href - Link URL
   * @param {string} text - Link text
   * @param {string} title - Link title
   * @returns {Object} Phishing analysis result
   */
  detectPhishingPatterns(href, text, title) {
    const phishingIndicators = {
      urgency: [
        /urgent/i, /immediate/i, /expires? (today|soon|now)/i,
        /act now/i, /limited time/i, /expires? in/i,
        /final notice/i, /last chance/i, /deadline/i
      ],
      financial: [
        /payment.*fail/i, /billing.*issue/i, /account.*suspend/i,
        /unauthorized.*charge/i, /refund.*process/i, /credit.*expire/i,
        /bank.*verify/i, /update.*payment/i, /billing.*update/i
      ],
      security: [
        /security.*alert/i, /suspicious.*activity/i, /account.*compromise/i,
        /login.*attempt/i, /password.*expire/i, /verify.*identity/i,
        /confirm.*account/i, /validate.*account/i, /reactivate.*account/i
      ],
      generic: [
        /click.*here.*to/i, /verify.*email/i, /confirm.*subscription/i,
        /update.*information/i, /maintain.*access/i, /continue.*service/i
      ],
      suspicious_domains: [
        /paypal-/i, /amazon-/i, /apple-/i, /microsoft-/i, /google-/i,
        /facebook-/i, /twitter-/i, /linkedin-/i, /instagram-/i,
        /secure-/i, /account-/i, /login-/i, /verify-/i, /update-/i
      ]
    }

    const detectedPatterns = []
    let riskScore = 0

    // Check all text content
    const textContent = [href, text, title].join(' ').toLowerCase()

    for (const [category, patterns] of Object.entries(phishingIndicators)) {
      for (const pattern of patterns) {
        if (pattern.test(textContent)) {
          detectedPatterns.push({
            category,
            pattern: pattern.source,
            match: textContent.match(pattern)?.[0]
          })
          riskScore += category === 'financial'
            ? 30
            : category === 'security'
              ? 25
              : category === 'urgency' ? 20 : 15
        }
      }
    }

    // Check for suspicious URL patterns
    try {
      const urlObj = new URL(href)

      // Check for suspicious domain patterns
      if (phishingIndicators.suspicious_domains.some(pattern => pattern.test(urlObj.hostname))) {
        detectedPatterns.push({
          category: 'suspicious_domain',
          pattern: 'Domain name mimicking legitimate service',
          match: urlObj.hostname
        })
        riskScore += 35
      }

      // Check for URL shorteners (higher risk)
      const shorteners = ['bit.ly', 'tinyurl.com', 'goo.gl', 't.co', 'ow.ly']
      if (shorteners.some(shortener => urlObj.hostname.includes(shortener))) {
        detectedPatterns.push({
          category: 'url_shortener',
          pattern: 'URL shortener detected',
          match: urlObj.hostname
        })
        riskScore += 20
      }

      // Check for suspicious URL structure
      if (urlObj.pathname.includes('..') || urlObj.pathname.includes('//')) {
        detectedPatterns.push({
          category: 'url_structure',
          pattern: 'Suspicious URL structure',
          match: urlObj.pathname
        })
        riskScore += 25
      }
    } catch (error) {
      // Invalid URL structure is suspicious
      detectedPatterns.push({
        category: 'invalid_url',
        pattern: 'Invalid URL structure',
        match: href
      })
      riskScore += 40
    }

    return {
      isPhishing: riskScore > 50,
      riskScore,
      detectedPatterns,
      riskLevel: riskScore > 70 ? 'high' : riskScore > 40 ? 'medium' : 'low'
    }
  }

  /**
   * Check if domain has good reputation
   * @param {string} domain - Domain to check
   * @returns {boolean} True if domain has good reputation
   */
  hasGoodDomainReputation(domain) {
    const goodReputationDomains = [
      'gmail.com', 'yahoo.com', 'outlook.com', 'hotmail.com',
      'amazon.com', 'apple.com', 'microsoft.com', 'google.com',
      'facebook.com', 'twitter.com', 'linkedin.com', 'github.com',
      'stackoverflow.com', 'wikipedia.org', 'reddit.com'
    ]

    return goodReputationDomains.some(goodDomain => 
      domain.includes(goodDomain) || domain.endsWith(goodDomain)
    )
  }

  /**
   * Check if domain is an email service provider
   * @param {string} domain - Domain to check
   * @returns {boolean} True if domain is an email service provider
   */
  isEmailServiceProvider(domain) {
    return this.emailServiceProviders.some(provider => 
      domain.includes(provider) || domain.endsWith(provider)
    )
  }

  /**
   * Check if link is suspicious
   * @param {string} href - Link URL
   * @returns {boolean} True if link is suspicious
   */
  isLinkSuspicious(href) {
    try {
      const url = new URL(href)
      
      // Check for suspicious patterns
      const suspiciousPatterns = [
        /bit\.ly|tinyurl\.com|goo\.gl|t\.co/i,
        /[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}/,
        /xn--/,
        /[a-z]{20,}/i,
        /[0-9]{10,}/
      ]

      return suspiciousPatterns.some(pattern => pattern.test(url.hostname))
    } catch (error) {
      return true // Invalid URLs are suspicious
    }
  }

  /**
   * Check if URL has legitimate unsubscribe pattern
   * @param {string} href - Link URL
   * @returns {boolean} True if URL has legitimate unsubscribe pattern
   */
  hasLegitimateUnsubscribePattern(href) {
    try {
      const url = new URL(href)
      const path = url.pathname.toLowerCase()
      const search = url.search.toLowerCase()
      
      const legitimatePatterns = [
        /unsubscribe/,
        /optout/,
        /remove/,
        /preferences/,
        /settings/
      ]

      return legitimatePatterns.some(pattern => 
        pattern.test(path) || pattern.test(search)
      )
    } catch (error) {
      return false
    }
  }

  /**
   * Check if domain is in local blacklist
   * @param {string} domain - Domain to check
   * @returns {Promise<boolean>} True if domain is blacklisted
   */
  async isInLocalBlacklist(domain) {
    try {
      const blacklist = await StorageUtils.getLocalBlacklist()
      return blacklist.includes(domain)
    } catch (error) {
      console.error('Error checking local blacklist:', error)
      return false
    }
  }

  /**
   * Get manual override for domain
   * @param {string} domain - Domain to check
   * @returns {Promise<Object|null>} Manual override or null
   */
  async getManualOverride(domain) {
    try {
      const overrides = await StorageUtils.get('security_overrides')
      return overrides.security_overrides?.[domain] || null
    } catch (error) {
      console.error('Error getting manual override:', error)
      return null
    }
  }

  /**
   * Determine security status from trust level
   * @param {number} trustLevel - Trust level (0-100)
   * @returns {string} Security status
   */
  determineSecurityStatus(trustLevel) {
    if (trustLevel >= 70) return SECURITY_STATUS.SAFE
    if (trustLevel >= 40) return SECURITY_STATUS.CAUTION
    if (trustLevel >= 1) return SECURITY_STATUS.DANGEROUS
    return SECURITY_STATUS.UNKNOWN
  }

  /**
   * Generate security warnings for a link
   * @param {string} href - Link URL
   * @param {string} text - Link text
   * @param {number} trustLevel - Trust level
   * @returns {Array} Array of security warnings
   */
  generateSecurityWarnings(href, text, trustLevel) {
    const warnings = []
    
    try {
      const url = new URL(href)
      
      // Check for HTTP (not HTTPS)
      if (url.protocol === 'http:') {
        warnings.push({
          type: 'insecure_connection',
          message: 'This link uses an insecure connection (HTTP)',
          severity: 'medium'
        })
      }

      // Check for suspicious domain
      if (this.isLinkSuspicious(href)) {
        warnings.push({
          type: 'suspicious_domain',
          message: 'This domain appears suspicious',
          severity: 'high'
        })
      }

      // Check for phishing patterns
      const phishingCheck = this.detectPhishingPatterns(href, text, '')
      if (phishingCheck.isPhishing) {
        warnings.push({
          type: 'phishing_detected',
          message: 'This link contains phishing indicators',
          severity: 'high',
          details: phishingCheck.detectedPatterns
        })
      }

      // Check for low trust level
      if (trustLevel < 40) {
        warnings.push({
          type: 'low_trust',
          message: 'This link has a low trust score',
          severity: 'medium'
        })
      }

      // Check for malicious domains
      if (this.maliciousDomains.includes(url.hostname)) {
        warnings.push({
          type: 'malicious_domain',
          message: 'This domain is known to be malicious',
          severity: 'high'
        })
      }

    } catch (error) {
      warnings.push({
        type: 'invalid_url',
        message: 'This URL appears to be malformed',
        severity: 'high'
      })
    }

    return warnings
  }

  /**
   * Analyze multiple links and determine the most restrictive security status
   * @param {Array} links - Array of link objects
   * @returns {Promise<string>} Most restrictive security status
   */
  async getMostRestrictiveSecurityStatus(links) {
    if (!links || links.length === 0) return SECURITY_STATUS.UNKNOWN

    const statuses = await Promise.all(
      links.map(async (link) => {
        const trustLevel = await this.calculateTrustLevel(link.href, link.text, link.title)
        return this.determineSecurityStatus(trustLevel)
      })
    )

    // Return most restrictive status
    if (statuses.includes(SECURITY_STATUS.DANGEROUS)) return SECURITY_STATUS.DANGEROUS
    if (statuses.includes(SECURITY_STATUS.CAUTION)) return SECURITY_STATUS.CAUTION
    if (statuses.includes(SECURITY_STATUS.SAFE)) return SECURITY_STATUS.SAFE
    return SECURITY_STATUS.UNKNOWN
  }

  /**
   * Analyze multiple links and determine the most optimistic security status
   * @param {Array} links - Array of link objects
   * @returns {Promise<string>} Most optimistic security status
   */
  async getMostOptimisticSecurityStatus(links) {
    if (!links || links.length === 0) return SECURITY_STATUS.UNKNOWN

    const statuses = await Promise.all(
      links.map(async (link) => {
        const trustLevel = await this.calculateTrustLevel(link.href, link.text, link.title)
        return this.determineSecurityStatus(trustLevel)
      })
    )

    // Return most optimistic status
    if (statuses.includes(SECURITY_STATUS.SAFE)) return SECURITY_STATUS.SAFE
    if (statuses.includes(SECURITY_STATUS.CAUTION)) return SECURITY_STATUS.CAUTION
    if (statuses.includes(SECURITY_STATUS.DANGEROUS)) return SECURITY_STATUS.DANGEROUS
    return SECURITY_STATUS.UNKNOWN
  }

  /**
   * Analyze a single unsubscribe link
   * @param {string} href - Link URL
   * @param {string} text - Link text
   * @param {string} title - Link title (optional)
   * @returns {Promise<Object>} Link analysis result
   */
  async analyzeLink(href, text, title = '') {
    try {
      const trustLevel = await this.calculateTrustLevel(href, text, title)
      const securityStatus = this.determineSecurityStatus(trustLevel)
      const warnings = this.generateSecurityWarnings(href, text, trustLevel)
      const phishingCheck = this.detectPhishingPatterns(href, text, title)

      return {
        trustLevel,
        securityStatus,
        warnings,
        phishingCheck,
        isSafe: securityStatus === SECURITY_STATUS.SAFE,
        isPhishing: phishingCheck.isPhishing,
        riskLevel: phishingCheck.riskLevel
      }
    } catch (error) {
      console.error('Error analyzing link:', error)
      return {
        trustLevel: 0,
        securityStatus: SECURITY_STATUS.UNKNOWN,
        warnings: [{
          type: 'analysis_error',
          message: 'Failed to analyze link security',
          severity: 'medium'
        }],
        phishingCheck: { isPhishing: false, riskScore: 0, detectedPatterns: [], riskLevel: 'low' },
        isSafe: false,
        isPhishing: false,
        riskLevel: 'unknown'
      }
    }
  }
}

export default SecurityAnalyzer