/**
 * Unsubscribe Handler Module for Gmail Unsubscriber
 * Handles unsubscribe link processing and user interactions
 */

import { ExtensionLogger } from '../shared/logger.js';
import { COMPONENTS } from '../shared/constants.js';
import { UIUtils } from '../shared/ui-utils.js';
import { DOMUtils } from '../shared/dom-utils.js';
import { MessageUtils } from '../shared/message-utils.js';

/**
 * Handles unsubscribe operations and UI interactions
 */
export class UnsubscribeHandler {
  constructor(parentInstance) {
    this.parent = parentInstance;
    this.activeModals = new Set();
  }

  async init() {
    ExtensionLogger.info(COMPONENTS.CONTENT, 'Unsubscribe handler initialized');
  }

  /**
   * Handle unsubscribe request for a sender
   */
  async handleUnsubscribe(senderId) {
    try {
      const subscription = this.parent.subscriptionDatabase.get(senderId);
      
      if (!subscription) {
        return { success: false, error: 'Subscription not found' };
      }

      // Show unsubscribe options modal
      await this.showUnsubscribeModal(subscription);
      
      return { success: true, message: 'Unsubscribe options displayed' };
    } catch (error) {
      ExtensionLogger.error(COMPONENTS.CONTENT, 'Unsubscribe handling failed', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Show unsubscribe options modal
   */
  async showUnsubscribeModal(subscription) {
    try {
      const modal = this.createUnsubscribeModal(subscription);
      this.activeModals.add(modal);
      
      // Set up event listeners
      this.setupModalEventListeners(modal, subscription);
      
      document.body.appendChild(modal);
      
      ExtensionLogger.info(COMPONENTS.CONTENT, 'Unsubscribe modal displayed');
    } catch (error) {
      ExtensionLogger.error(COMPONENTS.CONTENT, 'Failed to show unsubscribe modal', error);
    }
  }

  /**
   * Create unsubscribe modal HTML
   */
  createUnsubscribeModal(subscription) {
    const modal = DOMUtils.createElement('div', { className: 'unsubscriber-modal' });
    
    const unsubscribeLinks = subscription.unsubscribeLinks || [];
    
    modal.innerHTML = `
      <div class="unsubscriber-modal-content">
        <div class="unsubscriber-modal-header">
          <h3>Unsubscribe Options</h3>
          <button class="unsubscriber-close">&times;</button>
        </div>
        <div class="unsubscriber-modal-body">
          <p>Found ${unsubscribeLinks.length} unsubscribe link(s) for <strong>${subscription.senderName}</strong>:</p>
          <div class="unsubscriber-links">
            ${unsubscribeLinks.map((link, index) => this.createLinkHTML(link, index)).join('')}
          </div>
          <div class="unsubscriber-warning">
            <p><strong>Privacy Notice:</strong> Clicking will open the unsubscribe link in a new tab. Links marked with ⚠️ may be suspicious.</p>
          </div>
        </div>
      </div>
    `;
    
    return modal;
  }

  /**
   * Create HTML for individual unsubscribe link
   */
  createLinkHTML(link, index) {
    const trustClass = link.trustLevel < 50 ? 'low-trust' : 
                      link.trustLevel >= 80 ? 'high-trust' : 'medium-trust';
    
    const warningsHTML = link.securityWarnings && link.securityWarnings.length > 0 
      ? `<span class="security-warnings">
          ${link.securityWarnings.map(warning => 
            `<span class="warning-${warning.type}">${warning.icon} ${warning.message}</span>`
          ).join('')}
        </span>`
      : '';
    
    const phishingHTML = link.phishingCheck && link.phishingCheck.isPhishing
      ? `<div class="phishing-details">
          <small>Detected patterns: ${link.phishingCheck.detectedPatterns.map(p => p.category).join(', ')}</small>
        </div>`
      : '';
    
    return `
      <div class="unsubscriber-link-item">
        <button class="unsubscriber-link-btn ${trustClass}" 
                data-href="${link.href}" data-index="${index}">
          <div class="link-info">
            <span class="link-text">${link.text || 'Unsubscribe Link'}</span>
            <span class="link-type">${link.type || 'HTTP'}</span>
          </div>
          <div class="link-meta">
            <span class="trust-level">Trust: ${link.trustLevel}%</span>
            ${warningsHTML}
          </div>
          ${phishingHTML}
        </button>
      </div>
    `;
  }

  /**
   * Set up modal event listeners
   */
  setupModalEventListeners(modal, subscription) {
    // Close button
    const closeBtn = modal.querySelector('.unsubscriber-close');
    closeBtn.addEventListener('click', () => {
      this.closeModal(modal);
    });

    // Overlay click to close
    modal.addEventListener('click', (e) => {
      if (e.target === modal) {
        this.closeModal(modal);
      }
    });

    // Link buttons
    const linkButtons = modal.querySelectorAll('.unsubscriber-link-btn');
    linkButtons.forEach(button => {
      button.addEventListener('click', (e) => {
        e.preventDefault();
        const href = button.getAttribute('data-href');
        const index = parseInt(button.getAttribute('data-index'));
        const link = subscription.unsubscribeLinks[index];
        
        this.handleLinkClick(link, subscription);
      });
    });
  }

  /**
   * Handle unsubscribe link click
   */
  async handleLinkClick(link, subscription) {
    try {
      // Check if link needs security confirmation
      if (link.trustLevel < 70 || (link.securityWarnings && link.securityWarnings.length > 0)) {
        const confirmed = await this.showSecurityConfirmation(link);
        if (!confirmed) {
          return;
        }
      }

      // Increment usage count
      try {
        await MessageUtils.sendToBackground('incrementUsage');
      } catch (error) {
        ExtensionLogger.warn(COMPONENTS.CONTENT, 'Failed to increment usage', error);
      }

      // Open unsubscribe link
      this.openUnsubscribeLink(link);

      // Update subscription status
      await this.updateSubscriptionStatus(subscription, 'unsubscribed');

      ExtensionLogger.info(COMPONENTS.CONTENT, 'Unsubscribe link opened', {
        sender: subscription.senderEmail,
        trustLevel: link.trustLevel
      });

    } catch (error) {
      ExtensionLogger.error(COMPONENTS.CONTENT, 'Failed to handle link click', error);
    }
  }

  /**
   * Show security confirmation dialog
   */
  async showSecurityConfirmation(link) {
    return new Promise((resolve) => {
      const confirmModal = this.createSecurityConfirmationModal(link, resolve);
      document.body.appendChild(confirmModal);
      this.activeModals.add(confirmModal);
    });
  }

  /**
   * Create security confirmation modal
   */
  createSecurityConfirmationModal(link, resolveCallback) {
    const modal = DOMUtils.createElement('div', { className: 'unsubscriber-security-modal' });
    
    const warningsHTML = link.securityWarnings && link.securityWarnings.length > 0
      ? `<div class="warning-list">
          <p><strong>Security Warnings:</strong></p>
          <ul>
            ${link.securityWarnings.map(warning => 
              `<li>${warning.icon} ${warning.message}</li>`
            ).join('')}
          </ul>
        </div>`
      : '';
    
    const phishingHTML = link.phishingCheck && link.phishingCheck.isPhishing
      ? `<div class="phishing-warning">
          <p><strong>Phishing Risk:</strong> ${link.phishingCheck.riskLevel}</p>
          <p>Detected patterns: ${link.phishingCheck.detectedPatterns.map(p => p.category).join(', ')}</p>
        </div>`
      : '';
    
    modal.innerHTML = `
      <div class="unsubscriber-security-modal-content">
        <div class="unsubscriber-security-modal-header">
          <h3>🛡️ Security Warning</h3>
        </div>
        <div class="unsubscriber-security-modal-body">
          <div class="security-alert">
            <p><strong>This link may be risky!</strong></p>
            <div class="risk-details">
              <p><strong>Trust Level:</strong> ${link.trustLevel}%</p>
              <p><strong>URL:</strong> <code>${link.href}</code></p>
              ${warningsHTML}
              ${phishingHTML}
            </div>
          </div>
          <div class="security-actions">
            <button class="security-btn cancel-btn">Cancel</button>
            <button class="security-btn add-blacklist-btn">Add to Blacklist</button>
            <button class="security-btn proceed-btn">Proceed Anyway</button>
          </div>
        </div>
      </div>
    `;

    // Set up event listeners
    const cancelBtn = modal.querySelector('.cancel-btn');
    const blacklistBtn = modal.querySelector('.add-blacklist-btn');
    const proceedBtn = modal.querySelector('.proceed-btn');

    cancelBtn.addEventListener('click', () => {
      this.closeModal(modal);
      resolveCallback(false);
    });

    blacklistBtn.addEventListener('click', async () => {
      await this.addToBlacklist(link);
      this.closeModal(modal);
      resolveCallback(false);
    });

    proceedBtn.addEventListener('click', () => {
      this.closeModal(modal);
      resolveCallback(true);
    });

    return modal;
  }

  /**
   * Open unsubscribe link in new tab
   */
  openUnsubscribeLink(link) {
    try {
      if (link.type === 'EMAIL') {
        // Handle mailto links
        window.location.href = link.href;
      } else {
        // Open HTTP links in new tab
        window.open(link.href, '_blank', 'noopener,noreferrer');
      }
    } catch (error) {
      ExtensionLogger.error(COMPONENTS.CONTENT, 'Failed to open unsubscribe link', error);
    }
  }

  /**
   * Update subscription status
   */
  async updateSubscriptionStatus(subscription, status) {
    try {
      subscription.status = status;
      subscription.lastAction = Date.now();
      
      // Update in parent's database
      this.parent.subscriptionDatabase.set(subscription.senderId, subscription);
      
      ExtensionLogger.info(COMPONENTS.CONTENT, 'Subscription status updated', {
        sender: subscription.senderEmail,
        status
      });
    } catch (error) {
      ExtensionLogger.error(COMPONENTS.CONTENT, 'Failed to update subscription status', error);
    }
  }

  /**
   * Add link to blacklist
   */
  async addToBlacklist(link) {
    try {
      // This would integrate with the security analyzer's blacklist
      await this.parent.securityAnalyzer.addToBlacklist(link.href);
      
      ExtensionLogger.info(COMPONENTS.CONTENT, 'Link added to blacklist', link.href);
    } catch (error) {
      ExtensionLogger.error(COMPONENTS.CONTENT, 'Failed to add to blacklist', error);
    }
  }

  /**
   * Close modal and clean up
   */
  closeModal(modal) {
    try {
      if (modal.parentNode) {
        modal.parentNode.removeChild(modal);
      }
      this.activeModals.delete(modal);
    } catch (error) {
      ExtensionLogger.error(COMPONENTS.CONTENT, 'Failed to close modal', error);
    }
  }

  /**
   * Clean up all active modals
   */
  cleanup() {
    for (const modal of this.activeModals) {
      this.closeModal(modal);
    }
    this.activeModals.clear();
    
    ExtensionLogger.info(COMPONENTS.CONTENT, 'Unsubscribe handler cleaned up');
  }
}
