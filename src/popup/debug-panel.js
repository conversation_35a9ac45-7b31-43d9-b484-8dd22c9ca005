/**
 * Debug panel module for popup interface
 * Extracted from popup.js to provide focused debugging functionality
 */

import { COMPONENTS } from '../shared/constants.js'
import { StorageUtils } from '../shared/storage-utils.js'
import { DOMUtils } from '../shared/dom-utils.js'
import { MessageUtils } from '../shared/message-utils.js'

export class DebugPanel {
  constructor(logger, popupInstance) {
    this.logger = logger
    this.popup = popupInstance
    this.stateMonitor = null
  }

  /**
   * Add debug section to popup
   */
  addDebugSection() {
    const debugSection = DOMUtils.createElement('div', {
      id: 'debug-section',
      style: `
        margin-top: 20px;
        padding: 15px;
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 5px;
        font-family: monospace;
        font-size: 12px;
      `
    })

    debugSection.innerHTML = `
      <div style="font-weight: bold; margin-bottom: 10px; color: #495057;">🔧 Debug Information</div>
      <div id="debug-stats" style="margin-bottom: 10px;"></div>
      <div id="debug-logs" style="margin-bottom: 10px;"></div>
      <div style="display: flex; gap: 5px; flex-wrap: wrap;">
        <button id="debug-refresh" style="padding: 4px 8px; font-size: 11px; border: 1px solid #ccc; background: white; cursor: pointer;">Refresh</button>
        <button id="debug-clear" style="padding: 4px 8px; font-size: 11px; border: 1px solid #ccc; background: white; cursor: pointer;">Clear Logs</button>
        <button id="debug-dashboard" style="padding: 4px 8px; font-size: 11px; border: 1px solid #ccc; background: white; cursor: pointer;">Dashboard</button>
        <button id="debug-export" style="padding: 4px 8px; font-size: 11px; border: 1px solid #ccc; background: white; cursor: pointer;">Export</button>
      </div>
    `

    document.body.appendChild(debugSection)

    // Add event listeners for debug buttons
    const refreshBtn = DOMUtils.querySelector('#debug-refresh')
    const clearBtn = DOMUtils.querySelector('#debug-clear')
    const dashboardBtn = DOMUtils.querySelector('#debug-dashboard')
    const exportBtn = DOMUtils.querySelector('#debug-export')

    if (refreshBtn) refreshBtn.addEventListener('click', () => this.refreshDebugInfo())
    if (clearBtn) clearBtn.addEventListener('click', () => this.clearDebugLogs())
    if (dashboardBtn) dashboardBtn.addEventListener('click', () => this.openDebugDashboard())
    if (exportBtn) exportBtn.addEventListener('click', () => this.exportDebugData())
  }

  /**
   * Initialize state monitoring
   */
  initializeStateMonitoring() {
    this.stateMonitor = this.createStateMonitor()
    this.startStateMonitoring()
  }

  /**
   * Start state monitoring
   */
  startStateMonitoring() {
    if (this.stateMonitor) {
      setInterval(() => {
        this.updateDebugInfo()
      }, 5000) // Update every 5 seconds
    }
  }

  /**
   * Create state monitor
   * @returns {Object} State monitor methods
   */
  createStateMonitor() {
    return {
      getCurrentState: () => {
        const state = {
          monthlyCount: this.popup.currentCount,
          remainingCount: this.popup.remainingCount,
          limitReached: this.popup.limitReached,
          currentDomain: this.popup.currentDomain,
          currentScore: this.popup.currentScore,
          currentUrl: this.popup.currentUrl,
          monthlyLimit: this.popup.monthlyLimit,
          debugMode: this.popup.debugMode,
          timestamp: new Date().toISOString()
        }

        this.logger.info(COMPONENTS.POPUP, 'Current popup state', state)
        return state
      },

      getStorageData: async () => {
        try {
          const data = await StorageUtils.get([
            'monthlyCount',
            'totalUnsubscribes',
            'lastResetDate',
            'unsubscribeHistory',
            'debug_logs'
          ])

          this.logger.info(COMPONENTS.STORAGE, 'Storage data retrieved', data)
          return data
        } catch (error) {
          this.logger.error(COMPONENTS.STORAGE, 'Failed to get storage data', error)
          return null
        }
      },

      checkExtensionHealth: async () => {
        const health = {
          extensionContext: !!(chrome && chrome.runtime && chrome.runtime.id),
          storageAccess: false,
          backgroundConnection: false,
          contentScriptActive: false
        }

        // Test storage access
        try {
          await StorageUtils.get(['test'])
          health.storageAccess = true
        } catch (error) {
          this.logger.error(COMPONENTS.RUNTIME, 'Storage access failed', error)
        }

        // Test background connection
        try {
          const response = await MessageUtils.sendToBackground('ping')
          health.backgroundConnection = response?.success || false
        } catch (error) {
          this.logger.error(COMPONENTS.RUNTIME, 'Background connection failed', error)
        }

        // Test content script
        try {
          const tab = await MessageUtils.getCurrentTab()
          if (tab) {
            const response = await MessageUtils.sendToContent(tab.id, 'ping')
            health.contentScriptActive = response?.success || false
          }
        } catch (error) {
          this.logger.error(COMPONENTS.RUNTIME, 'Content script check failed', error)
        }

        this.logger.info(COMPONENTS.RUNTIME, 'Extension health check', health)
        return health
      },

      simulateUnsubscribe: () => {
        const originalCount = this.popup.currentCount
        this.popup.currentCount++
        this.popup.remainingCount = Math.max(0, this.popup.monthlyLimit - this.popup.currentCount)
        this.popup.limitReached = this.popup.currentCount >= this.popup.monthlyLimit

        this.popup.updateUI()
        this.updateDebugInfo()

        this.logger.info(COMPONENTS.POPUP, 'Simulated unsubscribe', {
          from: originalCount,
          to: this.popup.currentCount,
          remaining: this.popup.remainingCount,
          limitReached: this.popup.limitReached
        })
      },

      resetCounters: () => {
        this.popup.currentCount = 0
        this.popup.remainingCount = this.popup.monthlyLimit
        this.popup.limitReached = false

        this.popup.updateUI()
        this.updateDebugInfo()

        this.logger.info(COMPONENTS.POPUP, 'Counters reset')
      }
    }
  }

  /**
   * Update debug information display
   */
  async updateDebugInfo() {
    const debugStats = DOMUtils.querySelector('#debug-stats')
    const debugLogs = DOMUtils.querySelector('#debug-logs')

    if (!debugStats || !debugLogs) return

    try {
      const state = this.stateMonitor.getCurrentState()
      const health = await this.stateMonitor.checkExtensionHealth()
      const storageData = await this.stateMonitor.getStorageData()

      // Update stats display
      debugStats.innerHTML = `
        <div style="font-weight: bold; color: #6c757d;">System Status</div>
        <div style="margin-left: 10px; font-size: 11px;">
          <div>Extension: ${health.extensionContext ? '✅' : '❌'}</div>
          <div>Storage: ${health.storageAccess ? '✅' : '❌'}</div>
          <div>Background: ${health.backgroundConnection ? '✅' : '❌'}</div>
          <div>Content: ${health.contentScriptActive ? '✅' : '❌'}</div>
        </div>
        <div style="font-weight: bold; color: #6c757d; margin-top: 10px;">Current State</div>
        <div style="margin-left: 10px; font-size: 11px;">
          <div>Count: ${state.monthlyCount}/${state.monthlyLimit}</div>
          <div>Remaining: ${state.remainingCount}</div>
          <div>Limit Reached: ${state.limitReached ? 'Yes' : 'No'}</div>
          <div>Debug Mode: ${state.debugMode ? 'On' : 'Off'}</div>
        </div>
      `

      // Update logs display
      const logs = await StorageUtils.getDebugLogs()
      const recentLogs = logs.slice(-5) // Show last 5 logs
      
      debugLogs.innerHTML = `
        <div style="font-weight: bold; color: #6c757d;">Recent Logs (${logs.length} total)</div>
        <div style="margin-left: 10px; font-size: 10px; max-height: 100px; overflow-y: auto;">
          ${recentLogs.map(log => `
            <div style="margin: 2px 0; color: ${this.getLogColor(log.level)};">
              [${new Date(log.timestamp).toLocaleTimeString()}] ${log.component}: ${log.message}
            </div>
          `).join('')}
        </div>
      `
    } catch (error) {
      this.logger.error(COMPONENTS.POPUP, 'Failed to update debug info', error)
    }
  }

  /**
   * Get color for log level
   * @param {string} level - Log level
   * @returns {string} Color code
   */
  getLogColor(level) {
    const colors = {
      error: '#dc3545',
      warn: '#ffc107',
      info: '#17a2b8',
      debug: '#6c757d'
    }
    return colors[level] || colors.info
  }

  /**
   * Refresh debug information
   */
  async refreshDebugInfo() {
    await this.updateDebugInfo()
    this.logger.info(COMPONENTS.POPUP, 'Debug info refreshed')
  }

  /**
   * Clear debug logs
   */
  async clearDebugLogs() {
    try {
      await StorageUtils.saveDebugLogs([])
      await this.updateDebugInfo()
      this.logger.info(COMPONENTS.STORAGE, 'Debug logs cleared')
    } catch (error) {
      this.logger.error(COMPONENTS.STORAGE, 'Failed to clear debug logs', error)
    }
  }

  /**
   * Open debug dashboard
   */
  openDebugDashboard() {
    try {
      chrome.tabs.create({
        url: chrome.runtime.getURL('debug.html')
      })
    } catch (error) {
      this.logger.error(COMPONENTS.POPUP, 'Failed to open debug dashboard', error)
    }
  }

  /**
   * Export debug data
   */
  async exportDebugData() {
    try {
      const state = this.stateMonitor.getCurrentState()
      const health = await this.stateMonitor.checkExtensionHealth()
      const storageData = await this.stateMonitor.getStorageData()
      const logs = await StorageUtils.getDebugLogs()

      const exportData = {
        timestamp: new Date().toISOString(),
        version: chrome.runtime.getManifest().version,
        state,
        health,
        storageData,
        logs
      }

      const blob = new Blob([JSON.stringify(exportData, null, 2)], { 
        type: 'application/json' 
      })
      const url = URL.createObjectURL(blob)
      
      const a = document.createElement('a')
      a.href = url
      a.download = `debug-export-${new Date().toISOString().slice(0, 10)}.json`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)

      this.logger.info(COMPONENTS.POPUP, 'Debug data exported successfully')
    } catch (error) {
      this.logger.error(COMPONENTS.POPUP, 'Failed to export debug data', error)
    }
  }

  /**
   * Setup testing event listeners
   */
  setupTestingEventListeners() {
    // Add testing buttons and event listeners
    const testingSection = DOMUtils.querySelector('#testing-section')
    if (!testingSection) return

    const buttons = {
      'test-free-tier': () => this.testChangeTier('free'),
      'test-premium-tier': () => this.testChangeTier('premium'),
      'test-unlimited-tier': () => this.testChangeTier('unlimited'),
      'test-increment': () => this.testIncrementUsage(),
      'test-reset-usage': () => this.testResetUsage(),
      'test-reset-storage': () => this.testResetStorage(),
      'test-show-stats': () => this.testShowStats()
    }

    Object.entries(buttons).forEach(([buttonId, handler]) => {
      const button = DOMUtils.querySelector(`#${buttonId}`)
      if (button) {
        button.addEventListener('click', handler)
      }
    })
  }

  /**
   * Test changing subscription tier
   * @param {string} tier - Subscription tier to test
   */
  async testChangeTier(tier) {
    try {
      const response = await MessageUtils.upgradeSubscription(tier)
      if (response.success) {
        this.popup.currentSubscription = { tier }
        this.popup.updateUI()
        this.logger.info(COMPONENTS.POPUP, `Test tier change to ${tier} successful`)
      } else {
        this.logger.error(COMPONENTS.POPUP, `Test tier change to ${tier} failed`, response.error)
      }
    } catch (error) {
      this.logger.error(COMPONENTS.POPUP, 'Failed to test tier change', error)
    }
  }

  /**
   * Test incrementing usage
   */
  async testIncrementUsage() {
    try {
      const response = await MessageUtils.incrementUsage()
      if (response.success) {
        this.popup.currentCount = response.data.monthly_count
        this.popup.remainingCount = response.data.remaining
        this.popup.limitReached = response.data.limit_reached
        this.popup.updateUI()
        this.logger.info(COMPONENTS.POPUP, 'Test increment usage successful')
      } else {
        this.logger.error(COMPONENTS.POPUP, 'Test increment usage failed', response.error)
      }
    } catch (error) {
      this.logger.error(COMPONENTS.POPUP, 'Failed to test increment usage', error)
    }
  }

  /**
   * Test resetting usage
   */
  async testResetUsage() {
    try {
      const stats = await StorageUtils.getUsageStats()
      stats.monthly_count = 0
      stats.last_reset = Date.now()
      await StorageUtils.saveUsageStats(stats)
      
      this.popup.currentCount = 0
      this.popup.remainingCount = this.popup.monthlyLimit
      this.popup.limitReached = false
      this.popup.updateUI()
      
      this.logger.info(COMPONENTS.POPUP, 'Test reset usage successful')
    } catch (error) {
      this.logger.error(COMPONENTS.POPUP, 'Failed to test reset usage', error)
    }
  }

  /**
   * Test resetting storage
   */
  async testResetStorage() {
    try {
      await StorageUtils.initializeDefaults()
      this.popup.loadStoredData()
      this.logger.info(COMPONENTS.POPUP, 'Test reset storage successful')
    } catch (error) {
      this.logger.error(COMPONENTS.POPUP, 'Failed to test reset storage', error)
    }
  }

  /**
   * Test showing stats
   */
  async testShowStats() {
    try {
      const stats = await MessageUtils.getStats()
      const statsDisplay = DOMUtils.querySelector('#debug-stats')
      if (statsDisplay) {
        statsDisplay.innerHTML += `
          <div style="margin-top: 10px; padding: 10px; background: #e9ecef; border-radius: 3px;">
            <div style="font-weight: bold;">Test Stats</div>
            <div style="font-size: 11px;">
              <div>Monthly Count: ${stats.monthly_count}</div>
              <div>Remaining: ${stats.remaining}</div>
              <div>Tier: ${stats.subscription?.tier || 'free'}</div>
              <div>Limit Reached: ${stats.limit_reached ? 'Yes' : 'No'}</div>
            </div>
          </div>
        `
      }
      this.logger.info(COMPONENTS.POPUP, 'Test show stats successful', stats)
    } catch (error) {
      this.logger.error(COMPONENTS.POPUP, 'Failed to test show stats', error)
    }
  }

  /**
   * Show testing section
   */
  showTestingSection() {
    const testingSection = DOMUtils.querySelector('#testing-section')
    if (testingSection) {
      testingSection.style.display = 'block'
    }
  }

  /**
   * Update testing button states
   */
  updateTestingButtonStates() {
    const buttons = ['test-free-tier', 'test-premium-tier', 'test-unlimited-tier']
    buttons.forEach(buttonId => {
      const button = DOMUtils.querySelector(`#${buttonId}`)
      if (button) {
        button.style.backgroundColor = this.popup.currentSubscription?.tier === buttonId.replace('test-', '').replace('-tier', '') ? '#007bff' : ''
        button.style.color = this.popup.currentSubscription?.tier === buttonId.replace('test-', '').replace('-tier', '') ? 'white' : ''
      }
    })
  }

  /**
   * Force tier update for testing
   * @param {string} tier - Tier to force update to
   */
  async forceTierUpdate(tier) {
    try {
      const stats = await StorageUtils.getUsageStats()
      stats.subscription = { tier }
      await StorageUtils.saveUsageStats(stats)
      
      this.popup.currentSubscription = { tier }
      this.popup.updateUI()
      this.updateTestingButtonStates()
      
      this.logger.info(COMPONENTS.POPUP, `Forced tier update to ${tier}`)
    } catch (error) {
      this.logger.error(COMPONENTS.POPUP, 'Failed to force tier update', error)
    }
  }
}

export default DebugPanel