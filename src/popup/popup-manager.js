/**
 * Main Popup Manager for Email Unsubscriber
 * Coordinates all popup functionality
 */

import { ExtensionLogger } from '../shared/logger.js';
import { COMPONENTS } from '../shared/constants.js';
import { MessageUtils } from '../shared/message-utils.js';
import { SubscriptionUtils } from '../shared/subscription-utils.js';
import { StorageUtils } from '../shared/storage-utils.js';
import { UIUtils } from '../shared/ui-utils.js';
import { StatsManager } from './stats-manager.js';
import { ScanManager } from './scan-manager.js';
import { SettingsManager } from './settings-manager.js';
import { DebugPanel } from './debug-panel.js';

/**
 * Main popup manager class
 */
export class EmailUnsubscriberPopup {
  constructor() {
    ExtensionLogger.info(COMPONENTS.POPUP, 'Popup initializing');

    // Initialize with default values that will be updated from backend
    this.monthlyLimit = 20; // Default free tier limit
    this.subscription = {
      tier: 'free',
      tierName: 'Free',
      monthlyLimit: 20,
      price: 0
    };
    this.currentCount = 0;
    this.remainingCount = 20;
    this.limitReached = false;
    this.daysUntilReset = 0;
    this.currentDomain = null;
    this.currentScore = null;
    this.currentUrl = null;
    this.debugMode = true;
    
    // Initialize components
    this.statsManager = new StatsManager(this);
    this.scanManager = new ScanManager(this);
    this.settingsManager = new SettingsManager(this);
    
    this.init();
  }

  async init() {
    ExtensionLogger.info(COMPONENTS.POPUP, 'Starting popup initialization');

    // First update UI with default values
    this.updateUI();

    // Then load actual data and update again
    await this.loadStoredData();
    this.updateUI();

    this.setupEventListeners();

    // Set up real-time storage listener for immediate updates
    this.setupStorageListener();

    if (this.debugMode) {
      this.showTestingSection();
    }

    ExtensionLogger.info(COMPONENTS.POPUP, 'Popup initialization complete');
  }

  /**
   * Load stored data from Chrome storage
   */
  async loadStoredData() {
    try {
      // Get subscription data
      const subscriptionData = await SubscriptionUtils.getCurrentSubscription();
      
      this.subscription = {
        tier: subscriptionData.tier,
        tierName: subscriptionData.tierName,
        monthlyLimit: subscriptionData.monthlyLimit,
        price: subscriptionData.price,
        features: subscriptionData.features
      };
      
      this.currentCount = subscriptionData.currentCount;
      this.remainingCount = subscriptionData.remainingCount;
      this.limitReached = subscriptionData.limitReached;
      this.daysUntilReset = subscriptionData.daysUntilReset;
      
      // Get current tab info
      const currentTab = await MessageUtils.getCurrentTab();
      if (currentTab) {
        this.currentUrl = currentTab.url;
        this.currentDomain = this.extractDomain(currentTab.url);
      }
      
      ExtensionLogger.info(COMPONENTS.POPUP, 'Loaded stored data', {
        subscription: this.subscription.tier,
        count: this.currentCount,
        remaining: this.remainingCount
      });
      
    } catch (error) {
      ExtensionLogger.error(COMPONENTS.POPUP, 'Failed to load stored data', error);
    }
  }

  /**
   * Set up event listeners for UI elements
   */
  setupEventListeners() {
    // Scan button
    const scanBtn = document.getElementById('scanBtn');
    if (scanBtn) {
      scanBtn.addEventListener('click', () => this.scanManager.scanCurrentEmail());
    }

    // Bulk scan button
    const bulkScanBtn = document.getElementById('bulkScanBtn');
    if (bulkScanBtn) {
      bulkScanBtn.addEventListener('click', () => this.scanManager.bulkScanEmails());
    }

    // Dashboard button
    const dashboardBtn = document.getElementById('dashboardBtn');
    if (dashboardBtn) {
      dashboardBtn.addEventListener('click', () => this.scanManager.openDashboard());
    }

    // Results screen dashboard button (same function, different button)
    const viewDashboardBtn = document.getElementById('viewDashboardBtn');
    if (viewDashboardBtn) {
      viewDashboardBtn.addEventListener('click', () => this.scanManager.openDashboard());
    }

    // Settings button
    const settingsBtn = document.getElementById('settingsBtn');
    if (settingsBtn) {
      settingsBtn.addEventListener('click', () => this.settingsManager.showSettings());
    }

    // Upgrade button
    const upgradeBtn = document.getElementById('upgradeBtn');
    if (upgradeBtn) {
      upgradeBtn.addEventListener('click', () => this.showUpgradeOptions());
    }

    // Back button
    const backBtn = document.getElementById('backBtn');
    if (backBtn) {
      backBtn.addEventListener('click', () => this.showMainScreen());
    }
  }

  /**
   * Set up storage listener for real-time updates
   */
  setupStorageListener() {
    if (!chrome?.storage?.onChanged) {
      ExtensionLogger.warn(COMPONENTS.POPUP, 'Chrome storage API not available');
      return;
    }

    chrome.storage.onChanged.addListener(async (changes, areaName) => {
      if (areaName !== 'local') return;

      // Check for relevant changes
      const relevantChanges = [
        'monthlyCount',
        'subscription',
        'lastResetDate',
        'totalUnsubscribes'
      ];

      const hasRelevantChanges = relevantChanges.some(key => changes[key]);
      
      if (hasRelevantChanges) {
        await this.loadStoredData();
        this.updateUI();
      }
    });
  }

  /**
   * Update UI elements with current data
   */
  updateUI() {
    this.updateUsageDisplay();
    this.updateTierDisplay();
    this.updateButtonStates();
    this.updateDomainInfo();
  }

  /**
   * Update usage display
   */
  updateUsageDisplay() {
    const usageCountEl = document.getElementById('usageCount');
    const usageBarEl = document.getElementById('usageBar');
    const usageBarFillEl = document.getElementById('usageBarFill');
    const remainingCountEl = document.getElementById('remainingCount');
    const resetInfoEl = document.getElementById('resetInfo');

    if (usageCountEl) {
      usageCountEl.textContent = this.currentCount;
    }

    if (remainingCountEl) {
      if (this.subscription.monthlyLimit < 0) {
        remainingCountEl.textContent = 'Unlimited';
      } else {
        remainingCountEl.textContent = this.remainingCount;
      }
    }

    if (usageBarFillEl && usageBarEl && this.subscription.monthlyLimit > 0) {
      const percentage = Math.min(100, (this.currentCount / this.subscription.monthlyLimit) * 100);
      usageBarFillEl.style.width = `${percentage}%`;
      
      if (percentage > 90) {
        usageBarFillEl.classList.add('critical');
      } else if (percentage > 70) {
        usageBarFillEl.classList.add('warning');
      }
    }

    if (resetInfoEl) {
      resetInfoEl.textContent = this.daysUntilReset === 1 
        ? 'Resets tomorrow' 
        : `Resets in ${this.daysUntilReset} days`;
    }
  }

  /**
   * Update tier display
   */
  updateTierDisplay() {
    const tierNameEl = document.getElementById('tierName');
    const tierBadgeEl = document.getElementById('tierBadge');
    
    if (tierNameEl) {
      tierNameEl.textContent = this.subscription.tierName;
    }
    
    if (tierBadgeEl) {
      tierBadgeEl.className = `tier-badge tier-${this.subscription.tier}`;
    }
  }

  /**
   * Update button states based on current state
   */
  updateButtonStates() {
    const scanBtnEl = document.getElementById('scanBtn');
    const bulkScanBtnEl = document.getElementById('bulkScanBtn');
    
    if (this.limitReached) {
      if (scanBtnEl) {
        scanBtnEl.classList.add('disabled');
        scanBtnEl.title = 'Monthly limit reached. Upgrade for more scans.';
      }
      
      if (bulkScanBtnEl) {
        bulkScanBtnEl.classList.add('disabled');
        bulkScanBtnEl.title = 'Monthly limit reached. Upgrade for more scans.';
      }
    } else {
      if (scanBtnEl) {
        scanBtnEl.classList.remove('disabled');
        scanBtnEl.title = '';
      }
      
      if (bulkScanBtnEl) {
        bulkScanBtnEl.classList.remove('disabled');
        bulkScanBtnEl.title = '';
      }
    }
  }

  /**
   * Update domain info display
   */
  updateDomainInfo() {
    const domainInfoEl = document.getElementById('domainInfo');
    
    if (domainInfoEl && this.currentDomain) {
      if (this.currentDomain.includes('mail.google.com')) {
        domainInfoEl.textContent = 'Gmail detected';
        domainInfoEl.className = 'domain-info gmail';
      } else {
        domainInfoEl.textContent = `Not in Gmail (${this.currentDomain})`;
        domainInfoEl.className = 'domain-info not-gmail';
      }
    }
  }

  /**
   * Extract domain from URL
   */
  extractDomain(url) {
    try {
      if (!url) return '';
      
      const domain = new URL(url).hostname;
      return domain;
    } catch (error) {
      return '';
    }
  }

  /**
   * Show testing section for debug mode
   */
  showTestingSection() {
    const testingSection = document.getElementById('testingSection');
    if (testingSection) {
      testingSection.style.display = 'block';
      
      // Set up testing buttons
      this.setupTestingButtons();
    }
  }

  /**
   * Set up testing buttons
   */
  setupTestingButtons() {
    const testFreeTierBtn = document.getElementById('testFreeTier');
    const testPremiumTierBtn = document.getElementById('testPremiumTier');
    const testUnlimitedTierBtn = document.getElementById('testUnlimitedTier');
    
    if (testFreeTierBtn) {
      testFreeTierBtn.addEventListener('click', () => this.simulateTier('free'));
    }
    
    if (testPremiumTierBtn) {
      testPremiumTierBtn.addEventListener('click', () => this.simulateTier('premium'));
    }
    
    if (testUnlimitedTierBtn) {
      testUnlimitedTierBtn.addEventListener('click', () => this.simulateTier('unlimited'));
    }
    
    // Add event listeners for test action buttons
    const testIncrementUsageBtn = document.getElementById('testIncrementUsage');
    const testResetUsageBtn = document.getElementById('testResetUsage');
    const testResetStorageBtn = document.getElementById('testResetStorage');
    const testShowStatsBtn = document.getElementById('testShowStats');
    
    if (testIncrementUsageBtn) {
      testIncrementUsageBtn.addEventListener('click', () => this.testIncrementUsage());
    }
    
    if (testResetUsageBtn) {
      testResetUsageBtn.addEventListener('click', () => this.testResetUsage());
    }
    
    if (testResetStorageBtn) {
      testResetStorageBtn.addEventListener('click', () => this.testResetStorage());
    }
    
    if (testShowStatsBtn) {
      testShowStatsBtn.addEventListener('click', () => this.testShowStats());
    }
    
    // Update active state
    this.updateTestingButtonStates(this.subscription.tier);
  }

  /**
   * Simulate subscription tier for testing
   */
  async simulateTier(tier) {
    try {
      await SubscriptionUtils.updateSubscription(tier);
      await this.loadStoredData();
      this.updateUI();
      this.updateTestingButtonStates(tier);
    } catch (error) {
      ExtensionLogger.error(COMPONENTS.POPUP, 'Failed to simulate tier', error);
    }
  }

  /**
   * Test method to increment usage count
   */
  async testIncrementUsage() {
    try {
      const result = await SubscriptionUtils.incrementUsage();
      ExtensionLogger.info(COMPONENTS.POPUP, 'Test increment usage result:', result);
      await this.loadStoredData();
      this.updateUI();
    } catch (error) {
      ExtensionLogger.error(COMPONENTS.POPUP, 'Failed to increment usage', error);
    }
  }

  /**
   * Test method to reset usage count
   */
  async testResetUsage() {
    try {
      await StorageUtils.set({ monthlyCount: 0 });
      ExtensionLogger.info(COMPONENTS.POPUP, 'Usage count reset to 0');
      await this.loadStoredData();
      this.updateUI();
    } catch (error) {
      ExtensionLogger.error(COMPONENTS.POPUP, 'Failed to reset usage', error);
    }
  }

  /**
   * Test method to reset all storage
   */
  async testResetStorage() {
    try {
      await StorageUtils.clear();
      ExtensionLogger.info(COMPONENTS.POPUP, 'All storage cleared');
      await this.loadStoredData();
      this.updateUI();
    } catch (error) {
      ExtensionLogger.error(COMPONENTS.POPUP, 'Failed to reset storage', error);
    }
  }

  /**
   * Test method to show current stats
   */
  async testShowStats() {
    try {
      const stats = await SubscriptionUtils.getCurrentSubscription();
      ExtensionLogger.info(COMPONENTS.POPUP, 'Current subscription stats:', stats);
      
      // Show alert with stats
      const statsText = `
Tier: ${stats.tier} (${stats.tierName})
Monthly Limit: ${stats.monthlyLimit === -1 ? 'Unlimited' : stats.monthlyLimit}
Current Count: ${stats.currentCount}
Remaining: ${stats.remainingCount === -1 ? 'Unlimited' : stats.remainingCount}
Limit Reached: ${stats.limitReached}
Days Until Reset: ${stats.daysUntilReset}
      `.trim();
      
      alert(statsText);
    } catch (error) {
      ExtensionLogger.error(COMPONENTS.POPUP, 'Failed to show stats', error);
    }
  }

  /**
   * Update testing button states
   */
  updateTestingButtonStates(currentTier) {
    // Remove active class from all buttons
    const testButtons = document.querySelectorAll('.test-btn');
    testButtons.forEach(btn => btn.classList.remove('active'));
    
    // Add active class to current tier button
    const activeButton = document.getElementById(`test${currentTier.charAt(0).toUpperCase() + currentTier.slice(1)}Tier`);
    if (activeButton) {
      activeButton.classList.add('active');
    }
  }
}
