/**
 * Scan Manager for Popup
 * Handles email scanning operations and UI updates
 */

import { ExtensionLogger } from '../shared/logger.js';
import { COMPONENTS } from '../shared/constants.js';
import { MessageUtils } from '../shared/message-utils.js';
import { UIUtils } from '../shared/ui-utils.js';

/**
 * Manages email scanning operations
 */
export class ScanManager {
  constructor(popupInstance) {
    this.popup = popupInstance;
    this.isScanning = false;
    this.scanResults = null;
  }

  /**
   * Scan current email for unsubscribe links
   */
  async scanCurrentEmail() {
    if (this.popup.limitReached) {
      UIUtils.showStatus(
        document.getElementById('statusMessage'),
        'Monthly limit reached. Upgrade for unlimited scans.',
        'warning'
      );
      return;
    }

    try {
      this.updateScanStatus('Scanning current email...', 'loading');

      const tab = await MessageUtils.getCurrentTab();

      if (!tab.url || !tab.url.includes('mail.google.com')) {
        this.updateScanStatus('Please navigate to Gmail to scan emails', 'error');
        return;
      }

      // Ensure content script is loaded
      await this.ensureContentScriptLoaded(tab.id);

      // Send message to content script
      const response = await MessageUtils.sendToContent(tab.id, 'scanCurrentEmail');

      if (response && response.success) {
        if (response.found) {
          this.handleScanSuccess(response);
        } else {
          this.updateScanStatus('No unsubscribe links found in current email', 'info');
        }
      } else {
        throw new Error(response?.error || 'Scan failed');
      }

    } catch (error) {
      ExtensionLogger.error(COMPONENTS.POPUP, 'Current email scan failed', error);
      this.updateScanStatus(`Scan failed: ${error.message}`, 'error');
    }
  }

  /**
   * Perform bulk scan of emails
   */
  async bulkScanEmails() {
    if (this.popup.limitReached) {
      UIUtils.showStatus(
        document.getElementById('statusMessage'),
        'Monthly limit reached. Upgrade for unlimited scans.',
        'warning'
      );
      return;
    }

    try {
      this.updateScanStatus('Starting bulk scan...', 'loading');

      const tab = await MessageUtils.getCurrentTab();

      if (!tab.url || !tab.url.includes('mail.google.com')) {
        this.updateScanStatus('Please navigate to Gmail to scan emails', 'error');
        return;
      }

      // Ensure content script is loaded
      await this.ensureContentScriptLoaded(tab.id);

      // Send bulk scan message
      const response = await MessageUtils.sendToContent(tab.id, 'bulkScanEmails', {
        maxEmails: 50,
        includeRead: true
      });

      if (response && response.success) {
        this.handleBulkScanSuccess(response);
      } else {
        throw new Error(response?.error || 'Bulk scan failed');
      }

    } catch (error) {
      ExtensionLogger.error(COMPONENTS.POPUP, 'Bulk scan failed', error);
      this.updateScanStatus(`Bulk scan failed: ${error.message}`, 'error');
    }
  }

  /**
   * Open subscription dashboard in new tab
   */
  async openDashboard() {
    try {
      // Check if we have subscription data
      const subscriptions = await this.loadStoredSubscriptions();
      
      if (subscriptions && subscriptions.length > 0) {
        // Open dashboard in a new tab
        await chrome.tabs.create({
          url: chrome.runtime.getURL('dashboard.html'),
          active: true
        });
        
        this.updateScanStatus('Dashboard opened in new tab', 'success');
        
        // Close popup after short delay
        setTimeout(() => {
          window.close();
        }, 500);
      } else {
        this.updateScanStatus('No subscription data found. Run a bulk scan first.', 'info');
      }
    } catch (error) {
      ExtensionLogger.error(COMPONENTS.POPUP, 'Failed to open dashboard', error);
      this.updateScanStatus(`Failed to open dashboard: ${error.message}`, 'error');
    }
  }

  /**
   * Load stored subscriptions from Chrome storage
   */
  async loadStoredSubscriptions() {
    try {
      const { StorageUtils } = await import('../shared/storage-utils.js');
      const result = await StorageUtils.get('subscriptions');
      return result.subscriptions || [];
    } catch (error) {
      ExtensionLogger.error(COMPONENTS.POPUP, 'Failed to load subscriptions', error);
      return [];
    }
  }

  /**
   * Handle successful single email scan
   */
  handleScanSuccess(response) {
    this.scanResults = response;
    
    // Update UI to show results
    this.showScanResults(response);
    
    // Update status
    this.updateScanStatus(
      `Found ${response.count} unsubscribe link${response.count !== 1 ? 's' : ''}`,
      'success'
    );

    // Update stats
    if (this.popup.statsManager) {
      this.popup.statsManager.incrementStat('detectedEmails');
    }
  }

  /**
   * Handle successful bulk scan
   */
  handleBulkScanSuccess(response) {
    this.scanResults = response;
    
    // Show bulk scan results
    this.showBulkScanResults(response);
    
    // Update status
    this.updateScanStatus(
      `Scanned ${response.totalScanned} emails, found ${response.subscriptionsFound} subscriptions`,
      'success'
    );

    // Update stats
    if (this.popup.statsManager) {
      this.popup.statsManager.incrementStat('detectedEmails', response.totalScanned);
    }
  }

  /**
   * Show scan results in UI
   */
  showScanResults(results) {
    const mainScreen = document.getElementById('mainScreen');
    const resultsScreen = document.getElementById('resultsScreen');
    
    if (mainScreen) mainScreen.style.display = 'none';
    if (resultsScreen) {
      resultsScreen.style.display = 'block';
      this.populateResultsScreen(results);
    }
  }

  /**
   * Show bulk scan results
   */
  showBulkScanResults(results) {
    const mainScreen = document.getElementById('mainScreen');
    const bulkResultsScreen = document.getElementById('bulkResultsScreen');
    
    if (mainScreen) mainScreen.style.display = 'none';
    if (bulkResultsScreen) {
      bulkResultsScreen.style.display = 'block';
      this.populateBulkResultsScreen(results);
    }
  }

  /**
   * Populate results screen with scan data
   */
  populateResultsScreen(results) {
    const senderInfoEl = document.getElementById('senderInfo');
    const linkCountEl = document.getElementById('linkCount');
    const securityStatusEl = document.getElementById('securityStatus');
    
    if (senderInfoEl && results.senderInfo) {
      senderInfoEl.textContent = results.senderInfo.name || results.senderInfo.email || 'Unknown';
    }
    
    if (linkCountEl) {
      linkCountEl.textContent = results.count;
    }
    
    if (securityStatusEl && results.links) {
      const avgTrustLevel = results.links.reduce((sum, link) => sum + (link.trustLevel || 50), 0) / results.links.length;
      const status = avgTrustLevel >= 80 ? 'Safe' : avgTrustLevel >= 60 ? 'Caution' : 'Risky';
      securityStatusEl.textContent = status;
      securityStatusEl.className = `security-status ${status.toLowerCase()}`;
    }
  }

  /**
   * Populate bulk results screen
   */
  populateBulkResultsScreen(results) {
    const totalScannedEl = document.getElementById('totalScanned');
    const subscriptionsFoundEl = document.getElementById('subscriptionsFound');
    const scanSummaryEl = document.getElementById('scanSummary');
    
    if (totalScannedEl) {
      totalScannedEl.textContent = results.totalScanned;
    }
    
    if (subscriptionsFoundEl) {
      subscriptionsFoundEl.textContent = results.subscriptionsFound;
    }
    
    if (scanSummaryEl && results.subscriptions) {
      const highRiskCount = results.subscriptions.filter(s => s.securityStatus === 'dangerous').length;
      const safeCount = results.subscriptions.filter(s => s.securityStatus === 'safe').length;
      
      scanSummaryEl.innerHTML = `
        <div class="scan-summary-item">
          <span class="summary-label">Safe:</span>
          <span class="summary-value safe">${safeCount}</span>
        </div>
        <div class="scan-summary-item">
          <span class="summary-label">High Risk:</span>
          <span class="summary-value risky">${highRiskCount}</span>
        </div>
      `;
    }
  }

  /**
   * Update scan status message
   */
  updateScanStatus(message, type) {
    const statusEl = document.getElementById('statusMessage');
    UIUtils.showStatus(statusEl, message, type);
    
    // Update button states
    this.updateScanButtonStates(type === 'loading');
  }

  /**
   * Update scan button states
   */
  updateScanButtonStates(isScanning) {
    const scanBtnEl = document.getElementById('scanBtn');
    const bulkScanBtnEl = document.getElementById('bulkScanBtn');
    
    if (isScanning) {
      if (scanBtnEl) {
        scanBtnEl.disabled = true;
        scanBtnEl.innerHTML = '<span class="btn-icon">⏳</span> Scanning...';
      }
      
      if (bulkScanBtnEl) {
        bulkScanBtnEl.disabled = true;
        bulkScanBtnEl.innerHTML = '<span class="btn-icon">⏳</span> Scanning...';
      }
    } else {
      if (scanBtnEl) {
        scanBtnEl.disabled = false;
        scanBtnEl.innerHTML = '<span class="btn-icon">🔍</span> Scan Current Email';
      }
      
      if (bulkScanBtnEl) {
        bulkScanBtnEl.disabled = false;
        bulkScanBtnEl.innerHTML = '<span class="btn-icon">🔍</span> Bulk Scan Inbox';
      }
    }
  }

  /**
   * Ensure content script is loaded in tab
   */
  async ensureContentScriptLoaded(tabId) {
    try {
      // First try to ping the content script
      const response = await MessageUtils.sendToContent(tabId, 'ping');
      if (response) {
        return true;
      }
    } catch (error) {
      // Content script not present, inject it
      try {
        await chrome.scripting.executeScript({
          target: { tabId },
          files: ['content.js']
        });
        
        ExtensionLogger.info(COMPONENTS.POPUP, 'Content script injected successfully');
        
        // Wait a bit for the script to initialize
        await new Promise(resolve => setTimeout(resolve, 500));
        
        return true;
      } catch (injectionError) {
        console.error('Failed to inject content script:', injectionError);
        throw new Error('Failed to load content script');
      }
    }
    
    return false;
  }

  /**
   * Show main screen (back from results)
   */
  showMainScreen() {
    const mainScreen = document.getElementById('mainScreen');
    const resultsScreen = document.getElementById('resultsScreen');
    const bulkResultsScreen = document.getElementById('bulkResultsScreen');
    
    if (mainScreen) mainScreen.style.display = 'block';
    if (resultsScreen) resultsScreen.style.display = 'none';
    if (bulkResultsScreen) bulkResultsScreen.style.display = 'none';
    
    // Clear status
    this.updateScanStatus('', 'info');
  }
}
