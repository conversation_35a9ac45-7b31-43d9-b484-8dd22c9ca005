/**
 * Settings Manager for Popup
 * Handles settings display and management
 */

import { ExtensionLogger } from '../shared/logger.js';
import { COMPONENTS, STORAGE_KEYS } from '../shared/constants.js';
import { StorageUtils } from '../shared/storage-utils.js';
import { UIUtils } from '../shared/ui-utils.js';

/**
 * Manages extension settings
 */
export class SettingsManager {
  constructor(popupInstance) {
    this.popup = popupInstance;
    this.settings = {
      notifications: true,
      autoScan: true,
      securityWarnings: true,
      debugMode: false
    };
  }

  /**
   * Load settings from storage
   */
  async loadSettings() {
    try {
      const data = await StorageUtils.get('settings');
      
      if (data.settings) {
        this.settings = { ...this.settings, ...data.settings };
      }
      
      this.updateSettingsUI();
      
      ExtensionLogger.info(COMPONENTS.POPUP, 'Settings loaded', this.settings);
    } catch (error) {
      ExtensionLogger.error(COMPONENTS.POPUP, 'Failed to load settings', error);
    }
  }

  /**
   * Save settings to storage
   */
  async saveSettings() {
    try {
      await StorageUtils.set({ settings: this.settings });
      
      ExtensionLogger.info(COMPONENTS.POPUP, 'Settings saved', this.settings);
      
      UIUtils.showStatus(
        document.getElementById('statusMessage'),
        'Settings saved successfully',
        'success'
      );
    } catch (error) {
      ExtensionLogger.error(COMPONENTS.POPUP, 'Failed to save settings', error);
      
      UIUtils.showStatus(
        document.getElementById('statusMessage'),
        'Failed to save settings',
        'error'
      );
    }
  }

  /**
   * Show settings screen
   */
  async showSettings() {
    const mainScreen = document.getElementById('mainScreen');
    const settingsScreen = document.getElementById('settingsScreen');
    
    if (mainScreen) mainScreen.style.display = 'none';
    if (settingsScreen) {
      settingsScreen.style.display = 'block';
      
      // Load current settings
      await this.loadSettings();
      
      // Set up settings event listeners
      this.setupSettingsEventListeners();
    }
  }

  /**
   * Set up event listeners for settings controls
   */
  setupSettingsEventListeners() {
    // Notification settings
    const notificationsToggle = document.getElementById('notificationsToggle');
    if (notificationsToggle) {
      notificationsToggle.addEventListener('change', (e) => {
        this.settings.notifications = e.target.checked;
        this.saveSettings();
      });
    }

    // Auto-scan settings
    const autoScanToggle = document.getElementById('autoScanToggle');
    if (autoScanToggle) {
      autoScanToggle.addEventListener('change', (e) => {
        this.settings.autoScan = e.target.checked;
        this.saveSettings();
      });
    }

    // Security warnings settings
    const securityWarningsToggle = document.getElementById('securityWarningsToggle');
    if (securityWarningsToggle) {
      securityWarningsToggle.addEventListener('change', (e) => {
        this.settings.securityWarnings = e.target.checked;
        this.saveSettings();
      });
    }

    // Debug mode settings
    const debugModeToggle = document.getElementById('debugModeToggle');
    if (debugModeToggle) {
      debugModeToggle.addEventListener('change', (e) => {
        this.settings.debugMode = e.target.checked;
        this.saveSettings();
        
        // Update popup debug mode
        this.popup.debugMode = e.target.checked;
        
        if (e.target.checked) {
          this.popup.showTestingSection();
        } else {
          this.hideTestingSection();
        }
      });
    }

    // Clear data button
    const clearDataBtn = document.getElementById('clearDataBtn');
    if (clearDataBtn) {
      clearDataBtn.addEventListener('click', () => this.showClearDataConfirmation());
    }

    // Export data button
    const exportDataBtn = document.getElementById('exportDataBtn');
    if (exportDataBtn) {
      exportDataBtn.addEventListener('click', () => this.exportUserData());
    }

    // Reset settings button
    const resetSettingsBtn = document.getElementById('resetSettingsBtn');
    if (resetSettingsBtn) {
      resetSettingsBtn.addEventListener('click', () => this.showResetSettingsConfirmation());
    }
  }

  /**
   * Update settings UI with current values
   */
  updateSettingsUI() {
    const notificationsToggle = document.getElementById('notificationsToggle');
    const autoScanToggle = document.getElementById('autoScanToggle');
    const securityWarningsToggle = document.getElementById('securityWarningsToggle');
    const debugModeToggle = document.getElementById('debugModeToggle');

    if (notificationsToggle) {
      notificationsToggle.checked = this.settings.notifications;
    }

    if (autoScanToggle) {
      autoScanToggle.checked = this.settings.autoScan;
    }

    if (securityWarningsToggle) {
      securityWarningsToggle.checked = this.settings.securityWarnings;
    }

    if (debugModeToggle) {
      debugModeToggle.checked = this.settings.debugMode;
    }
  }

  /**
   * Hide testing section
   */
  hideTestingSection() {
    const testingSection = document.getElementById('testingSection');
    if (testingSection) {
      testingSection.style.display = 'none';
    }
  }

  /**
   * Show clear data confirmation
   */
  async showClearDataConfirmation() {
    const confirmed = await this.showConfirmationDialog(
      'Clear All Data',
      'This will permanently delete all your subscription data, statistics, and settings. This action cannot be undone. Are you sure?'
    );

    if (confirmed) {
      await this.clearAllData();
    }
  }

  /**
   * Clear all extension data
   */
  async clearAllData() {
    try {
      // Clear all storage
      await chrome.storage.local.clear();
      
      // Reset settings to defaults
      this.settings = {
        notifications: true,
        autoScan: true,
        securityWarnings: true,
        debugMode: false
      };
      
      // Save default settings
      await this.saveSettings();
      
      // Update UI
      this.updateSettingsUI();
      
      // Reload popup data
      await this.popup.loadStoredData();
      this.popup.updateUI();
      
      UIUtils.showStatus(
        document.getElementById('statusMessage'),
        'All data cleared successfully',
        'success'
      );
      
      ExtensionLogger.info(COMPONENTS.POPUP, 'All data cleared');
    } catch (error) {
      ExtensionLogger.error(COMPONENTS.POPUP, 'Failed to clear data', error);
      
      UIUtils.showStatus(
        document.getElementById('statusMessage'),
        'Failed to clear data',
        'error'
      );
    }
  }

  /**
   * Export user data
   */
  async exportUserData() {
    try {
      // Get all data from storage
      const allData = await chrome.storage.local.get(null);
      
      const exportData = {
        ...allData,
        exportDate: new Date().toISOString(),
        version: '1.0.0'
      };
      
      // Create and download file
      const blob = new Blob([JSON.stringify(exportData, null, 2)], {
        type: 'application/json'
      });
      
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `email-unsubscriber-data-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      
      UIUtils.showStatus(
        document.getElementById('statusMessage'),
        'Data exported successfully',
        'success'
      );
      
      ExtensionLogger.info(COMPONENTS.POPUP, 'User data exported');
    } catch (error) {
      ExtensionLogger.error(COMPONENTS.POPUP, 'Failed to export data', error);
      
      UIUtils.showStatus(
        document.getElementById('statusMessage'),
        'Failed to export data',
        'error'
      );
    }
  }

  /**
   * Show reset settings confirmation
   */
  async showResetSettingsConfirmation() {
    const confirmed = await this.showConfirmationDialog(
      'Reset Settings',
      'This will reset all settings to their default values. Are you sure?'
    );

    if (confirmed) {
      await this.resetSettings();
    }
  }

  /**
   * Reset settings to defaults
   */
  async resetSettings() {
    try {
      this.settings = {
        notifications: true,
        autoScan: true,
        securityWarnings: true,
        debugMode: false
      };
      
      await this.saveSettings();
      this.updateSettingsUI();
      
      // Update popup debug mode
      this.popup.debugMode = false;
      this.hideTestingSection();
      
      UIUtils.showStatus(
        document.getElementById('statusMessage'),
        'Settings reset to defaults',
        'success'
      );
      
      ExtensionLogger.info(COMPONENTS.POPUP, 'Settings reset to defaults');
    } catch (error) {
      ExtensionLogger.error(COMPONENTS.POPUP, 'Failed to reset settings', error);
      
      UIUtils.showStatus(
        document.getElementById('statusMessage'),
        'Failed to reset settings',
        'error'
      );
    }
  }

  /**
   * Show confirmation dialog
   */
  async showConfirmationDialog(title, message) {
    return new Promise((resolve) => {
      UIUtils.showConfirmation(title, message, 
        () => resolve(true),
        () => resolve(false)
      );
    });
  }

  /**
   * Get current settings
   */
  getSettings() {
    return { ...this.settings };
  }

  /**
   * Update a specific setting
   */
  async updateSetting(key, value) {
    if (this.settings.hasOwnProperty(key)) {
      this.settings[key] = value;
      await this.saveSettings();
      return true;
    }
    return false;
  }
}
