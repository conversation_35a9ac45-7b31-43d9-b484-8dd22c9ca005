/**
 * Stats Manager for Popup
 * Handles statistics display and management
 */

import { ExtensionLogger } from '../shared/logger.js';
import { COMPONENTS } from '../shared/constants.js';
import { MessageUtils } from '../shared/message-utils.js';
import { UIUtils } from '../shared/ui-utils.js';

/**
 * Manages statistics display and updates
 */
export class StatsManager {
  constructor(popupInstance) {
    this.popup = popupInstance;
    this.stats = {
      totalUnsubscribes: 0,
      monthlyCount: 0,
      detectedEmails: 0,
      successfulUnsubscribes: 0,
      blockedSuspiciousLinks: 0
    };
  }

  /**
   * Load statistics from background
   */
  async loadStats() {
    try {
      const response = await MessageUtils.sendToBackground('getStats');
      
      if (response && response.success) {
        this.stats = response.stats;
        this.updateStatsDisplay();
        
        ExtensionLogger.info(COMPONENTS.POPUP, 'Stats loaded', this.stats);
      } else {
        ExtensionLogger.warn(COMPONENTS.POPUP, 'Failed to load stats', response);
      }
    } catch (error) {
      ExtensionLogger.error(COMPONENTS.POPUP, 'Error loading stats', error);
    }
  }

  /**
   * Update statistics display in UI
   */
  updateStatsDisplay() {
    this.updateMainStats();
    this.updateDetailedStats();
  }

  /**
   * Update main statistics display
   */
  updateMainStats() {
    const totalUnsubscribesEl = document.getElementById('totalUnsubscribes');
    const monthlyCountEl = document.getElementById('monthlyCount');
    
    if (totalUnsubscribesEl) {
      totalUnsubscribesEl.textContent = UIUtils.formatNumber(this.stats.totalUnsubscribes || 0);
    }
    
    if (monthlyCountEl) {
      monthlyCountEl.textContent = UIUtils.formatNumber(this.stats.monthlyCount || 0);
    }
  }

  /**
   * Update detailed statistics display
   */
  updateDetailedStats() {
    const detectedEmailsEl = document.getElementById('detectedEmails');
    const successfulUnsubscribesEl = document.getElementById('successfulUnsubscribes');
    const blockedLinksEl = document.getElementById('blockedSuspiciousLinks');
    
    if (detectedEmailsEl) {
      detectedEmailsEl.textContent = UIUtils.formatNumber(this.stats.detectedEmails || 0);
    }
    
    if (successfulUnsubscribesEl) {
      successfulUnsubscribesEl.textContent = UIUtils.formatNumber(this.stats.successfulUnsubscribes || 0);
    }
    
    if (blockedLinksEl) {
      blockedLinksEl.textContent = UIUtils.formatNumber(this.stats.blockedSuspiciousLinks || 0);
    }
  }

  /**
   * Show detailed statistics view
   */
  showDetailedStats() {
    const mainScreen = document.getElementById('mainScreen');
    const statsScreen = document.getElementById('statsScreen');
    
    if (mainScreen) mainScreen.style.display = 'none';
    if (statsScreen) {
      statsScreen.style.display = 'block';
      this.loadStats(); // Refresh stats when showing
    }
  }

  /**
   * Calculate success rate
   */
  getSuccessRate() {
    if (this.stats.detectedEmails === 0) return 0;
    return Math.round((this.stats.successfulUnsubscribes / this.stats.detectedEmails) * 100);
  }

  /**
   * Get usage percentage for current month
   */
  getUsagePercentage() {
    if (this.popup.subscription.monthlyLimit <= 0) return 0; // Unlimited
    return Math.min(100, (this.stats.monthlyCount / this.popup.subscription.monthlyLimit) * 100);
  }

  /**
   * Format time period for display
   */
  formatTimePeriod(days) {
    if (days === 1) return 'yesterday';
    if (days <= 7) return `${days} days ago`;
    if (days <= 30) return `${Math.floor(days / 7)} weeks ago`;
    return `${Math.floor(days / 30)} months ago`;
  }

  /**
   * Get trend indicator for stats
   */
  getTrendIndicator(current, previous) {
    if (previous === 0) return 'new';
    
    const change = ((current - previous) / previous) * 100;
    
    if (change > 10) return 'up';
    if (change < -10) return 'down';
    return 'stable';
  }

  /**
   * Export statistics data
   */
  exportStats() {
    const exportData = {
      ...this.stats,
      subscription: this.popup.subscription,
      exportDate: new Date().toISOString(),
      version: '1.0.0'
    };
    
    const blob = new Blob([JSON.stringify(exportData, null, 2)], {
      type: 'application/json'
    });
    
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `email-unsubscriber-stats-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    ExtensionLogger.info(COMPONENTS.POPUP, 'Stats exported');
  }

  /**
   * Reset monthly statistics
   */
  async resetMonthlyStats() {
    try {
      const confirmed = await this.showResetConfirmation();
      if (!confirmed) return;
      
      const response = await MessageUtils.sendToBackground('resetMonthlyStats');
      
      if (response && response.success) {
        await this.loadStats();
        UIUtils.showStatus(
          document.getElementById('statusMessage'),
          'Monthly statistics reset successfully',
          'success'
        );
      } else {
        throw new Error(response?.error || 'Failed to reset stats');
      }
    } catch (error) {
      ExtensionLogger.error(COMPONENTS.POPUP, 'Failed to reset monthly stats', error);
      UIUtils.showStatus(
        document.getElementById('statusMessage'),
        'Failed to reset statistics',
        'error'
      );
    }
  }

  /**
   * Show reset confirmation dialog
   */
  async showResetConfirmation() {
    return new Promise((resolve) => {
      UIUtils.showConfirmation(
        'Reset Monthly Statistics',
        'Are you sure you want to reset your monthly usage statistics? This action cannot be undone.',
        () => resolve(true),
        () => resolve(false)
      );
    });
  }

  /**
   * Update stats after successful operation
   */
  incrementStat(statName, amount = 1) {
    if (this.stats.hasOwnProperty(statName)) {
      this.stats[statName] += amount;
      this.updateStatsDisplay();
    }
  }

  /**
   * Get formatted stats summary for display
   */
  getStatsSummary() {
    return {
      totalUnsubscribes: UIUtils.formatNumber(this.stats.totalUnsubscribes || 0),
      monthlyCount: UIUtils.formatNumber(this.stats.monthlyCount || 0),
      successRate: `${this.getSuccessRate()}%`,
      usagePercentage: `${Math.round(this.getUsagePercentage())}%`,
      detectedEmails: UIUtils.formatNumber(this.stats.detectedEmails || 0),
      blockedLinks: UIUtils.formatNumber(this.stats.blockedSuspiciousLinks || 0)
    };
  }
}
