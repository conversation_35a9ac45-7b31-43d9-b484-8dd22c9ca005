/**
 * Shared constants and configuration for the Email Unsubscriber extension
 * Centralized configuration to avoid duplication across modules
 */

// Subscription tier configuration
export const SUBSCRIPTION_TIERS = {
  free: {
    name: 'Free',
    monthlyLimit: 20,
    price: 0,
    features: [
      'Basic email scanning',
      'Basic unsubscribe',
      'Security warnings',
      'Usage tracking'
    ]
  },
  premium: {
    name: 'Premium',
    monthlyLimit: 500,
    price: 4.99,
    features: [
      'All Free features',
      'Advanced analytics',
      'Bulk operations',
      'AI categorization',
      'Priority support'
    ]
  },
  unlimited: {
    name: 'Unlimited',
    monthlyLimit: -1, // -1 means unlimited
    price: 9.99,
    features: [
      'All Premium features',
      'Unlimited unsubscribes',
      'Multi-account support',
      'Team features',
      'Enterprise support'
    ]
  }
}

// Security status levels
export const SECURITY_STATUS = {
  SAFE: 'safe',
  CAUTION: 'caution',
  DANGEROUS: 'dangerous',
  UNKNOWN: 'unknown'
}

// Component types for logging
export const COMPONENTS = {
  GMAIL: 'Gmail',
  POPUP: 'Popup',
  BACKGROUND: 'Background',
  CONTENT: 'Content',
  EXTRACTION: 'Extraction',
  DASHBOARD: 'Dashboard',
  STORAGE: 'Storage',
  RUNTIME: 'Runtime'
}

// Log levels
export const LOG_LEVELS = {
  ERROR: 'error',
  WARN: 'warn',
  INFO: 'info',
  DEBUG: 'debug'
}

// Storage keys
export const STORAGE_KEYS = {
  SUBSCRIPTIONS: 'subscriptions',
  USAGE_STATS: 'usage_stats',
  USER_PREFERENCES: 'user_preferences',
  DEBUG_LOGS: 'debug_logs',
  EXTRACTION_CACHE: 'extraction_cache',
  SECURITY_OVERRIDES: 'security_overrides',
  LOCAL_BLACKLIST: 'local_blacklist'
}

// Message types for inter-component communication
export const MESSAGE_TYPES = {
  SCAN_EMAIL: 'scan_email',
  BULK_SCAN: 'bulk_scan',
  UNSUBSCRIBE: 'unsubscribe',
  GET_STATS: 'get_stats',
  INCREMENT_USAGE: 'increment_usage',
  UPGRADE_SUBSCRIPTION: 'upgrade_subscription',
  REPORT_FALSE_POSITIVE: 'report_false_positive',
  SET_MANUAL_OVERRIDE: 'set_manual_override',
  DEBUG_LOG: 'DEBUG_LOG'
}

// Gmail selectors and patterns
export const GMAIL_SELECTORS = {
  EMAIL_CONTAINER: '[data-message-id]',
  SENDER_ELEMENT: '[email]',
  SUBJECT_ELEMENT: '[data-thread-id]',
  BODY_ELEMENT: '.ii.gt',
  UNSUBSCRIBE_LINK: 'a[href*="unsubscribe"]'
}

// Unsubscribe link patterns
export const UNSUBSCRIBE_PATTERNS = {
  COMMON_TEXTS: [
    'unsubscribe',
    'opt out',
    'opt-out',
    'remove',
    'stop emails',
    'stop receiving',
    'manage preferences',
    'update preferences',
    'email preferences',
    'manage subscription',
    'cancel subscription',
    'update subscription',
    'email settings',
    'notification settings',
    'stop notifications',
    'turn off',
    'disable',
    'manage emails',
    'email options',
    'subscription center',
    'preference center',
    'communications preferences',
    'mail preferences',
    'email frequency',
    'one-click unsubscribe',
    'instant unsubscribe',
    'list-unsubscribe',
    'remove me',
    'take me off',
    'no longer interested',
    'don\'t want these emails',
    'stop sending',
    'manage communication',
    'communication preferences'
  ],
  URL_PATTERNS: [
    'unsubscribe',
    'opt-out',
    'optout',
    'remove-subscription',
    'manage-subscription',
    'email-preferences',
    'preferences/email',
    'settings/email',
    'subscription-center',
    'newsletter-unsubscribe',
    'unsubscribe-email',
    'email-unsubscribe',
    'stop-emails',
    'list-unsubscribe',
    'mailto:unsubscribe',
    'unsub',
    'preference-center',
    'subscription-management',
    'email-management'
  ],
  SUSPICIOUS_PATTERNS: [
    'click here',
    'verify account',
    'confirm email',
    'update payment',
    'billing issue',
    'account suspended',
    'urgent action required',
    'verify immediately'
  ]
}

// Security validation patterns
export const SECURITY_PATTERNS = {
  LEGITIMATE_DOMAINS: [
    'mailchimp.com',
    'sendgrid.com',
    'constantcontact.com',
    'campaignmonitor.com'
  ],
  SUSPICIOUS_INDICATORS: [
    'bit.ly',
    'tinyurl.com',
    'suspicious-domain.com'
  ]
}

// Performance and limits
export const PERFORMANCE_LIMITS = {
  MAX_LOGS: 500,
  MAX_CACHE_SIZE: 1000,
  SCAN_TIMEOUT: 30000,
  BULK_SCAN_BATCH_SIZE: 50
}

// UI constants
export const UI_CONSTANTS = {
  POPUP_WIDTH: 400,
  POPUP_HEIGHT: 600,
  DASHBOARD_Z_INDEX: 10000,
  ANIMATION_DURATION: 300
}

// Extension metadata
export const EXTENSION_INFO = {
  NAME: 'Email Unsubscriber',
  VERSION: '1.0.0',
  DESCRIPTION: 'Privacy-focused email unsubscription tool for Gmail'
}