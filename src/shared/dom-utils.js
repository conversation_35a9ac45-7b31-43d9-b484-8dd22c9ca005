/**
 * Shared DOM utilities for Chrome Extension
 * Provides common DOM manipulation functions across content scripts and popup
 */

import { UI_CONSTANTS } from './constants.js'

/**
 * DOM utility functions for Chrome Extension
 */
export const DOMUtils = {
  /**
   * Safely create DOM element with attributes and content
   * @param {string} tagName - HTML tag name
   * @param {Object} attributes - Element attributes
   * @param {string} content - Element content
   * @returns {HTMLElement} Created element
   */
  createElement(tagName, attributes = {}, content = '') {
    const element = document.createElement(tagName)
    
    // Set attributes
    for (const [key, value] of Object.entries(attributes)) {
      if (key === 'className') {
        element.className = value
      } else if (key === 'innerHTML') {
        element.innerHTML = value
      } else {
        element.setAttribute(key, value)
      }
    }
    
    // Set content
    if (content) {
      element.textContent = content
    }
    
    return element
  },

  /**
   * Safely query selector with error handling
   * @param {string} selector - CSS selector
   * @param {Element} parent - Parent element (default: document)
   * @returns {Element|null} Found element or null
   */
  querySelector(selector, parent = document) {
    try {
      return parent.querySelector(selector)
    } catch (error) {
      console.error('Invalid selector:', selector, error)
      return null
    }
  },

  /**
   * Safely query all selectors with error handling
   * @param {string} selector - CSS selector
   * @param {Element} parent - Parent element (default: document)
   * @returns {NodeList} Found elements
   */
  querySelectorAll(selector, parent = document) {
    try {
      return parent.querySelectorAll(selector)
    } catch (error) {
      console.error('Invalid selector:', selector, error)
      return []
    }
  },

  /**
   * Check if element exists and is visible
   * @param {Element} element - Element to check
   * @returns {boolean} True if element exists and is visible
   */
  isElementVisible(element) {
    if (!element) return false
    
    const rect = element.getBoundingClientRect()
    return (
      rect.width > 0 &&
      rect.height > 0 &&
      rect.top >= 0 &&
      rect.left >= 0 &&
      rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
      rect.right <= (window.innerWidth || document.documentElement.clientWidth)
    )
  },

  /**
   * Wait for element to appear in DOM
   * @param {string} selector - CSS selector
   * @param {number} timeout - Timeout in milliseconds
   * @param {Element} parent - Parent element (default: document)
   * @returns {Promise<Element>} Promise resolving to found element
   */
  waitForElement(selector, timeout = 5000, parent = document) {
    return new Promise((resolve, reject) => {
      const element = this.querySelector(selector, parent)
      if (element) {
        resolve(element)
        return
      }

      const observer = new MutationObserver((mutations) => {
        const element = this.querySelector(selector, parent)
        if (element) {
          observer.disconnect()
          resolve(element)
        }
      })

      observer.observe(parent, {
        childList: true,
        subtree: true
      })

      setTimeout(() => {
        observer.disconnect()
        reject(new Error(`Element ${selector} not found within ${timeout}ms`))
      }, timeout)
    })
  },

  /**
   * Add CSS styles to element
   * @param {Element} element - Target element
   * @param {Object} styles - CSS styles object
   */
  addStyles(element, styles) {
    if (!element || !styles) return
    
    for (const [property, value] of Object.entries(styles)) {
      element.style[property] = value
    }
  },

  /**
   * Remove element safely
   * @param {Element} element - Element to remove
   */
  removeElement(element) {
    if (element && element.parentNode) {
      element.parentNode.removeChild(element)
    }
  },

  /**
   * Create modal overlay
   * @param {Object} options - Modal options
   * @returns {HTMLElement} Modal overlay element
   */
  createModalOverlay(options = {}) {
    const overlay = this.createElement('div', {
      className: 'unsubscriber-modal-overlay',
      style: `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: ${UI_CONSTANTS.DASHBOARD_Z_INDEX};
        display: flex;
        align-items: center;
        justify-content: center;
      `
    })

    const modal = this.createElement('div', {
      className: 'unsubscriber-modal',
      style: `
        background: white;
        border-radius: 8px;
        padding: 20px;
        max-width: 500px;
        max-height: 80vh;
        overflow-y: auto;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        position: relative;
      `
    })

    if (options.title) {
      const title = this.createElement('h3', {
        style: 'margin-top: 0; color: #333;'
      }, options.title)
      modal.appendChild(title)
    }

    if (options.content) {
      const content = this.createElement('div', {
        innerHTML: options.content
      })
      modal.appendChild(content)
    }

    if (options.closable !== false) {
      const closeBtn = this.createElement('button', {
        className: 'unsubscriber-close-btn',
        style: `
          position: absolute;
          top: 10px;
          right: 10px;
          background: none;
          border: none;
          font-size: 20px;
          cursor: pointer;
          color: #666;
        `
      }, '×')

      closeBtn.addEventListener('click', () => {
        this.removeElement(overlay)
      })

      modal.appendChild(closeBtn)
    }

    overlay.appendChild(modal)
    return overlay
  },

  /**
   * Create button element with consistent styling
   * @param {Object} options - Button options
   * @returns {HTMLElement} Button element
   */
  createButton(options = {}) {
    const button = this.createElement('button', {
      className: `unsubscriber-btn ${options.className || ''}`,
      style: `
        padding: 8px 16px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
        transition: background-color 0.2s;
        ${options.style || ''}
      `
    }, options.text || 'Button')

    if (options.onClick) {
      button.addEventListener('click', options.onClick)
    }

    return button
  },

  /**
   * Create progress bar element
   * @param {Object} options - Progress bar options
   * @returns {HTMLElement} Progress bar element
   */
  createProgressBar(options = {}) {
    const container = this.createElement('div', {
      className: 'unsubscriber-progress-container',
      style: `
        width: 100%;
        height: 20px;
        background-color: #f0f0f0;
        border-radius: 10px;
        overflow: hidden;
        ${options.containerStyle || ''}
      `
    })

    const bar = this.createElement('div', {
      className: 'unsubscriber-progress-bar',
      style: `
        height: 100%;
        background-color: #4CAF50;
        width: 0%;
        transition: width 0.3s ease;
        ${options.barStyle || ''}
      `
    })

    container.appendChild(bar)

    // Add update method
    container.updateProgress = (percentage) => {
      bar.style.width = `${Math.max(0, Math.min(100, percentage))}%`
    }

    return container
  },

  /**
   * Create loading spinner element
   * @param {Object} options - Spinner options
   * @returns {HTMLElement} Spinner element
   */
  createSpinner(options = {}) {
    const spinner = this.createElement('div', {
      className: 'unsubscriber-spinner',
      style: `
        display: inline-block;
        width: 20px;
        height: 20px;
        border: 3px solid #f3f3f3;
        border-top: 3px solid #3498db;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        ${options.style || ''}
      `
    })

    // Add CSS animation if not already present
    if (!document.querySelector('#unsubscriber-spinner-styles')) {
      const style = this.createElement('style', {
        id: 'unsubscriber-spinner-styles'
      })
      style.textContent = `
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `
      document.head.appendChild(style)
    }

    return spinner
  },

  /**
   * Sanitize HTML content to prevent XSS
   * @param {string} html - HTML content to sanitize
   * @returns {string} Sanitized HTML
   */
  sanitizeHTML(html) {
    if (!html) return ''
    
    // Basic HTML sanitization - remove script tags and dangerous attributes
    return html
      .replace(/<script[^>]*>.*?<\/script>/gi, '')
      .replace(/<iframe[^>]*>.*?<\/iframe>/gi, '')
      .replace(/on\w+="[^"]*"/gi, '')
      .replace(/on\w+='[^']*'/gi, '')
      .replace(/javascript:/gi, '')
  },

  /**
   * Debounce function calls
   * @param {Function} func - Function to debounce
   * @param {number} wait - Wait time in milliseconds
   * @returns {Function} Debounced function
   */
  debounce(func, wait) {
    let timeout
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout)
        func(...args)
      }
      clearTimeout(timeout)
      timeout = setTimeout(later, wait)
    }
  },

  /**
   * Throttle function calls
   * @param {Function} func - Function to throttle
   * @param {number} limit - Time limit in milliseconds
   * @returns {Function} Throttled function
   */
  throttle(func, limit) {
    let inThrottle
    return function(...args) {
      if (!inThrottle) {
        func.apply(this, args)
        inThrottle = true
        setTimeout(() => inThrottle = false, limit)
      }
    }
  },

  /**
   * Check if page is Gmail
   * @returns {boolean} True if current page is Gmail
   */
  isGmailPage() {
    return window.location.hostname === 'mail.google.com'
  },

  /**
   * Get Gmail email containers
   * @returns {NodeList} Email container elements
   */
  getGmailEmailContainers() {
    return this.querySelectorAll('[data-message-id]')
  },

  /**
   * Extract text content safely
   * @param {Element} element - Element to extract text from
   * @returns {string} Text content
   */
  getTextContent(element) {
    if (!element) return ''
    return element.textContent || element.innerText || ''
  }
}

export default DOMUtils