/**
 * Shared Gmail utilities for Chrome Extension
 * Provides Gmail-specific DOM manipulation and detection functions
 */

import { GMAIL_SELECTORS } from './constants.js';
import { ExtensionLogger } from './logger.js';

/**
 * Gmail utility functions for Chrome Extension
 */
export const GmailUtils = {
  /**
   * Check if current page is Gmail
   * @returns {boolean} True if on Gmail
   */
  isGmailPage() {
    const { href, hostname, pathname } = window.location;
    
    // Check if we're on Gmail domain
    const isGmailDomain = hostname === 'mail.google.com';
    
    if (!isGmailDomain) {
      return false;
    }
    
    // More comprehensive Gmail app detection
    const isGmailApp = isGmailDomain && (
      pathname.startsWith('/mail/') ||
      pathname === '/mail' ||
      pathname === '/' ||
      href.includes('#inbox') ||
      href.includes('#sent') ||
      href.includes('#drafts') ||
      href.includes('#spam') ||
      href.includes('#trash') ||
      href.includes('#important') ||
      href.includes('#starred') ||
      href.includes('#all') ||
      href.includes('#label/') ||
      href.includes('#category/') ||
      href.includes('#search/') ||
      document.title.toLowerCase().includes('gmail') ||
      document.body.classList.contains('gmail_default') ||
      document.querySelector('.nH[role="main"]') !== null ||
      document.querySelector('.zA') !== null ||
      document.querySelector('.ii.gt') !== null ||
      document.querySelector('[data-thread-id]') !== null
    );
    
    return isGmailApp;
  },
  
  /**
   * Get Gmail email elements from the page
   * @returns {NodeList} Email elements
   */
  getEmailElements() {
    // Debug logging to understand what's on the page
    const debugLog = (message, elements) => {
      console.log(`[DEBUG] ${message}:`, elements.length, elements);
    };
    
    // First check if we're actually in inbox list view by looking for the email list table
    const inboxTable = document.querySelector('table[role="grid"]') || 
                      document.querySelector('tbody tr[jsaction]') ||
                      document.querySelector('.F.cf.zt tbody');
    
    const currentUrl = window.location.href;
    const isInboxUrl = currentUrl.includes('#inbox') || currentUrl.includes('#sent') || 
                      currentUrl.includes('#drafts') || currentUrl.includes('#spam') ||
                      currentUrl.includes('#trash') || currentUrl.includes('#starred');
    
    // If we're in inbox URL and have inbox table, prioritize inbox view
    if (isInboxUrl && inboxTable) {
      debugLog('Detected inbox list view, skipping conversation check', []);
      // Skip conversation view detection and go directly to inbox selectors
    } else {
      // Check if we're in conversation view (single email thread)
      const conversationView = document.querySelector('.ii.gt');
      if (conversationView && !isInboxUrl) {
        // In conversation view, get all individual messages
        const messages = document.querySelectorAll('.ii.gt');
        debugLog('Conversation view messages', messages);
        if (messages.length > 0) {
          return messages;
        }
      }
    }
    
    // For inbox/list view, try multiple selectors in order of preference
    const listSelectors = [
      // Most specific Gmail inbox row selectors first
      'table[role="grid"] tbody tr[jsaction]:not([aria-hidden="true"])',
      'tbody tr[jsaction*="click"]:not([aria-hidden="true"])',
      'tbody tr[jsaction*="rcuQ6b"]:not([aria-hidden="true"])',
      'tbody tr[jsaction*="JIbuQc"]:not([aria-hidden="true"])',
      
      // Thread-based selectors
      'tbody tr[data-thread-id]:not([aria-hidden="true"])',
      'tbody tr[data-legacy-thread-id]:not([aria-hidden="true"])',
      
      // Class-based selectors
      'tbody tr:not(.aKh):not(.aKi):not([aria-hidden="true"])',
      'tr.zA:not([aria-hidden="true"])',
      'tr.yW:not([aria-hidden="true"])',
      
      // Generic TR selectors
      'tr[jsaction*="click"]:not([aria-hidden="true"])',
      'tr[jsaction*="rcuQ6b"]:not([aria-hidden="true"])',
      'tr[jsaction*="JIbuQc"]:not([aria-hidden="true"])',
      
      // Div-based email elements
      '.zA[data-thread-id]',
      '.yW[data-thread-id]',
      
      // Message-based selectors (for conversation view)
      '.ii.gt[data-message-id]',
      '[data-message-id]',
      
      // Generic attribute selectors
      '[data-thread-id]',
      '[data-legacy-thread-id]'
    ];
    
    // Try each selector and return the first one that finds emails
    for (let i = 0; i < listSelectors.length; i++) {
      const selector = listSelectors[i];
      const elements = document.querySelectorAll(selector);
      debugLog(`Selector ${i + 1} (${selector})`, elements);
      
      if (elements.length > 0) {
        // Filter out non-email elements
        const emailElements = Array.from(elements).filter(el => {
          // More comprehensive email detection
          const isEmail = el.hasAttribute('data-thread-id') || 
                         el.hasAttribute('data-legacy-thread-id') ||
                         el.hasAttribute('data-message-id') ||
                         el.querySelector('[email]') ||
                         el.querySelector('.yW') ||
                         el.querySelector('.go') ||
                         el.querySelector('.bog') ||
                         el.querySelector('.y6') ||
                         el.classList.contains('zA') ||
                         el.classList.contains('yW') ||
                         (el.tagName === 'TR' && el.getAttribute('jsaction')) ||
                         (el.tagName === 'TR' && el.closest('tbody'));
          
          // Additional check: must not be hidden or system elements
          const isVisible = !el.hasAttribute('aria-hidden') || el.getAttribute('aria-hidden') !== 'true';
          const isNotSystem = !el.classList.contains('aKh') && !el.classList.contains('aKi');
          
          return isEmail && isVisible && isNotSystem;
        });
        
        debugLog(`Filtered emails for selector ${i + 1}`, emailElements);
        
        if (emailElements.length > 0) {
          return emailElements;
        }
      }
    }
    
    // Last resort - try to find any email-like elements in the main area
    const fallbackSelectors = [
      'div[role="main"] tr[jsaction]',
      'div[role="main"] tbody tr',
      '.nH[role="main"] tr',
      '.Cp[role="main"] tr',
      'div[role="main"] .zA',
      'div[role="main"] .yW'
    ];
    
    for (const selector of fallbackSelectors) {
      const elements = document.querySelectorAll(selector);
      debugLog(`Fallback selector (${selector})`, elements);
      
      if (elements.length > 0) {
        const filtered = Array.from(elements).filter(el => 
          !el.hasAttribute('aria-hidden') && 
          !el.classList.contains('aKh') && 
          !el.classList.contains('aKi')
        );
        
        if (filtered.length > 0) {
          return filtered;
        }
      }
    }
    
    console.log('[DEBUG] No email elements found with any selector');
    return [];
  },
  
  /**
   * Extract sender information from email element
   * @param {HTMLElement} emailElement - Email element
   * @returns {Object} Sender information
   */
  extractSenderInfo(emailElement) {
    const senderInfo = {
      name: '',
      email: '',
      element: null
    };
    
    console.log('[DEBUG] Extracting sender info from element:', emailElement);
    console.log('[DEBUG] Element classes:', emailElement.className);
    console.log('[DEBUG] Element id:', emailElement.id);
    
    // For expanded email elements (.ii.gt), we need to look for sender info in the conversation view
    // Gmail puts sender info in the email header area, not in the expanded content
    let searchElement = emailElement;
    
    // If this is an expanded email element, look for the conversation view header
    if (emailElement.classList.contains('ii') && emailElement.classList.contains('gt')) {
      console.log('[DEBUG] This is an expanded email element, looking for conversation header...');
      
      // Look for the conversation view header that contains sender information
      const possibleHeaders = [];
      
      // Method 1: Look for the conversation header (.adn.ads)
      const conversationHeader = document.querySelector('.adn.ads');
      if (conversationHeader) {
        possibleHeaders.push(conversationHeader);
        console.log('[DEBUG] Found conversation header (.adn.ads):', conversationHeader);
      }
      
      // Method 2: Look in the thread container (.Cp)
      const threadContainer = document.querySelector('.Cp');
      if (threadContainer) {
        // Look for email headers within the thread
        const emailHeaders = threadContainer.querySelectorAll('.ajz, .go, .yW, .yP, .a1, .qu, .bA4');
        emailHeaders.forEach(header => {
          if (header.textContent.includes('@')) {
            possibleHeaders.push(header);
            console.log('[DEBUG] Found potential email header in thread:', header);
          }
        });
      }
      
      // Method 3: Look for sender info in the current email's parent structure
      let currentParent = emailElement.parentElement;
      while (currentParent && currentParent !== document.body) {
        const senderElements = currentParent.querySelectorAll('[email], .go, .yW, .yP, .a1, .qu, .bA4, .ajz');
        senderElements.forEach(elem => {
          if (elem.textContent.includes('@') || elem.hasAttribute('email')) {
            possibleHeaders.push(elem);
            console.log('[DEBUG] Found sender element in parent:', elem);
          }
        });
        currentParent = currentParent.parentElement;
      }
      
      // Method 4: Look in the page for any visible sender information
      const allPossibleSenders = document.querySelectorAll('.go, .yW, .yP, .a1, .qu, .bA4, .ajz, .gb, .yX');
      allPossibleSenders.forEach(elem => {
        if (elem.textContent.includes('@') || elem.hasAttribute('email')) {
          possibleHeaders.push(elem);
          console.log('[DEBUG] Found potential sender on page:', elem);
        }
      });
      
      // Use the first valid header found
      for (const header of possibleHeaders) {
        if (header && (header.textContent.includes('@') || header.hasAttribute('email'))) {
          console.log('[DEBUG] Selected header for sender extraction:', header);
          searchElement = header;
          break;
        }
      }
    }
    
    console.log('[DEBUG] Using search element:', searchElement);
    
    // Try different selectors for sender information
    // Priority order: working selectors from backup system first
    const senderSelectors = [
      // Working selectors from backup system
      '.go .gb .g2[email]',
      '.yW .gb[email]',
      '.a1.aN[email]',
      '.qu .go .gb[email]',
      '.yW span[email]',
      '.yW span[name]',
      '.gb[email]',
      '.gb[name]',
      '.oL.aDm .oM .aDn .oZ-x3d .oY .aoo span[email]',
      
      // Additional selectors for expanded emails
      '.ajz span[email]',
      '.ajz .gb[email]',
      '.ajz .yW[email]',
      '.ajz .go[email]',
      '.ajz [email]',
      '.adn.ads span[email]',
      '.adn.ads .gb[email]',
      '.adn.ads [email]',
      
      // Current system selectors as fallback
      '[email]',
      '.go span[email]',
      '.yW span[email]',
      '.yP span[email]',
      '.yX span[email]',
      '.a1 span[email]',
      '.qu span[email]',
      '.bA4 span[email]',
      '.yW .yX',
      '.go .yX',
      '.yP .yX',
      '.qu .yX span',
      '.go .yX span',
      '.yW .yX span',
      '.a1 .yX span',
      '.bA4 .yX span'
    ];
    
    for (const selector of senderSelectors) {
      const element = searchElement.querySelector(selector);
      if (element) {
        console.log(`[DEBUG] Found element with selector ${selector}:`, element);
        senderInfo.element = element;
        
        // Extract email
        const emailAttr = element.getAttribute('email');
        const emailText = element.textContent.trim();
        senderInfo.email = emailAttr || emailText;
        
        // Extract name - check for name attribute first
        const nameAttr = element.getAttribute('name');
        const nameText = element.textContent.trim();
        senderInfo.name = nameAttr || nameText;
        
        console.log(`[DEBUG] Extracted - Email: ${senderInfo.email}, Name: ${senderInfo.name}`);
        
        if (senderInfo.email) {
          break;
        }
      }
    }
    
    // If no email found, try more aggressive selectors
    if (!senderInfo.email) {
      const fallbackSelectors = [
        '.hb',
        '.go .hb',
        '.yW .hb',
        '.yP .hb',
        '.a1 .hb',
        '.qu .hb',
        '.bA4 .hb',
        '.hb span',
        '.go .hb span',
        '.yW .hb span'
      ];
      
      for (const selector of fallbackSelectors) {
        const element = searchElement.querySelector(selector);
        if (element) {
          console.log(`[DEBUG] Fallback found element with selector ${selector}:`, element);
          const text = element.textContent.trim();
          
          // Check if text contains an email
          if (text.includes('@')) {
            senderInfo.email = text;
            senderInfo.name = text.split('@')[0]; // Use part before @ as fallback name
            console.log(`[DEBUG] Fallback extracted - Email: ${senderInfo.email}, Name: ${senderInfo.name}`);
            break;
          }
        }
      }
    }
    
    // If still no email, try to extract from anywhere in the element
    if (!senderInfo.email) {
      const fullText = searchElement.textContent || '';
      const emailMatch = fullText.match(/[\w\.-]+@[\w\.-]+\.\w+/);
      if (emailMatch) {
        senderInfo.email = emailMatch[0];
        senderInfo.name = emailMatch[0].split('@')[0];
        console.log(`[DEBUG] Regex extracted - Email: ${senderInfo.email}, Name: ${senderInfo.name}`);
      }
    }
    
    // Additional debug logging
    if (!senderInfo.email) {
      console.log('[DEBUG] No email found, trying DOM inspection...');
      console.log('[DEBUG] Search element HTML:', searchElement.outerHTML.substring(0, 500));
      console.log('[DEBUG] Available elements with email attr:', searchElement.querySelectorAll('[email]'));
      console.log('[DEBUG] Available elements with name attr:', searchElement.querySelectorAll('[name]'));
      console.log('[DEBUG] All span elements:', searchElement.querySelectorAll('span'));
    }
    
    console.log('[DEBUG] Final sender info:', senderInfo);
    return senderInfo;
  },
  
  /**
   * Extract subject from email element
   * @param {HTMLElement} emailElement - Email element
   * @returns {string} Email subject
   */
  extractSubject(emailElement) {
    const subjectSelectors = [
      '.bog',
      '.y6 span[id]',
      '.y6 span',
      '.yW .y6',
      '.zA .y6',
      '.yX .y6',
      '.subject',
      '[data-thread-id] .y6'
    ];
    
    for (const selector of subjectSelectors) {
      const element = emailElement.querySelector(selector);
      if (element && element.textContent.trim()) {
        return element.textContent.trim();
      }
    }
    
    return '';
  },
  
  /**
   * Check if email element is expanded (showing full content)
   * @param {HTMLElement} emailElement - Email element
   * @returns {boolean} True if expanded
   */
  isEmailExpanded(emailElement) {
    // Check for expanded content indicators
    const expandedIndicators = [
      '.ii.gt',
      '.adn.ads',
      '.ii',
      '.a3s.aiL',
      '.ii.gt .a3s'
    ];
    
    for (const selector of expandedIndicators) {
      if (emailElement.querySelector(selector)) {
        return true;
      }
    }
    
    return false;
  },
  
  /**
   * Check if email element is a list item (inbox view)
   * @param {HTMLElement} emailElement - Email element
   * @returns {boolean} True if list item
   */
  isEmailListItem(emailElement) {
    return emailElement.classList.contains('zA') ||
           emailElement.classList.contains('yW') ||
           emailElement.tagName === 'TR' ||
           emailElement.hasAttribute('data-thread-id') ||
           emailElement.hasAttribute('data-legacy-thread-id') ||
           emailElement.getAttribute('role') === 'listitem';
  },
  
  /**
   * Get thread ID from email element
   * @param {HTMLElement} emailElement - Email element
   * @returns {string} Thread ID
   */
  getThreadId(emailElement) {
    return emailElement.getAttribute('data-thread-id') ||
           emailElement.getAttribute('data-legacy-thread-id') ||
           emailElement.getAttribute('id') ||
           '';
  },
  
  /**
   * Wait for Gmail to load
   * @param {number} timeout - Timeout in milliseconds
   * @returns {Promise<boolean>} True if Gmail loaded
   */
  async waitForGmailLoad(timeout = 10000) {
    const startTime = Date.now();
    
    while (Date.now() - startTime < timeout) {
      if (this.isGmailPage() && document.querySelector('.nH[role="main"]')) {
        return true;
      }
      
      await new Promise(resolve => setTimeout(resolve, 100));
    }
    
    return false;
  },
  
  /**
   * Scroll element into view safely
   * @param {HTMLElement} element - Element to scroll to
   * @param {Object} options - Scroll options
   */
  scrollIntoView(element, options = {}) {
    if (!element) return;
    
    try {
      element.scrollIntoView({
        behavior: 'smooth',
        block: 'center',
        inline: 'nearest',
        ...options
      });
    } catch (error) {
      // Fallback for older browsers
      element.scrollIntoView();
    }
  },

  /**
   * Scroll to bottom of email list to load more emails
   * @param {number} maxAttempts - Maximum scroll attempts
   * @returns {Promise<number>} Number of new emails loaded
   */
  async scrollToLoadMoreEmails(maxAttempts = 3) {
    let attempts = 0;
    let initialEmailCount = this.getEmailElements().length;
    
    while (attempts < maxAttempts) {
      // Scroll to bottom of the email list
      const emailListContainer = document.querySelector('.Cp[role="main"]') || 
                                document.querySelector('.nH[role="main"]') ||
                                document.querySelector('.AO');
      
      if (emailListContainer) {
        emailListContainer.scrollTop = emailListContainer.scrollHeight;
      } else {
        // Fallback: scroll window
        window.scrollTo(0, document.body.scrollHeight);
      }
      
      // Wait for potential loading
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const newEmailCount = this.getEmailElements().length;
      
      // If no new emails loaded, stop trying
      if (newEmailCount === initialEmailCount) {
        break;
      }
      
      initialEmailCount = newEmailCount;
      attempts++;
    }
    
    return this.getEmailElements().length;
  },

  /**
   * Get current Gmail view type
   * @returns {string} View type (inbox, conversation, sent, etc.)
   */
  getGmailViewType() {
    const url = window.location.href;
    
    if (url.includes('#inbox')) return 'inbox';
    if (url.includes('#sent')) return 'sent';
    if (url.includes('#drafts')) return 'drafts';
    if (url.includes('#spam')) return 'spam';
    if (url.includes('#trash')) return 'trash';
    if (url.includes('#starred')) return 'starred';
    if (url.includes('#important')) return 'important';
    if (url.includes('#all')) return 'all';
    if (url.includes('#label/')) return 'label';
    if (url.includes('#category/')) return 'category';
    if (url.includes('#search/')) return 'search';
    
    // Check for conversation view
    if (document.querySelector('.ii.gt')) return 'conversation';
    
    return 'inbox'; // default
  },

  /**
   * Debug function to analyze Gmail page structure
   * @returns {Object} Page structure info
   */
  debugGmailStructure() {
    const structure = {
      url: window.location.href,
      viewType: this.getGmailViewType(),
      mainContainer: null,
      tableElements: [],
      trElements: [],
      emailIndicators: {}
    };

    // Find main container
    const mainContainers = [
      document.querySelector('.nH[role="main"]'),
      document.querySelector('.Cp[role="main"]'),
      document.querySelector('div[role="main"]'),
      document.querySelector('.AO')
    ];
    
    structure.mainContainer = mainContainers.find(el => el !== null);

    // Find table elements
    structure.tableElements = Array.from(document.querySelectorAll('table')).map(table => ({
      tagName: table.tagName,
      className: table.className,
      id: table.id,
      rowCount: table.rows ? table.rows.length : 0
    }));

    // Find TR elements
    structure.trElements = Array.from(document.querySelectorAll('tr')).map(tr => ({
      className: tr.className,
      jsaction: tr.getAttribute('jsaction'),
      threadId: tr.getAttribute('data-thread-id'),
      legacyThreadId: tr.getAttribute('data-legacy-thread-id'),
      ariaHidden: tr.getAttribute('aria-hidden'),
      hasEmailIndicator: !!(tr.querySelector('[email]') || tr.querySelector('.yW') || tr.querySelector('.go'))
    }));

    // Check for common email indicators
    structure.emailIndicators = {
      dataThreadId: document.querySelectorAll('[data-thread-id]').length,
      dataLegacyThreadId: document.querySelectorAll('[data-legacy-thread-id]').length,
      dataMessageId: document.querySelectorAll('[data-message-id]').length,
      emailAttribute: document.querySelectorAll('[email]').length,
      yWClass: document.querySelectorAll('.yW').length,
      goClass: document.querySelectorAll('.go').length,
      zAClass: document.querySelectorAll('.zA').length,
      iiGtClass: document.querySelectorAll('.ii.gt').length,
      jsactionClick: document.querySelectorAll('[jsaction*="click"]').length,
      jsactionRcuQ6b: document.querySelectorAll('[jsaction*="rcuQ6b"]').length,
      jsactionJIbuQc: document.querySelectorAll('[jsaction*="JIbuQc"]').length,
      
      // Inbox-specific selectors
      inboxTable: document.querySelectorAll('table[role="grid"]').length,
      tbodyTrJsaction: document.querySelectorAll('tbody tr[jsaction]').length,
      tbodyTrClickNotHidden: document.querySelectorAll('tbody tr[jsaction*="click"]:not([aria-hidden="true"])').length,
      tbodyTrNotHidden: document.querySelectorAll('tbody tr:not([aria-hidden="true"])').length,
      tbodyTrAll: document.querySelectorAll('tbody tr').length
    };

    console.log('[DEBUG] Gmail Structure Analysis:', structure);
    return structure;
  }
};
