/**
 * Centralized logging system for Chrome Extension debugging
 * Provides persistent logging with storage and real-time visibility
 */

import { LOG_LEVELS, COMPONENTS, STORAGE_KEYS, PERFORMANCE_LIMITS } from './constants.js';
import { StorageUtils } from './storage-utils.js';

/**
 * Extension logger with persistent storage and console output
 */
export const ExtensionLogger = {
  // Component types and log levels are imported from constants.js
  COMPONENTS,
  LEVELS: LOG_LEVELS,

  /**
   * Main logging function
   * @param {string} level - Log level (error, warn, info, debug)
   * @param {string} component - Component name
   * @param {string} message - Log message
   * @param {any} data - Additional data to log
   */
  log: function(level, component, message, data = null) {
    const timestamp = new Date().toISOString();
    const logEntry = {
      id: Date.now() + Math.random(),
      timestamp,
      level,
      component,
      message,
      data: data ? JSON.stringify(data) : null,
      url: window.location ? window.location.href : 'unknown'
    };

    // Console output with styling
    const style = this.getConsoleStyle(level);
    this.consoleOutput(level, `%c[${component}]%c ${message}`, style, 'color: inherit');
    
    if (data) {
      this.consoleOutput(level, 'Log data:', null, data);
    }

    // Store log in persistent storage
    this.storeLog(logEntry);

    // Send log to background script for centralized collection
    this.sendLogToBackground(logEntry);

    return logEntry;
  },

  /**
   * Store log entry in Chrome storage
   * @param {Object} logEntry - Log entry to store
   */
  storeLog: async function(logEntry) {
    try {
      // Get existing logs
      const result = await StorageUtils.get(STORAGE_KEYS.DEBUG_LOGS);
      let logs = result[STORAGE_KEYS.DEBUG_LOGS] || [];

      // Add new log
      logs.unshift(logEntry);

      // Limit log size
      if (logs.length > PERFORMANCE_LIMITS.MAX_LOGS) {
        logs = logs.slice(0, PERFORMANCE_LIMITS.MAX_LOGS);
      }

      // Save updated logs
      await StorageUtils.set({ [STORAGE_KEYS.DEBUG_LOGS]: logs });
    } catch (error) {
      console.error('Failed to store log:', error);
    }
  },

  /**
   * Send log to background script for centralized collection
   * @param {Object} logEntry - Log entry to send
   */
  sendLogToBackground: function(logEntry) {
    if (chrome?.runtime?.sendMessage) {
      chrome.runtime.sendMessage({
        action: 'DEBUG_LOG',
        log: logEntry
      }).catch(error => {
        // Ignore errors - background might not be ready
        if (error && !error.message?.includes('receiving end does not exist')) {
          console.warn('Error sending log to background:', error);
        }
      });
    }
  },

  /**
   * Output to console with proper method selection
   * @param {string} level - Log level
   * @param {string} message - Formatted message
   * @param {string} style - CSS style
   * @param {any} data - Additional data
   */
  consoleOutput: function(level, message, style, data) {
    switch (level) {
      case 'error':
        if (style) {
          console.error(message, style, data || '')
        } else {
          console.error(message, data)
        }
        break
      case 'warn':
        if (style) {
          console.warn(message, style, data || '')
        } else {
          console.warn(message, data)
        }
        break
      case 'info':
        if (style) {
          console.info(message, style, data || '')
        } else {
          console.info(message, data)
        }
        break
      case 'debug':
        if (style) {
          console.log(message, style, data || '')  // Use console.log for debug
        } else {
          console.log(message, data)
        }
        break
      default:
        if (style) {
          console.log(message, style, data || '')
        } else {
          console.log(message, data)
        }
    }
  },

  /**
   * Get console style for log level
   * @param {string} level - Log level
   * @returns {string} CSS style for console
   */
  getConsoleStyle: function(level) {
    switch (level) {
      case LOG_LEVELS.ERROR:
        return 'background: #f44336; color: white; padding: 2px 4px; border-radius: 2px; font-weight: bold;';
      case LOG_LEVELS.WARN:
        return 'background: #ff9800; color: white; padding: 2px 4px; border-radius: 2px; font-weight: bold;';
      case LOG_LEVELS.INFO:
        return 'background: #2196f3; color: white; padding: 2px 4px; border-radius: 2px; font-weight: bold;';
      case LOG_LEVELS.DEBUG:
        return 'background: #9e9e9e; color: white; padding: 2px 4px; border-radius: 2px; font-weight: bold;';
      default:
        return 'background: #9e9e9e; color: white; padding: 2px 4px; border-radius: 2px; font-weight: bold;';
    }
  },

  /**
   * Log error message
   * @param {string} component - Component name
   * @param {string} message - Log message
   * @param {any} data - Additional data
   * @returns {Object} Log entry
   */
  error: function(component, message, data = null) {
    return this.log(LOG_LEVELS.ERROR, component, message, data);
  },

  /**
   * Log warning message
   * @param {string} component - Component name
   * @param {string} message - Log message
   * @param {any} data - Additional data
   * @returns {Object} Log entry
   */
  warn: function(component, message, data = null) {
    return this.log(LOG_LEVELS.WARN, component, message, data);
  },

  /**
   * Log info message
   * @param {string} component - Component name
   * @param {string} message - Log message
   * @param {any} data - Additional data
   * @returns {Object} Log entry
   */
  info: function(component, message, data = null) {
    return this.log(LOG_LEVELS.INFO, component, message, data);
  },

  /**
   * Log debug message
   * @param {string} component - Component name
   * @param {string} message - Log message
   * @param {any} data - Additional data
   * @returns {Object} Log entry
   */
  debug: function(component, message, data = null) {
    return this.log(LOG_LEVELS.DEBUG, component, message, data);
  }
};
