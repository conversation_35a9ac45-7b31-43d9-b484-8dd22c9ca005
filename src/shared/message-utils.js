/**
 * Shared message utilities for Chrome Extension inter-component communication
 * Provides consistent message passing across content scripts, popup, and background
 */

import { MESSAGE_TYPES } from './constants.js'

/**
 * Message utilities for Chrome Extension communication
 */
export const MessageUtils = {
  /**
   * Send message to background script with error handling
   * @param {string} type - Message type
   * @param {Object} data - Message data
   * @returns {Promise<Object>} Response from background
   */
  async sendToBackground(type, data = {}) {
    return new Promise((resolve, reject) => {
      if (!chrome?.runtime?.sendMessage) {
        reject(new Error('Chrome runtime not available'))
        return
      }

      const message = { type, ...data }
      
      chrome.runtime.sendMessage(message, (response) => {
        if (chrome.runtime.lastError) {
          reject(new Error(chrome.runtime.lastError.message))
          return
        }
        
        if (response && response.error) {
          reject(new Error(response.error))
          return
        }
        
        resolve(response || {})
      })
    })
  },

  /**
   * Send message to content script with error handling
   * @param {number} tabId - Tab ID
   * @param {string} type - Message type
   * @param {Object} data - Message data
   * @returns {Promise<Object>} Response from content script
   */
  async sendToContent(tabId, type, data = {}) {
    return new Promise((resolve, reject) => {
      if (!chrome?.tabs?.sendMessage) {
        reject(new Error('Chrome tabs API not available'))
        return
      }

      const message = { type, ...data }
      
      chrome.tabs.sendMessage(tabId, message, (response) => {
        if (chrome.runtime.lastError) {
          reject(new Error(chrome.runtime.lastError.message))
          return
        }
        
        if (response && response.error) {
          reject(new Error(response.error))
          return
        }
        
        resolve(response || {})
      })
    })
  },

  /**
   * Send message to popup with error handling
   * @param {string} type - Message type
   * @param {Object} data - Message data
   * @returns {Promise<Object>} Response from popup
   */
  async sendToPopup(type, data = {}) {
    return new Promise((resolve, reject) => {
      if (!chrome?.runtime?.sendMessage) {
        reject(new Error('Chrome runtime not available'))
        return
      }

      const message = { type, target: 'popup', ...data }
      
      chrome.runtime.sendMessage(message, (response) => {
        if (chrome.runtime.lastError) {
          // Popup might not be open, don't treat as error
          if (chrome.runtime.lastError.message.includes('Could not establish connection')) {
            resolve(null)
            return
          }
          reject(new Error(chrome.runtime.lastError.message))
          return
        }
        
        resolve(response || {})
      })
    })
  },

  /**
   * Set up message listener with error handling
   * @param {Function} handler - Message handler function
   * @returns {Function} Cleanup function
   */
  setupMessageListener(handler) {
    if (!chrome?.runtime?.onMessage) {
      console.error('Chrome runtime not available for message listener')
      return () => {}
    }

    const wrappedHandler = (message, sender, sendResponse) => {
      try {
        // Call the handler and handle both sync and async responses
        const result = handler(message, sender, sendResponse)
        
        // If handler returns a promise, handle it
        if (result && typeof result.then === 'function') {
          result
            .then(response => {
              if (response !== undefined) {
                sendResponse(response)
              }
            })
            .catch(error => {
              console.error('Error in message handler:', error)
              sendResponse({ error: error.message })
            })
          return true // Keep message channel open for async response
        }
        
        // If handler returned a value, send it
        if (result !== undefined) {
          sendResponse(result)
        }
      } catch (error) {
        console.error('Error in message handler:', error)
        sendResponse({ error: error.message })
      }
    }

    chrome.runtime.onMessage.addListener(wrappedHandler)
    
    // Return cleanup function
    return () => {
      chrome.runtime.onMessage.removeListener(wrappedHandler)
    }
  },

  /**
   * Get current active tab
   * @returns {Promise<Object>} Current tab
   */
  async getCurrentTab() {
    return new Promise((resolve, reject) => {
      if (!chrome?.tabs?.query) {
        reject(new Error('Chrome tabs API not available'))
        return
      }

      chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
        if (chrome.runtime.lastError) {
          reject(new Error(chrome.runtime.lastError.message))
          return
        }
        
        if (tabs && tabs.length > 0) {
          resolve(tabs[0])
        } else {
          reject(new Error('No active tab found'))
        }
      })
    })
  },

  /**
   * Check if extension context is valid
   * @returns {boolean} True if extension context is valid
   */
  isExtensionContextValid() {
    return !!(chrome && chrome.runtime && chrome.runtime.id)
  },

  /**
   * Scan current email - convenience method
   * @returns {Promise<Object>} Scan results
   */
  async scanCurrentEmail() {
    return this.sendToBackground(MESSAGE_TYPES.SCAN_EMAIL)
  },

  /**
   * Start bulk scan - convenience method
   * @returns {Promise<Object>} Bulk scan results
   */
  async startBulkScan() {
    return this.sendToBackground(MESSAGE_TYPES.BULK_SCAN)
  },

  /**
   * Handle unsubscribe action - convenience method
   * @param {Object} linkData - Unsubscribe link data
   * @returns {Promise<Object>} Unsubscribe results
   */
  async handleUnsubscribe(linkData) {
    return this.sendToBackground(MESSAGE_TYPES.UNSUBSCRIBE, linkData)
  },

  /**
   * Get usage statistics - convenience method
   * @returns {Promise<Object>} Usage statistics
   */
  async getStats() {
    return this.sendToBackground(MESSAGE_TYPES.GET_STATS)
  },

  /**
   * Increment usage count - convenience method
   * @returns {Promise<Object>} Updated usage stats
   */
  async incrementUsage() {
    return this.sendToBackground(MESSAGE_TYPES.INCREMENT_USAGE)
  },

  /**
   * Upgrade subscription - convenience method
   * @param {string} tier - Subscription tier
   * @returns {Promise<Object>} Upgrade results
   */
  async upgradeSubscription(tier) {
    return this.sendToBackground(MESSAGE_TYPES.UPGRADE_SUBSCRIPTION, { tier })
  },

  /**
   * Report false positive - convenience method
   * @param {Object} reportData - False positive report data
   * @returns {Promise<Object>} Report results
   */
  async reportFalsePositive(reportData) {
    return this.sendToBackground(MESSAGE_TYPES.REPORT_FALSE_POSITIVE, reportData)
  },

  /**
   * Set manual override - convenience method
   * @param {Object} overrideData - Manual override data
   * @returns {Promise<Object>} Override results
   */
  async setManualOverride(overrideData) {
    return this.sendToBackground(MESSAGE_TYPES.SET_MANUAL_OVERRIDE, overrideData)
  },

  /**
   * Send debug log - convenience method
   * @param {Object} logEntry - Debug log entry
   * @returns {Promise<Object>} Log results
   */
  async sendDebugLog(logEntry) {
    return this.sendToBackground(MESSAGE_TYPES.DEBUG_LOG, { logEntry })
  },

  /**
   * Create standardized error response
   * @param {string} message - Error message
   * @param {Object} details - Error details
   * @returns {Object} Standardized error response
   */
  createErrorResponse(message, details = {}) {
    return {
      success: false,
      error: message,
      details,
      timestamp: Date.now()
    }
  },

  /**
   * Create standardized success response
   * @param {Object} data - Success data
   * @returns {Object} Standardized success response
   */
  createSuccessResponse(data = {}) {
    return {
      success: true,
      data,
      timestamp: Date.now()
    }
  }
}

export default MessageUtils