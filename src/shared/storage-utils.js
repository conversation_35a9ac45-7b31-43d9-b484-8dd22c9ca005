/**
 * Shared storage utilities for Chrome Extension
 * Provides consistent storage operations across all extension components
 */

import { STORAGE_KEYS } from './constants.js'

/**
 * Chrome storage wrapper with error handling and promise support
 */
export const StorageUtils = {
  /**
   * Get data from Chrome storage with error handling
   * @param {string|string[]} keys - Storage keys to retrieve
   * @returns {Promise<Object>} Retrieved data
   */
  async get(keys) {
    return new Promise((resolve, reject) => {
      if (!chrome?.storage?.local) {
        reject(new Error('Chrome storage not available'))
        return
      }

      chrome.storage.local.get(keys, (result) => {
        if (chrome.runtime.lastError) {
          reject(new Error(chrome.runtime.lastError.message))
          return
        }
        resolve(result)
      })
    })
  },

  /**
   * Set data in Chrome storage with error handling
   * @param {Object} data - Data to store
   * @returns {Promise<void>}
   */
  async set(data) {
    return new Promise((resolve, reject) => {
      if (!chrome?.storage?.local) {
        reject(new Error('Chrome storage not available'))
        return
      }

      chrome.storage.local.set(data, () => {
        if (chrome.runtime.lastError) {
          reject(new Error(chrome.runtime.lastError.message))
          return
        }
        resolve()
      })
    })
  },

  /**
   * Remove data from Chrome storage
   * @param {string|string[]} keys - Keys to remove
   * @returns {Promise<void>}
   */
  async remove(keys) {
    return new Promise((resolve, reject) => {
      if (!chrome?.storage?.local) {
        reject(new Error('Chrome storage not available'))
        return
      }

      chrome.storage.local.remove(keys, () => {
        if (chrome.runtime.lastError) {
          reject(new Error(chrome.runtime.lastError.message))
          return
        }
        resolve()
      })
    })
  },

  /**
   * Clear all Chrome storage data
   * @returns {Promise<void>}
   */
  async clear() {
    return new Promise((resolve, reject) => {
      if (!chrome?.storage?.local) {
        reject(new Error('Chrome storage not available'))
        return
      }

      chrome.storage.local.clear(() => {
        if (chrome.runtime.lastError) {
          reject(new Error(chrome.runtime.lastError.message))
          return
        }
        resolve()
      })
    })
  },

  /**
   * Get usage statistics from storage
   * @returns {Promise<Object>} Usage statistics
   */
  async getUsageStats() {
    try {
      const result = await this.get(STORAGE_KEYS.USAGE_STATS)
      return result[STORAGE_KEYS.USAGE_STATS] || {
        monthly_count: 0,
        last_reset: Date.now(),
        subscription: { tier: 'free' }
      }
    } catch (error) {
      console.error('Error getting usage stats:', error)
      return {
        monthly_count: 0,
        last_reset: Date.now(),
        subscription: { tier: 'free' }
      }
    }
  },

  /**
   * Save usage statistics to storage
   * @param {Object} stats - Usage statistics to save
   * @returns {Promise<void>}
   */
  async saveUsageStats(stats) {
    try {
      await this.set({
        [STORAGE_KEYS.USAGE_STATS]: stats
      })
    } catch (error) {
      console.error('Error saving usage stats:', error)
      throw error
    }
  },

  /**
   * Get subscription database from storage
   * @returns {Promise<Object>} Subscription database
   */
  async getSubscriptionDatabase() {
    try {
      const result = await this.get(STORAGE_KEYS.SUBSCRIPTIONS)
      return result[STORAGE_KEYS.SUBSCRIPTIONS] || {}
    } catch (error) {
      console.error('Error getting subscription database:', error)
      return {}
    }
  },

  /**
   * Save subscription database to storage
   * @param {Object} database - Subscription database to save
   * @returns {Promise<void>}
   */
  async saveSubscriptionDatabase(database) {
    try {
      await this.set({
        [STORAGE_KEYS.SUBSCRIPTIONS]: database
      })
    } catch (error) {
      console.error('Error saving subscription database:', error)
      throw error
    }
  },

  /**
   * Get user preferences from storage
   * @returns {Promise<Object>} User preferences
   */
  async getUserPreferences() {
    try {
      const result = await this.get(STORAGE_KEYS.USER_PREFERENCES)
      return result[STORAGE_KEYS.USER_PREFERENCES] || {
        enable_security_warnings: true,
        auto_scan_enabled: true,
        debug_mode: false
      }
    } catch (error) {
      console.error('Error getting user preferences:', error)
      return {
        enable_security_warnings: true,
        auto_scan_enabled: true,
        debug_mode: false
      }
    }
  },

  /**
   * Save user preferences to storage
   * @param {Object} preferences - User preferences to save
   * @returns {Promise<void>}
   */
  async saveUserPreferences(preferences) {
    try {
      await this.set({
        [STORAGE_KEYS.USER_PREFERENCES]: preferences
      })
    } catch (error) {
      console.error('Error saving user preferences:', error)
      throw error
    }
  },

  /**
   * Get debug logs from storage
   * @returns {Promise<Array>} Debug logs
   */
  async getDebugLogs() {
    try {
      const result = await this.get(STORAGE_KEYS.DEBUG_LOGS)
      return result[STORAGE_KEYS.DEBUG_LOGS] || []
    } catch (error) {
      console.error('Error getting debug logs:', error)
      return []
    }
  },

  /**
   * Save debug logs to storage
   * @param {Array} logs - Debug logs to save
   * @returns {Promise<void>}
   */
  async saveDebugLogs(logs) {
    try {
      await this.set({
        [STORAGE_KEYS.DEBUG_LOGS]: logs
      })
    } catch (error) {
      console.error('Error saving debug logs:', error)
      throw error
    }
  },

  /**
   * Get local blacklist from storage
   * @returns {Promise<Array>} Local blacklist
   */
  async getLocalBlacklist() {
    try {
      const result = await this.get(STORAGE_KEYS.LOCAL_BLACKLIST)
      return result[STORAGE_KEYS.LOCAL_BLACKLIST] || []
    } catch (error) {
      console.error('Error getting local blacklist:', error)
      return []
    }
  },

  /**
   * Save local blacklist to storage
   * @param {Array} blacklist - Local blacklist to save
   * @returns {Promise<void>}
   */
  async saveLocalBlacklist(blacklist) {
    try {
      await this.set({
        [STORAGE_KEYS.LOCAL_BLACKLIST]: blacklist
      })
    } catch (error) {
      console.error('Error saving local blacklist:', error)
      throw error
    }
  },

  /**
   * Initialize default storage values
   * @returns {Promise<void>}
   */
  async initializeDefaults() {
    try {
      const currentData = await this.get(Object.values(STORAGE_KEYS))
      
      const defaults = {
        [STORAGE_KEYS.USAGE_STATS]: {
          monthly_count: 0,
          last_reset: Date.now(),
          subscription: { tier: 'free' }
        },
        [STORAGE_KEYS.USER_PREFERENCES]: {
          enable_security_warnings: true,
          auto_scan_enabled: true,
          debug_mode: false
        },
        [STORAGE_KEYS.SUBSCRIPTIONS]: {},
        [STORAGE_KEYS.DEBUG_LOGS]: [],
        [STORAGE_KEYS.LOCAL_BLACKLIST]: [],
        [STORAGE_KEYS.EXTRACTION_CACHE]: {},
        [STORAGE_KEYS.SECURITY_OVERRIDES]: {}
      }

      // Only set defaults for missing keys
      const dataToSet = {}
      for (const [key, defaultValue] of Object.entries(defaults)) {
        if (!(key in currentData)) {
          dataToSet[key] = defaultValue
        }
      }

      if (Object.keys(dataToSet).length > 0) {
        await this.set(dataToSet)
      }
    } catch (error) {
      console.error('Error initializing default storage:', error)
      throw error
    }
  }
}

export default StorageUtils