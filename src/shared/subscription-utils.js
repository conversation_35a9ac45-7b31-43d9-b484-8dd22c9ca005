/**
 * Shared subscription utilities for Chrome Extension
 * Handles subscription tier management and usage tracking
 */

import { SUBSCRIPTION_TIERS, STORAGE_KEYS } from './constants.js';
import { StorageUtils } from './storage-utils.js';
import { ExtensionLogger } from './logger.js';

/**
 * Subscription utility functions for Chrome Extension
 */
export const SubscriptionUtils = {
  /**
   * Get current subscription data
   * @returns {Promise<Object>} Subscription data
   */
  async getCurrentSubscription() {
    try {
      const data = await StorageUtils.get([
        STORAGE_KEYS.USAGE_STATS,
        'subscription',
        'monthlyCount',
        'lastResetDate'
      ]);
      
      const subscription = data.subscription || { tier: 'free' };
      const monthlyCount = data.monthlyCount || 0;
      const lastResetDate = data.lastResetDate || Date.now();
      
      // Get tier details
      const tierDetails = SUBSCRIPTION_TIERS[subscription.tier] || SUBSCRIPTION_TIERS.free;
      
      // Calculate remaining usage
      const monthlyLimit = tierDetails.monthlyLimit;
      const remainingCount = monthlyLimit < 0 ? -1 : Math.max(0, monthlyLimit - monthlyCount);
      const limitReached = monthlyLimit >= 0 && monthlyCount >= monthlyLimit;
      
      // Calculate days until reset
      const now = new Date();
      const resetDate = new Date(lastResetDate);
      resetDate.setMonth(resetDate.getMonth() + 1);
      const daysUntilReset = Math.ceil((resetDate - now) / (1000 * 60 * 60 * 24));
      
      return {
        tier: subscription.tier,
        tierName: tierDetails.name,
        monthlyLimit,
        price: tierDetails.price,
        features: tierDetails.features,
        currentCount: monthlyCount,
        remainingCount,
        limitReached,
        daysUntilReset,
        lastResetDate,
        validUntil: subscription.validUntil,
        purchaseDate: subscription.purchaseDate,
        autoRenew: subscription.autoRenew || false
      };
    } catch (error) {
      ExtensionLogger.error('SubscriptionUtils', 'Failed to get subscription', error);
      
      // Return default free tier
      const tierDetails = SUBSCRIPTION_TIERS.free;
      return {
        tier: 'free',
        tierName: tierDetails.name,
        monthlyLimit: tierDetails.monthlyLimit,
        price: tierDetails.price,
        features: tierDetails.features,
        currentCount: 0,
        remainingCount: tierDetails.monthlyLimit,
        limitReached: false,
        daysUntilReset: 30,
        lastResetDate: Date.now(),
        validUntil: null,
        purchaseDate: null,
        autoRenew: false
      };
    }
  },
  
  /**
   * Increment usage count
   * @returns {Promise<Object>} Updated subscription data
   */
  async incrementUsage() {
    try {
      // Get current data
      const data = await StorageUtils.get([
        'monthlyCount',
        'totalUnsubscribes',
        'subscription'
      ]);
      
      // Update counts
      const monthlyCount = (data.monthlyCount || 0) + 1;
      const totalUnsubscribes = (data.totalUnsubscribes || 0) + 1;
      
      // Get subscription tier
      const subscription = data.subscription || { tier: 'free' };
      const tierDetails = SUBSCRIPTION_TIERS[subscription.tier] || SUBSCRIPTION_TIERS.free;
      
      // Check if limit reached
      const monthlyLimit = tierDetails.monthlyLimit;
      const limitReached = monthlyLimit >= 0 && monthlyCount >= monthlyLimit;
      
      // Save updated counts
      await StorageUtils.set({
        monthlyCount,
        totalUnsubscribes
      });
      
      return {
        success: true,
        monthlyCount,
        totalUnsubscribes,
        limitReached,
        tier: subscription.tier
      };
    } catch (error) {
      ExtensionLogger.error('SubscriptionUtils', 'Failed to increment usage', error);
      return {
        success: false,
        error: error.message
      };
    }
  },
  
  /**
   * Check if monthly usage should be reset
   * @returns {Promise<boolean>} True if reset was performed
   */
  async checkMonthlyReset() {
    try {
      const data = await StorageUtils.get(['lastResetDate']);
      const lastResetDate = data.lastResetDate ? new Date(data.lastResetDate) : null;
      
      if (!lastResetDate) {
        // Initialize reset date
        await StorageUtils.set({ lastResetDate: Date.now() });
        return false;
      }
      
      const now = new Date();
      const monthDiff = (now.getFullYear() - lastResetDate.getFullYear()) * 12 + 
                        now.getMonth() - lastResetDate.getMonth();
      
      if (monthDiff >= 1) {
        // Reset monthly count
        await StorageUtils.set({
          monthlyCount: 0,
          lastResetDate: Date.now()
        });
        
        ExtensionLogger.info('SubscriptionUtils', 'Monthly usage reset');
        return true;
      }
      
      return false;
    } catch (error) {
      ExtensionLogger.error('SubscriptionUtils', 'Failed to check monthly reset', error);
      return false;
    }
  },
  
  /**
   * Update subscription tier
   * @param {string} tier - Tier to upgrade to
   * @param {Object} details - Subscription details
   * @returns {Promise<Object>} Updated subscription data
   */
  async updateSubscription(tier, details = {}) {
    try {
      if (!SUBSCRIPTION_TIERS[tier]) {
        throw new Error(`Invalid subscription tier: ${tier}`);
      }
      
      const subscription = {
        tier,
        validUntil: details.validUntil || null,
        purchaseDate: details.purchaseDate || Date.now(),
        autoRenew: details.autoRenew || false
      };
      
      await StorageUtils.set({ subscription });
      
      return {
        success: true,
        subscription
      };
    } catch (error) {
      ExtensionLogger.error('SubscriptionUtils', 'Failed to update subscription', error);
      return {
        success: false,
        error: error.message
      };
    }
  }
};
