/**
 * Shared UI utilities for Chrome Extension
 * Provides common UI manipulation functions across content scripts and popup
 */

import { UI_CONSTANTS } from './constants.js';
import { DOMUtils } from './dom-utils.js';
import { ExtensionLogger } from './logger.js';

/**
 * UI utility functions for Chrome Extension
 */
export const UIUtils = {
  /**
   * Show a status message with optional auto-hide
   * @param {HTMLElement} statusElement - Status element to update
   * @param {string} message - Message to display
   * @param {string} type - Status type (success, error, warning, loading)
   * @param {number} autoHideMs - Auto-hide timeout in ms (0 to disable)
   */
  showStatus(statusElement, message, type = 'info', autoHideMs = 5000) {
    if (!statusElement) return;
    
    // Clear any existing timeouts
    if (statusElement._hideTimeout) {
      clearTimeout(statusElement._hideTimeout);
      statusElement._hideTimeout = null;
    }
    
    // Update status
    statusElement.textContent = message;
    statusElement.className = `status-item ${type}`;
    
    // Auto-hide if specified and not an error
    if (autoHideMs > 0 && type !== 'error') {
      statusElement._hideTimeout = setTimeout(() => {
        statusElement.textContent = '';
        statusElement.className = 'status-item';
      }, autoHideMs);
    }
  },
  
  /**
   * Create a modal dialog
   * @param {Object} options - Modal options
   * @param {string} options.title - Modal title
   * @param {string} options.content - Modal content HTML
   * @param {Object} options.buttons - Modal buttons {id: {text, type, onClick}}
   * @param {boolean} options.closeOnOverlayClick - Close on overlay click
   * @returns {HTMLElement} Modal element
   */
  createModal(options) {
    const { title, content, buttons = {}, closeOnOverlayClick = true } = options;
    
    // Create modal container
    const modal = DOMUtils.createElement('div', { className: 'unsubscriber-modal' });
    
    // Create modal content
    const modalContent = DOMUtils.createElement('div', { className: 'unsubscriber-modal-content' });
    
    // Create header
    const header = DOMUtils.createElement('div', { className: 'unsubscriber-modal-header' });
    header.appendChild(DOMUtils.createElement('h3', {}, title));
    
    const closeBtn = DOMUtils.createElement('button', { className: 'unsubscriber-close' }, '×');
    closeBtn.addEventListener('click', () => {
      document.body.removeChild(modal);
    });
    
    header.appendChild(closeBtn);
    modalContent.appendChild(header);
    
    // Create body
    const body = DOMUtils.createElement('div', { 
      className: 'unsubscriber-modal-body',
      innerHTML: content
    });
    modalContent.appendChild(body);
    
    // Create footer with buttons if any
    if (Object.keys(buttons).length > 0) {
      const footer = DOMUtils.createElement('div', { className: 'unsubscriber-modal-footer' });
      
      for (const [id, buttonConfig] of Object.entries(buttons)) {
        const { text, type = 'default', onClick } = buttonConfig;
        const button = DOMUtils.createElement('button', {
          id,
          className: `unsubscriber-btn ${type}`
        }, text);
        
        button.addEventListener('click', (e) => {
          if (onClick) {
            onClick(e);
          }
        });
        
        footer.appendChild(button);
      }
      
      modalContent.appendChild(footer);
    }
    
    modal.appendChild(modalContent);
    
    // Handle overlay click
    if (closeOnOverlayClick) {
      modal.addEventListener('click', (e) => {
        if (e.target === modal) {
          document.body.removeChild(modal);
        }
      });
    }
    
    // Add to document
    document.body.appendChild(modal);
    
    return modal;
  },
  
  /**
   * Show a confirmation dialog
   * @param {string} title - Dialog title
   * @param {string} message - Dialog message
   * @param {Function} onConfirm - Confirm callback
   * @param {Function} onCancel - Cancel callback
   * @returns {HTMLElement} Modal element
   */
  showConfirmation(title, message, onConfirm, onCancel) {
    return this.createModal({
      title,
      content: `<p>${message}</p>`,
      buttons: {
        cancel: {
          text: 'Cancel',
          type: 'default',
          onClick: () => {
            if (onCancel) onCancel();
            document.body.removeChild(modal);
          }
        },
        confirm: {
          text: 'Confirm',
          type: 'primary',
          onClick: () => {
            if (onConfirm) onConfirm();
            document.body.removeChild(modal);
          }
        }
      }
    });
  },
  
  /**
   * Format a number with commas
   * @param {number} num - Number to format
   * @returns {string} Formatted number
   */
  formatNumber(num) {
    return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  },
  
  /**
   * Format a date relative to now
   * @param {Date|number|string} date - Date to format
   * @returns {string} Formatted date
   */
  formatRelativeDate(date) {
    const d = new Date(date);
    const now = new Date();
    const diffMs = now - d;
    const diffSec = Math.floor(diffMs / 1000);
    const diffMin = Math.floor(diffSec / 60);
    const diffHour = Math.floor(diffMin / 60);
    const diffDay = Math.floor(diffHour / 24);
    
    if (diffDay > 30) {
      return d.toLocaleDateString();
    } else if (diffDay > 0) {
      return `${diffDay} day${diffDay > 1 ? 's' : ''} ago`;
    } else if (diffHour > 0) {
      return `${diffHour} hour${diffHour > 1 ? 's' : ''} ago`;
    } else if (diffMin > 0) {
      return `${diffMin} minute${diffMin > 1 ? 's' : ''} ago`;
    } else {
      return 'Just now';
    }
  }
};
