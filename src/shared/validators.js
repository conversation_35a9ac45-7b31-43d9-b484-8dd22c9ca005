/**
 * Shared validation utilities for Chrome Extension
 * Provides URL validation, security checks, and input validation
 */

import { SECURITY_PATTERNS, UNSUBSCRIBE_PATTERNS, SECURITY_STATUS } from './constants.js'

/**
 * Validation utilities for Chrome Extension
 */
export const Validators = {
  /**
   * Validate URL format and security
   * @param {string} url - URL to validate
   * @returns {Object} Validation result with isValid and securityStatus
   */
  validateUrl(url) {
    if (!url || typeof url !== 'string') {
      return { isValid: false, securityStatus: SECURITY_STATUS.DANGEROUS, reason: 'Invalid URL format' }
    }

    try {
      const urlObj = new URL(url)
      
      // Check protocol
      if (!['http:', 'https:'].includes(urlObj.protocol)) {
        return { isValid: false, securityStatus: SECURITY_STATUS.DANGEROUS, reason: 'Invalid protocol' }
      }

      // Check for suspicious patterns
      const domain = urlObj.hostname.toLowerCase()
      
      // Check against suspicious domains
      if (SECURITY_PATTERNS.SUSPICIOUS_INDICATORS.some(pattern => domain.includes(pattern))) {
        return { isValid: true, securityStatus: SECURITY_STATUS.DANGEROUS, reason: 'Suspicious domain' }
      }

      // Check against legitimate domains
      if (SECURITY_PATTERNS.LEGITIMATE_DOMAINS.some(pattern => domain.includes(pattern))) {
        return { isValid: true, securityStatus: SECURITY_STATUS.SAFE, reason: 'Legitimate domain' }
      }

      // Check for URL shorteners
      const shorteners = ['bit.ly', 'tinyurl.com', 't.co', 'short.link', 'goo.gl']
      if (shorteners.some(shortener => domain.includes(shortener))) {
        return { isValid: true, securityStatus: SECURITY_STATUS.CAUTION, reason: 'URL shortener' }
      }

      // Check for suspicious parameters
      const params = urlObj.searchParams
      const suspiciousParams = ['token', 'id', 'verify', 'confirm', 'click']
      const hasSuspiciousParams = suspiciousParams.some(param => params.has(param))
      
      if (hasSuspiciousParams) {
        return { isValid: true, securityStatus: SECURITY_STATUS.CAUTION, reason: 'Suspicious parameters' }
      }

      // Default to unknown for new domains
      return { isValid: true, securityStatus: SECURITY_STATUS.UNKNOWN, reason: 'Unknown domain' }
      
    } catch (error) {
      return { isValid: false, securityStatus: SECURITY_STATUS.DANGEROUS, reason: 'Malformed URL' }
    }
  },

  /**
   * Check if URL is likely an unsubscribe link
   * @param {string} url - URL to check
   * @param {string} linkText - Link text content
   * @returns {Object} Analysis result
   */
  analyzeUnsubscribeLink(url, linkText = '') {
    const urlValidation = this.validateUrl(url)
    if (!urlValidation.isValid) {
      return { isUnsubscribe: false, confidence: 0, ...urlValidation }
    }

    let confidence = 0
    const reasons = []

    try {
      const urlObj = new URL(url)
      const fullUrl = url.toLowerCase()
      const text = linkText.toLowerCase()

      // Check URL path and query parameters
      const pathAndQuery = (urlObj.pathname + urlObj.search).toLowerCase()
      
      // Positive indicators in URL
      if (UNSUBSCRIBE_PATTERNS.COMMON_TEXTS.some(pattern => pathAndQuery.includes(pattern))) {
        confidence += 40
        reasons.push('Unsubscribe pattern in URL')
      }

      // Positive indicators in link text
      if (UNSUBSCRIBE_PATTERNS.COMMON_TEXTS.some(pattern => text.includes(pattern))) {
        confidence += 30
        reasons.push('Unsubscribe pattern in text')
      }

      // Negative indicators (suspicious patterns)
      if (UNSUBSCRIBE_PATTERNS.SUSPICIOUS_PATTERNS.some(pattern => text.includes(pattern))) {
        confidence -= 20
        reasons.push('Suspicious pattern in text')
      }

      // Domain reputation bonus
      if (SECURITY_PATTERNS.LEGITIMATE_DOMAINS.some(domain => urlObj.hostname.includes(domain))) {
        confidence += 20
        reasons.push('Legitimate email service domain')
      }

      // HTTPS bonus
      if (urlObj.protocol === 'https:') {
        confidence += 10
        reasons.push('Secure HTTPS connection')
      }

      // Normalize confidence to 0-100 range
      confidence = Math.max(0, Math.min(100, confidence))

      return {
        isUnsubscribe: confidence >= 50,
        confidence,
        reasons,
        securityStatus: urlValidation.securityStatus,
        isValid: true
      }
      
    } catch (error) {
      return {
        isUnsubscribe: false,
        confidence: 0,
        reasons: ['URL analysis failed'],
        securityStatus: SECURITY_STATUS.DANGEROUS,
        isValid: false
      }
    }
  },

  /**
   * Validate email address format
   * @param {string} email - Email address to validate
   * @returns {Object} Validation result
   */
  validateEmail(email) {
    if (!email || typeof email !== 'string') {
      return { isValid: false, reason: 'Invalid email format' }
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    const isValid = emailRegex.test(email)

    if (!isValid) {
      return { isValid: false, reason: 'Invalid email format' }
    }

    // Extract domain for additional checks
    const domain = email.split('@')[1].toLowerCase()
    
    // Check for suspicious domains
    if (SECURITY_PATTERNS.SUSPICIOUS_INDICATORS.some(pattern => domain.includes(pattern))) {
      return { isValid: true, securityStatus: SECURITY_STATUS.CAUTION, reason: 'Suspicious domain' }
    }

    return { isValid: true, securityStatus: SECURITY_STATUS.SAFE, domain }
  },

  /**
   * Validate sender information
   * @param {Object} senderInfo - Sender information object
   * @returns {Object} Validation result
   */
  validateSenderInfo(senderInfo) {
    if (!senderInfo || typeof senderInfo !== 'object') {
      return { isValid: false, reason: 'Invalid sender info' }
    }

    const { email, name, domain } = senderInfo
    
    // Validate email
    if (!email) {
      return { isValid: false, reason: 'Missing email address' }
    }

    const emailValidation = this.validateEmail(email)
    if (!emailValidation.isValid) {
      return emailValidation
    }

    // Validate domain consistency
    if (domain && email) {
      const emailDomain = email.split('@')[1]
      if (emailDomain !== domain) {
        return { isValid: false, reason: 'Domain mismatch' }
      }
    }

    // Check for suspicious patterns in name
    if (name) {
      const suspiciousNamePatterns = ['noreply', 'no-reply', 'donotreply', 'automated']
      const hasAutomatedName = suspiciousNamePatterns.some(pattern => 
        name.toLowerCase().includes(pattern)
      )
      
      if (hasAutomatedName) {
        return { isValid: true, securityStatus: SECURITY_STATUS.SAFE, reason: 'Automated sender' }
      }
    }

    return { isValid: true, securityStatus: SECURITY_STATUS.SAFE }
  },

  /**
   * Validate subscription tier
   * @param {string} tier - Subscription tier
   * @returns {Object} Validation result
   */
  validateSubscriptionTier(tier) {
    const validTiers = ['free', 'premium', 'unlimited']
    
    if (!tier || !validTiers.includes(tier)) {
      return { isValid: false, reason: 'Invalid subscription tier' }
    }

    return { isValid: true, tier }
  },

  /**
   * Validate usage count against subscription limits
   * @param {number} count - Current usage count
   * @param {string} tier - Subscription tier
   * @returns {Object} Validation result
   */
  validateUsageLimit(count, tier) {
    const tierValidation = this.validateSubscriptionTier(tier)
    if (!tierValidation.isValid) {
      return tierValidation
    }

    if (typeof count !== 'number' || count < 0) {
      return { isValid: false, reason: 'Invalid usage count' }
    }

    // Import subscription tiers for limit checking
    const limits = {
      free: 20,
      premium: 500,
      unlimited: -1 // -1 means unlimited
    }

    const limit = limits[tier]
    
    if (limit === -1) {
      return { isValid: true, withinLimit: true, remaining: -1 }
    }

    const withinLimit = count <= limit
    const remaining = Math.max(0, limit - count)

    return {
      isValid: true,
      withinLimit,
      remaining,
      limit,
      usage: count
    }
  },

  /**
   * Validate message type for inter-component communication
   * @param {string} type - Message type
   * @returns {Object} Validation result
   */
  validateMessageType(type) {
    const validTypes = [
      'scan_email',
      'bulk_scan',
      'unsubscribe',
      'get_stats',
      'increment_usage',
      'upgrade_subscription',
      'report_false_positive',
      'set_manual_override',
      'DEBUG_LOG'
    ]

    if (!type || !validTypes.includes(type)) {
      return { isValid: false, reason: 'Invalid message type' }
    }

    return { isValid: true, type }
  },

  /**
   * Sanitize input string for security
   * @param {string} input - Input string to sanitize
   * @returns {string} Sanitized string
   */
  sanitizeInput(input) {
    if (!input || typeof input !== 'string') {
      return ''
    }

    // Remove HTML tags and dangerous characters
    return input
      .replace(/<[^>]*>/g, '') // Remove HTML tags
      .replace(/[<>'"&]/g, '') // Remove dangerous characters
      .trim()
  },

  /**
   * Validate Chrome extension context
   * @returns {Object} Validation result
   */
  validateExtensionContext() {
    const hasChrome = typeof chrome !== 'undefined'
    const hasRuntime = hasChrome && chrome.runtime && chrome.runtime.id
    const hasStorage = hasChrome && chrome.storage && chrome.storage.local

    return {
      isValid: hasChrome && hasRuntime && hasStorage,
      hasChrome,
      hasRuntime,
      hasStorage
    }
  },

  /**
   * Validate DOM element
   * @param {Element} element - DOM element to validate
   * @returns {Object} Validation result
   */
  validateDOMElement(element) {
    if (!element || !(element instanceof Element)) {
      return { isValid: false, reason: 'Invalid DOM element' }
    }

    const isConnected = element.isConnected !== false
    const hasParent = element.parentNode !== null

    return {
      isValid: true,
      isConnected,
      hasParent,
      tagName: element.tagName
    }
  }
}

export default Validators