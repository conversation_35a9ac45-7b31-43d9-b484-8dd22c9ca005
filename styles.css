/* Gmail integration styles for Email Unsubscriber */

/* Button styles for unsubscribe buttons in Gmail */
.unsubscriber-button {
    margin: 8px 0;
    padding: 4px 0;
    border-top: 1px solid #e8eaed;
    border-bottom: 1px solid #e8eaed;
    background: #f8f9fa;
}

.unsubscriber-btn {
    display: flex;
    align-items: center;
    gap: 6px;
    background: #ffffff;
    border: 1px solid #dadce0;
    border-radius: 4px;
    padding: 8px 12px;
    font-size: 12px;
    color: #5f6368;
    cursor: pointer;
    transition: all 0.2s ease;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Arial, sans-serif;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.unsubscriber-btn:hover {
    background: #f1f3f4;
    border-color: #bdc1c6;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
}

.unsubscriber-btn:active {
    background: #e8eaed;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.unsubscriber-icon {
    font-size: 14px;
}

.unsubscriber-text {
    font-weight: 500;
}

/* Modal styles */
.unsubscriber-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10000;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Arial, sans-serif;
}

.unsubscriber-modal-content {
    background: white;
    border-radius: 8px;
    padding: 0;
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.unsubscriber-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    border-bottom: 1px solid #e8eaed;
    background: #f8f9fa;
}

.unsubscriber-modal-header h3 {
    margin: 0;
    color: #333;
    font-size: 18px;
    font-weight: 500;
}

.unsubscriber-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #5f6368;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background-color 0.2s ease;
}

.unsubscriber-close:hover {
    background: #e8eaed;
}

.unsubscriber-modal-body {
    padding: 20px;
}

.unsubscriber-modal-body p {
    margin: 0 0 16px 0;
    color: #5f6368;
    font-size: 14px;
}

.unsubscriber-links {
    margin: 16px 0;
}

.unsubscriber-link-item {
    margin-bottom: 8px;
}

.unsubscriber-link-btn {
    width: 100%;
    padding: 12px;
    background: #f8f9fa;
    border: 1px solid #dadce0;
    border-radius: 4px;
    cursor: pointer;
    text-align: left;
    font-size: 14px;
    transition: all 0.2s ease;
    font-family: inherit;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.unsubscriber-link-btn:hover {
    background: #e8eaed;
    border-color: #bdc1c6;
}

.unsubscriber-link-btn:active {
    background: #dadce0;
}

.unsubscriber-link-btn .warning {
    color: #ea4335;
    font-size: 16px;
}

.unsubscriber-warning {
    margin-top: 16px;
    padding: 12px;
    background: #e8f0fe;
    border-radius: 4px;
    font-size: 12px;
    color: #5f6368;
    border-left: 4px solid #4285f4;
}

.unsubscriber-warning strong {
    color: #1a73e8;
}

/* Security warning styles */
.unsubscriber-security-warning {
    background: #fef7e0;
    border-left: 4px solid #fbbc04;
    padding: 12px;
    margin: 8px 0;
    border-radius: 4px;
    font-size: 12px;
    color: #e8710a;
}

.unsubscriber-danger-warning {
    background: #fce8e6;
    border-left: 4px solid #ea4335;
    padding: 12px;
    margin: 8px 0;
    border-radius: 4px;
    font-size: 12px;
    color: #d93025;
}

/* Animation for button appearance */
@keyframes unsubscriberFadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.unsubscriber-button {
    animation: unsubscriberFadeIn 0.3s ease-out;
}

/* Responsive styles */
@media (max-width: 600px) {
    .unsubscriber-modal-content {
        width: 95%;
        max-width: none;
    }
    
    .unsubscriber-modal-body {
        padding: 16px;
    }
    
    .unsubscriber-modal-header {
        padding: 12px 16px;
    }
    
    .unsubscriber-btn {
        padding: 10px;
        font-size: 11px;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .unsubscriber-btn {
        border: 2px solid #000;
    }
    
    .unsubscriber-modal-content {
        border: 2px solid #000;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .unsubscriber-btn,
    .unsubscriber-link-btn,
    .unsubscriber-close {
        transition: none;
    }
    
    .unsubscriber-button {
        animation: none;
    }
}