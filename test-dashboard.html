<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin-bottom: 20px; padding: 20px; border: 1px solid #ddd; border-radius: 8px; }
        .test-button { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        .test-button:hover { background: #0056b3; }
        .test-output { background: #f8f9fa; padding: 15px; border-radius: 4px; margin-top: 10px; font-family: monospace; white-space: pre-wrap; }
        .error { background: #f8d7da; color: #721c24; }
        .success { background: #d4edda; color: #155724; }
    </style>
</head>
<body>
    <h1>Dashboard Function Test</h1>
    
    <div class="test-section">
        <h2>Test 1: Button Event Handling</h2>
        <button class="test-button" data-action="unsubscribe" data-sender-id="test-sender">Test Unsubscribe</button>
        <button class="test-button" data-action="details" data-sender-id="test-sender">Test Details</button>
        <div id="test1-output" class="test-output"></div>
    </div>
    
    <div class="test-section">
        <h2>Test 2: Sample Data Generation</h2>
        <button class="test-button" onclick="testSampleData()">Test Sample Data</button>
        <div id="test2-output" class="test-output"></div>
    </div>
    
    <div class="test-section">
        <h2>Test 3: Chrome Storage Mock</h2>
        <button class="test-button" onclick="testChromeStorage()">Test Chrome Storage</button>
        <div id="test3-output" class="test-output"></div>
    </div>
    
    <script>
        // Mock Chrome APIs for testing
        if (typeof chrome === 'undefined') {
            window.chrome = {
                storage: {
                    local: {
                        get: function(keys) {
                            return Promise.resolve({});
                        },
                        set: function(data) {
                            return Promise.resolve();
                        }
                    }
                }
            };
        }
        
        // Include the dashboard class
        class TestDashboard {
            constructor() {
                this.isDebugMode = true;
                this.subscriptions = [];
                this.selectedSubscriptions = new Set();
                this.setupEventListeners();
            }
            
            log(message, data = null) {
                console.log(`[Dashboard] ${message}`, data || '');
            }
            
            logError(message, error = null) {
                console.error(`[Dashboard] ${message}`, error || '');
            }
            
            setupEventListeners() {
                document.addEventListener('click', (event) => {
                    if (event.target.closest('[data-action]')) {
                        this.handleSubscriptionClick(event);
                    }
                });
            }
            
            handleSubscriptionClick(event) {
                const button = event.target.closest('[data-action]')
                if (!button) return

                const action = button.dataset.action
                const senderId = button.dataset.senderId
                
                const output = document.getElementById('test1-output');
                
                if (!senderId) {
                    this.logError('No senderId found for action:', action)
                    output.textContent = `ERROR: No senderId found for action: ${action}`;
                    output.className = 'test-output error';
                    return
                }

                this.log(`Button clicked - Action: ${action}, SenderId: ${senderId}`)
                output.textContent = `SUCCESS: Button clicked - Action: ${action}, SenderId: ${senderId}`;
                output.className = 'test-output success';

                switch (action) {
                    case 'unsubscribe':
                        this.unsubscribeSingle(senderId)
                        break
                    case 'details':
                        this.viewDetails(senderId)
                        break
                    default:
                        this.logError('Unknown action:', action)
                }
            }
            
            unsubscribeSingle(senderId) {
                const output = document.getElementById('test1-output');
                output.textContent += `\nUnsubscribeSingle called with: ${senderId}`;
            }
            
            viewDetails(senderId) {
                const output = document.getElementById('test1-output');
                output.textContent += `\nViewDetails called with: ${senderId}`;
            }
            
            getSampleData() {
                return [
                    {
                        senderId: 'hirist.tech',
                        domain: 'hirist.tech',
                        senderEmail: '<EMAIL>',
                        senderName: 'Hirist Tech',
                        frequency: 'daily',
                        lastEmailDate: '2025-04-09T00:00:00Z',
                        emailCount: 1,
                        category: 'promotional',
                        trustLevel: 60,
                        securityStatus: 'caution',
                        unsubscribeLinks: [
                            {
                                href: 'https://hirist.tech/unsubscribe',
                                trustLevel: 60,
                                securityStatus: 'caution'
                            }
                        ]
                    },
                    {
                        senderId: 'github.com',
                        domain: 'github.com',
                        senderEmail: '<EMAIL>',
                        senderName: 'GitHub',
                        frequency: 'weekly',
                        lastEmailDate: '2025-07-10T00:00:00Z',
                        emailCount: 15,
                        category: 'newsletter',
                        trustLevel: 95,
                        securityStatus: 'safe',
                        unsubscribeLinks: [
                            {
                                href: 'https://github.com/settings/notifications',
                                trustLevel: 95,
                                securityStatus: 'safe'
                            }
                        ]
                    }
                ];
            }
        }
        
        // Test functions
        function testSampleData() {
            const dashboard = new TestDashboard();
            const sampleData = dashboard.getSampleData();
            const output = document.getElementById('test2-output');
            
            output.textContent = `Sample Data (${sampleData.length} items):\n\n`;
            sampleData.forEach((item, index) => {
                output.textContent += `${index + 1}. ${item.senderName} (${item.senderId})\n`;
                output.textContent += `   Email: ${item.senderEmail}\n`;
                output.textContent += `   Trust Level: ${item.trustLevel}%\n`;
                output.textContent += `   Security Status: ${item.securityStatus}\n`;
                output.textContent += `   Unsubscribe Links: ${item.unsubscribeLinks.length}\n\n`;
            });
            
            output.className = 'test-output success';
        }
        
        async function testChromeStorage() {
            const output = document.getElementById('test3-output');
            
            try {
                // Test storage get
                const result = await chrome.storage.local.get(['subscriptionDatabase']);
                output.textContent = `Chrome Storage Get Result:\n${JSON.stringify(result, null, 2)}\n\n`;
                
                // Test storage set
                const testData = [{senderId: 'test', domain: 'test.com'}];
                await chrome.storage.local.set({ subscriptionDatabase: testData });
                output.textContent += `Chrome Storage Set: SUCCESS\n\n`;
                
                // Test storage get again
                const result2 = await chrome.storage.local.get(['subscriptionDatabase']);
                output.textContent += `Chrome Storage Get After Set:\n${JSON.stringify(result2, null, 2)}`;
                
                output.className = 'test-output success';
            } catch (error) {
                output.textContent = `Chrome Storage Error: ${error.message}`;
                output.className = 'test-output error';
            }
        }
        
        // Initialize test dashboard
        const testDashboard = new TestDashboard();
        console.log('Test dashboard initialized');
    </script>
</body>
</html>