// Manual test script for email extraction
// Run this in the Gmail browser console to test the extraction manually

// Test the email extraction functionality
async function testExtraction () {
  console.log('🧪 Testing Email Extraction...')

  // Check if the content script is loaded
  if (typeof window.gmailUnsubscriber === 'undefined') {
    console.error('❌ Gmail Unsubscriber content script not loaded')
    console.log('Make sure you:')
    console.log('1. Have the extension installed and enabled')
    console.log('2. Are on a Gmail page')
    console.log('3. The content script is properly injected')
    return
  }

  console.log('✅ Gmail Unsubscriber content script found')

  // Get the current email container
  const emailContainers = document.querySelectorAll('[data-message-id], .ii.gt div[role="listitem"], .adn, .h7')
  console.log(`📧 Found ${emailContainers.length} potential email containers`)

  if (emailContainers.length === 0) {
    console.log('⚠️ No email containers found. Try:')
    console.log('1. Opening an email in Gmail')
    console.log('2. Make sure you\'re in the email view (not inbox list)')
    console.log('3. Try expanding the email if it\'s collapsed')
    return
  }

  // Test extraction on the first email container
  const container = emailContainers[0]
  console.log('🔍 Testing extraction on first email container:', container)

  try {
    // Test sender info extraction
    const senderInfo = window.gmailUnsubscriber.extractSenderInfo(container)
    console.log('👤 Extracted sender info:', senderInfo)

    // Test unsubscribe link finding
    const unsubscribeLinks = await window.gmailUnsubscriber.findUnsubscribeLinks(container)
    console.log(`🔗 Found ${unsubscribeLinks.length} unsubscribe links:`, unsubscribeLinks)

    if (unsubscribeLinks.length > 0) {
      // Test the storage process
      await window.gmailUnsubscriber.storeExtractionResults(unsubscribeLinks, container)
      console.log('💾 Extraction results stored')

      // Check what was stored
      if (typeof chrome !== 'undefined' && chrome.storage) {
        const result = await chrome.storage.local.get(['subscriptionDatabase'])
        console.log('📊 Current subscription database:', result.subscriptionDatabase)
      }
    } else {
      console.log('⚠️ No unsubscribe links found in this email')
      console.log('Try opening an email with unsubscribe links (like newsletters or promotional emails)')
    }
  } catch (error) {
    console.error('❌ Error during extraction test:', error)
  }
}

// Test bulk scan functionality
async function testBulkScan () {
  console.log('🚀 Testing Bulk Scan...')

  if (typeof window.gmailUnsubscriber === 'undefined') {
    console.error('❌ Gmail Unsubscriber content script not loaded')
    return
  }

  try {
    // Run a small bulk scan on visible emails
    const result = await window.gmailUnsubscriber.performBulkScan(5) // Scan max 5 emails
    console.log('📊 Bulk scan results:', result)

    // Check the database after scan
    if (typeof chrome !== 'undefined' && chrome.storage) {
      const dbResult = await chrome.storage.local.get(['subscriptionDatabase'])
      console.log('💾 Subscription database after bulk scan:', dbResult.subscriptionDatabase)
    }
  } catch (error) {
    console.error('❌ Error during bulk scan test:', error)
  }
}

// Run the tests
console.log('🔧 Email Unsubscriber Test Suite')
console.log('Available functions:')
console.log('- testExtraction() - Test extraction on current email')
console.log('- testBulkScan() - Test bulk scan on visible emails')
console.log('- debugStorage() - Check current storage (if debug-storage.js is loaded)')

// Auto-run basic test
testExtraction()
