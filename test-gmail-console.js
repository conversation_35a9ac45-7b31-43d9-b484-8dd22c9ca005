// Gmail Extension Test Script - Run in Browser Console
// Copy and paste this entire script into Gmail's browser console (F12 > Console)

console.log('🚀 Starting Gmail Extension Test...')

// Test 1: Check if we're on Gmail
function testGmailDetection () {
  const hostname = window.location.hostname
  const pathname = window.location.pathname
  const href = window.location.href

  const isGmailDomain = hostname === 'mail.google.com' ||
                         hostname.endsWith('.mail.google.com')

  const isGmailApp = pathname.startsWith('/mail/') ||
                      pathname === '/mail' ||
                      href.includes('#inbox') ||
                      href.includes('#sent') ||
                      href.includes('#drafts') ||
                      document.title.toLowerCase().includes('gmail') ||
                      document.querySelector('.nH[role="main"]') !== null ||
                      document.querySelector('.zA') !== null ||
                      document.querySelector('.ii.gt') !== null

  console.log('✅ Gmail Detection Test:', {
    hostname,
    pathname,
    isGmailDomain,
    isGmailApp,
    result: isGmailDomain && isGmailApp
  })

  return isGmailDomain && isGmailApp
}

// Test 2: Find email containers
function testEmailContainers () {
  console.log('🔍 Testing Email Container Detection...')

  const selectors = [
    '.ii.gt .a3s.aiL',
    '.a3s.aiL',
    '.ii.gt',
    '[data-message-id]',
    '.zA',
    '.message',
    '.nH.if'
  ]

  const results = {}
  selectors.forEach(selector => {
    const elements = document.querySelectorAll(selector)
    results[selector] = elements.length
    if (elements.length > 0) {
      console.log(`✅ Found ${elements.length} elements with selector: ${selector}`)
    }
  })

  return results
}

// Test 3: Find unsubscribe links in current email
function testUnsubscribeLinkDetection () {
  console.log('🔗 Testing Unsubscribe Link Detection...')

  const unsubscribePatterns = [
    /unsubscribe/i,
    /opt[- ]?out/i,
    /remove[- ]?me/i,
    /manage[- ]?preferences/i,
    /email[- ]?preferences/i
  ]

  const links = document.querySelectorAll('a[href]')
  const unsubscribeLinks = []

  console.log(`📊 Scanning ${links.length} total links...`)

  links.forEach(link => {
    const href = link.getAttribute('href')
    const text = link.textContent.trim()
    const title = link.getAttribute('title') || ''

    const isUnsubscribeLink = unsubscribePatterns.some(pattern =>
      pattern.test(text) || pattern.test(title) || pattern.test(href)
    )

    if (isUnsubscribeLink) {
      unsubscribeLinks.push({
        href,
        text,
        title,
        element: link
      })
      console.log(`✅ Found unsubscribe link: "${text}" | ${href}`)
    }
  })

  console.log(`📈 Total unsubscribe links found: ${unsubscribeLinks.length}`)
  return unsubscribeLinks
}

// Test 4: Test UI injection (CSP-safe version)
function testUIInjection () {
  console.log('🎨 Testing UI Injection...')

  // Remove any existing test buttons
  const existingButton = document.querySelector('.test-unsubscriber-button')
  if (existingButton) {
    existingButton.remove()
  }

  // Create test button safely (avoiding innerHTML)
  const container = document.createElement('div')
  container.className = 'test-unsubscriber-button'
  container.style.margin = '16px 0'
  container.style.padding = '12px'
  container.style.backgroundColor = '#f8f9fa'
  container.style.borderRadius = '8px'
  container.style.border = '1px solid #e8eaed'

  const button = document.createElement('button')
  button.style.background = '#1a73e8'
  button.style.border = 'none'
  button.style.borderRadius = '20px'
  button.style.padding = '8px 16px'
  button.style.fontSize = '14px'
  button.style.fontWeight = '500'
  button.style.color = '#ffffff'
  button.style.cursor = 'pointer'
  button.style.display = 'flex'
  button.style.alignItems = 'center'
  button.style.gap = '8px'

  const icon = document.createElement('span')
  icon.textContent = '📧'

  const text = document.createElement('span')
  text.textContent = 'Test Unsubscribe Button'

  button.appendChild(icon)
  button.appendChild(text)
  container.appendChild(button)

  // Try to find email content to inject into
  const emailContent = document.querySelector('.ii.gt') ||
                         document.querySelector('.a3s.aiL') ||
                         document.querySelector('[data-message-id]') ||
                         document.body

  if (emailContent && emailContent !== document.body) {
    emailContent.appendChild(container)
    console.log('✅ Test button injected into email content')
  } else {
    document.body.appendChild(container)
    console.log('⚠️ Test button injected into document body (email content not found)')
  }

  // Add click handler
  button.addEventListener('click', () => {
    alert('Test button clicked! UI injection working.')
  })

  return container
}

// Test 5: Test sender info extraction
function testSenderExtraction () {
  console.log('👤 Testing Sender Info Extraction...')

  const senderSelectors = [
    '.go .gb .g2',
    '.yW .gb',
    '.a1.aN',
    '.qu .go .gb',
    '.yW span[email]',
    '.gb[email]'
  ]

  const senderInfo = {}

  senderSelectors.forEach(selector => {
    const element = document.querySelector(selector)
    if (element) {
      senderInfo.email = element.getAttribute('email') || element.textContent.trim()
      senderInfo.name = element.getAttribute('name') || element.getAttribute('title')
      senderInfo.selector = selector
      console.log(`✅ Found sender info using: ${selector}`, senderInfo)
    }
  })

  if (!senderInfo.email) {
    console.log('⚠️ No sender info found')
  }

  return senderInfo
}

// Run all tests
async function runAllTests () {
  console.log('🧪 Running Complete Gmail Extension Test Suite...\n')

  const results = {
    gmailDetection: testGmailDetection(),
    emailContainers: testEmailContainers(),
    unsubscribeLinks: testUnsubscribeLinkDetection(),
    senderInfo: testSenderExtraction(),
    uiInjection: testUIInjection()
  }

  console.log('\n📊 Test Results Summary:')
  console.log('========================')
  console.log(`Gmail Detection: ${results.gmailDetection ? '✅ PASS' : '❌ FAIL'}`)
  console.log(`Email Containers Found: ${Object.values(results.emailContainers).reduce((a, b) => a + b, 0)} total`)
  console.log(`Unsubscribe Links Found: ${results.unsubscribeLinks.length}`)
  console.log(`Sender Info: ${results.senderInfo.email ? '✅ FOUND' : '❌ NOT FOUND'}`)
  console.log(`UI Injection: ${results.uiInjection ? '✅ SUCCESS' : '❌ FAILED'}`)

  if (results.unsubscribeLinks.length > 0) {
    console.log('\n🔗 Unsubscribe Links Details:')
    results.unsubscribeLinks.forEach((link, i) => {
      console.log(`${i + 1}. "${link.text}" -> ${link.href}`)
    })
  }

  return results
}

// Auto-run the tests
runAllTests()

console.log('\n💡 Instructions:')
console.log('1. Open an email in Gmail')
console.log('2. Run this script again to test on that specific email')
console.log('3. Check if the test unsubscribe button appears')
console.log('4. Look for any unsubscribe links detected in the console output')
