// Enhanced Gmail Extension Test - For Testing Specific Emails
// Run this after opening a specific email in Gmail

console.log('📧 Testing Specific Email Content...')

function testEmailContent () {
  // Find the currently opened email
  const emailSelectors = [
    '.ii.gt .a3s.aiL', // Main email content
    '.a3s.aiL', // Alternative email content
    '.ii.gt', // Email container
    '.adn.ads .ii.gt', // Conversation view
    '[data-message-id] .ii.gt' // Message with ID
  ]

  let emailContent = null
  let selectorUsed = ''

  for (const selector of emailSelectors) {
    const element = document.querySelector(selector)
    if (element && element.innerHTML.trim().length > 100) {
      emailContent = element
      selectorUsed = selector
      break
    }
  }

  if (!emailContent) {
    console.log('❌ No email content found. Make sure you have opened a specific email.')
    return null
  }

  console.log(`✅ Found email content using selector: ${selectorUsed}`)
  console.log(`📏 Email content length: ${emailContent.innerHTML.length} characters`)

  return emailContent
}

function testUnsubscribeInEmail (emailContent) {
  if (!emailContent) return []

  console.log('🔍 Scanning for unsubscribe links in email content...')

  const unsubscribePatterns = [
    /unsubscribe/i,
    /opt[- ]?out/i,
    /remove[- ]?me/i,
    /manage[- ]?preferences/i,
    /email[- ]?preferences/i,
    /subscription[- ]?preferences/i,
    /update[- ]?preferences/i,
    /list[- ]?unsubscribe/i,
    /stop[- ]?emails/i,
    /cancel[- ]?subscription/i
  ]

  const links = emailContent.querySelectorAll('a[href]')
  const unsubscribeLinks = []

  console.log(`📊 Scanning ${links.length} links in email content...`)

  links.forEach((link, index) => {
    const href = link.getAttribute('href')
    const text = link.textContent.trim()
    const title = link.getAttribute('title') || ''
    const parentText = link.parentElement ? link.parentElement.textContent.trim() : ''

    const isUnsubscribe = unsubscribePatterns.some(pattern =>
      pattern.test(text) || pattern.test(title) || pattern.test(href) || pattern.test(parentText)
    )

    if (isUnsubscribe) {
      const linkInfo = {
        index: index + 1,
        href,
        text: text || '[No text]',
        title,
        parentText: parentText.substring(0, 100) + (parentText.length > 100 ? '...' : ''),
        element: link
      }

      unsubscribeLinks.push(linkInfo)

      console.log(`✅ Unsubscribe Link #${linkInfo.index}:`)
      console.log(`   Text: "${linkInfo.text}"`)
      console.log(`   URL: ${linkInfo.href}`)
      console.log(`   Context: "${linkInfo.parentText}"`)

      // Highlight the link visually
      link.style.border = '2px solid #ff4444'
      link.style.backgroundColor = '#ffffaa'
      link.style.padding = '2px'
    }
  })

  return unsubscribeLinks
}

function testContentPatterns (emailContent) {
  if (!emailContent) return

  console.log('🔎 Testing content patterns...')

  const content = emailContent.textContent.toLowerCase()

  // Test for common marketing email patterns
  const marketingPatterns = [
    'newsletter', 'unsubscribe', 'marketing', 'promotional',
    'offers', 'deals', 'subscribe', 'preferences', 'opt-out'
  ]

  const foundPatterns = marketingPatterns.filter(pattern =>
    content.includes(pattern)
  )

  console.log(`📈 Marketing patterns found: ${foundPatterns.join(', ')}`)

  // Test for email headers
  const headerInfo = {}

  // Look for sender info in email
  const senderElement = document.querySelector('.go .gb') ||
                         document.querySelector('.yW .gb') ||
                         document.querySelector('[email]')

  if (senderElement) {
    headerInfo.sender = senderElement.getAttribute('email') ||
                           senderElement.textContent.trim()
  }

  // Look for subject
  const subjectElement = document.querySelector('.hP') ||
                          document.querySelector('.bog')

  if (subjectElement) {
    headerInfo.subject = subjectElement.textContent.trim()
  }

  console.log('📧 Email Info:', headerInfo)

  return { foundPatterns, headerInfo }
}

function addTestButton (emailContent, unsubscribeLinks) {
  if (!emailContent || unsubscribeLinks.length === 0) return

  // Remove existing test button
  const existing = document.querySelector('.enhanced-test-button')
  if (existing) existing.remove()

  // Create enhanced test button
  const container = document.createElement('div')
  container.className = 'enhanced-test-button'
  container.style.cssText = `
        margin: 16px 0;
        padding: 16px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 12px;
        color: white;
        font-family: 'Google Sans', sans-serif;
        box-shadow: 0 4px 15px rgba(0,0,0,0.2);
    `

  const title = document.createElement('h3')
  title.textContent = '🎯 Extension Test Results'
  title.style.cssText = 'margin: 0 0 12px 0; font-size: 16px;'

  const stats = document.createElement('div')
  stats.textContent = `Found ${unsubscribeLinks.length} unsubscribe link(s)`
  stats.style.cssText = 'font-size: 14px; margin-bottom: 12px;'

  const button = document.createElement('button')
  button.textContent = `Test Unsubscribe Links (${unsubscribeLinks.length})`
  button.style.cssText = `
        background: white;
        color: #667eea;
        border: none;
        padding: 8px 16px;
        border-radius: 6px;
        cursor: pointer;
        font-weight: 500;
        transition: transform 0.2s;
    `

  button.onmouseover = () => button.style.transform = 'scale(1.05)'
  button.onmouseout = () => button.style.transform = 'scale(1)'

  button.onclick = () => {
    const message = unsubscribeLinks.map((link, i) =>
            `${i + 1}. "${link.text}" -> ${link.href}`
    ).join('\n\n')

    alert(`Unsubscribe Links Found:\n\n${message}`)
  }

  container.appendChild(title)
  container.appendChild(stats)
  container.appendChild(button)

  // Insert at the end of email content
  emailContent.appendChild(container)

  console.log('✅ Enhanced test button added to email')
}

// Main test function
function runEmailTest () {
  console.log('🚀 Running Enhanced Email Test...\n')

  const emailContent = testEmailContent()
  if (!emailContent) {
    console.log('❌ Test failed: No email content found')
    return
  }

  const unsubscribeLinks = testUnsubscribeInEmail(emailContent)
  const patterns = testContentPatterns(emailContent)

  addTestButton(emailContent, unsubscribeLinks)

  console.log('\n📊 Final Results:')
  console.log('==================')
  console.log('✅ Email Content: Found')
  console.log(`🔗 Unsubscribe Links: ${unsubscribeLinks.length}`)
  console.log(`📈 Marketing Patterns: ${patterns.foundPatterns.length}`)
  console.log(`📧 Sender: ${patterns.headerInfo.sender || 'Not found'}`)
  console.log(`📝 Subject: ${patterns.headerInfo.subject || 'Not found'}`)

  if (unsubscribeLinks.length > 0) {
    console.log('\n🎯 SUCCESS: Extension would work on this email!')
    console.log('💡 Links have been highlighted in yellow with red borders')
  } else {
    console.log('\n⚠️ This email may not contain unsubscribe links')
    console.log('💡 Try testing with a marketing/newsletter email')
  }

  return {
    success: emailContent !== null,
    unsubscribeCount: unsubscribeLinks.length,
    patterns: patterns.foundPatterns,
    sender: patterns.headerInfo.sender
  }
}

// Auto-run the enhanced test
runEmailTest()

console.log('\n💡 Instructions:')
console.log('1. If no unsubscribe links found, try a different email')
console.log('2. Marketing emails (newsletters, promotions) usually have unsubscribe links')
console.log('3. Look for highlighted links in the email content')
console.log('4. Click the test button to see all detected links')
