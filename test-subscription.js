// Test script for subscription system functionality
// Run this in browser console to test the subscription tier system

console.log('🧪 Testing Subscription System')

// Test 1: Check background script connection
async function testBackgroundConnection () {
  try {
    const response = await chrome.runtime.sendMessage({ action: 'ping' })
    console.log('✅ Background script connection:', response)
    return true
  } catch (error) {
    console.error('❌ Background script connection failed:', error)
    return false
  }
}

// Test 2: Get current subscription stats
async function testGetStats () {
  try {
    const response = await chrome.runtime.sendMessage({ action: 'getStats' })
    console.log('✅ Current stats:', response)

    if (response.success) {
      console.log('📊 Subscription info:', {
        tier: response.stats.subscription.tier,
        tierName: response.stats.subscription.tierName,
        monthlyLimit: response.stats.monthlyLimit,
        currentCount: response.stats.monthlyCount,
        remainingCount: response.stats.remainingCount,
        limitReached: response.stats.limitReached
      })
    }

    return response
  } catch (error) {
    console.error('❌ Get stats failed:', error)
    return null
  }
}

// Test 3: Get subscription tiers
async function testGetSubscriptionTiers () {
  try {
    const response = await chrome.runtime.sendMessage({ action: 'getSubscriptionTiers' })
    console.log('✅ Available tiers:', response)
    return response
  } catch (error) {
    console.error('❌ Get subscription tiers failed:', error)
    return null
  }
}

// Test 4: Test upgrade to premium
async function testUpgradeToPremium () {
  try {
    const response = await chrome.runtime.sendMessage({
      action: 'upgradeSubscription',
      tier: 'premium',
      duration: 'monthly'
    })
    console.log('✅ Upgrade to premium result:', response)
    return response
  } catch (error) {
    console.error('❌ Upgrade to premium failed:', error)
    return null
  }
}

// Test 5: Test upgrade to unlimited
async function testUpgradeToUnlimited () {
  try {
    const response = await chrome.runtime.sendMessage({
      action: 'upgradeSubscription',
      tier: 'unlimited',
      duration: 'monthly'
    })
    console.log('✅ Upgrade to unlimited result:', response)
    return response
  } catch (error) {
    console.error('❌ Upgrade to unlimited failed:', error)
    return null
  }
}

// Test 6: Test reset to free tier
async function testResetToFree () {
  try {
    await chrome.storage.local.set({
      subscription: {
        tier: 'free',
        validUntil: null,
        purchaseDate: null,
        autoRenew: false,
        trialUsed: false
      }
    })
    console.log('✅ Reset to free tier completed')
    return true
  } catch (error) {
    console.error('❌ Reset to free tier failed:', error)
    return false
  }
}

// Test 7: Test increment usage
async function testIncrementUsage () {
  try {
    const response = await chrome.runtime.sendMessage({ action: 'incrementUsage' })
    console.log('✅ Increment usage result:', response)
    return response
  } catch (error) {
    console.error('❌ Increment usage failed:', error)
    return null
  }
}

// Run all tests
async function runAllTests () {
  console.log('🚀 Starting subscription system tests...\n')

  // Test 1: Background connection
  console.log('Test 1: Background Script Connection')
  const connectionTest = await testBackgroundConnection()

  if (!connectionTest) {
    console.log('❌ Cannot proceed with tests - background script not responding')
    return
  }

  console.log('\n' + '='.repeat(50) + '\n')

  // Test 2: Get initial stats
  console.log('Test 2: Get Current Stats')
  const initialStats = await testGetStats()

  console.log('\n' + '='.repeat(50) + '\n')

  // Test 3: Get available tiers
  console.log('Test 3: Get Subscription Tiers')
  const tiers = await testGetSubscriptionTiers()

  console.log('\n' + '='.repeat(50) + '\n')

  // Test 4: Test usage increment
  console.log('Test 4: Test Usage Increment')
  const usageTest = await testIncrementUsage()

  console.log('\n' + '='.repeat(50) + '\n')

  // Test 5: Upgrade to premium
  console.log('Test 5: Upgrade to Premium')
  const premiumUpgrade = await testUpgradeToPremium()

  // Get stats after premium upgrade
  if (premiumUpgrade && premiumUpgrade.success) {
    console.log('📊 Stats after premium upgrade:')
    await testGetStats()
  }

  console.log('\n' + '='.repeat(50) + '\n')

  // Test 6: Upgrade to unlimited
  console.log('Test 6: Upgrade to Unlimited')
  const unlimitedUpgrade = await testUpgradeToUnlimited()

  // Get stats after unlimited upgrade
  if (unlimitedUpgrade && unlimitedUpgrade.success) {
    console.log('📊 Stats after unlimited upgrade:')
    await testGetStats()
  }

  console.log('\n' + '='.repeat(50) + '\n')

  // Test 7: Reset to free
  console.log('Test 7: Reset to Free Tier')
  const resetTest = await testResetToFree()

  // Get final stats
  if (resetTest) {
    console.log('📊 Final stats after reset:')
    await testGetStats()
  }

  console.log('\n🎉 All tests completed!')
  console.log('\n💡 To test the UI:')
  console.log('1. Open the extension popup')
  console.log('2. Check that limits and tier badges update correctly')
  console.log('3. Try scanning emails and observe the dynamic limits')
}

// Export functions for manual testing
window.subscriptionTests = {
  runAllTests,
  testBackgroundConnection,
  testGetStats,
  testGetSubscriptionTiers,
  testUpgradeToPremium,
  testUpgradeToUnlimited,
  testResetToFree,
  testIncrementUsage
}

// Auto-run tests when script is loaded
runAllTests()

console.log('\n📚 Manual test functions available:')
console.log('- subscriptionTests.testGetStats()')
console.log('- subscriptionTests.testUpgradeToPremium()')
console.log('- subscriptionTests.testUpgradeToUnlimited()')
console.log('- subscriptionTests.testResetToFree()')
console.log('- subscriptionTests.runAllTests()')
