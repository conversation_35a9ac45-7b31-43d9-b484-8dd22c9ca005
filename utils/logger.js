/**
 * Centralized logging system for Chrome Extension debugging
 * Provides persistent logging with storage and real-time visibility
 */

const ExtensionLogger = {
  // Log levels
  LEVELS: {
    ERROR: 'error',
    WARN: 'warn',
    INFO: 'info',
    DEBUG: 'debug'
  },

  // Component types
  COMPONENTS: {
    GMAIL: 'Gmail',
    POPUP: 'Popup',
    BACKGROUND: 'Background',
    CONTENT: 'Content',
    EXTRACTION: 'Extraction',
    DASHBOARD: 'Dashboard',
    STORAGE: 'Storage',
    RUNTIME: 'Runtime'
  },

  // Maximum number of logs to keep
  MAX_LOGS: 500,

  /**
   * Main logging function
   * @param {string} level - Log level (error, warn, info, debug)
   * @param {string} component - Component name
   * @param {string} message - Log message
   * @param {any} data - Additional data to log
   */
  log: function(level, component, message, data = null) {
    const timestamp = new Date().toISOString()
    const logEntry = {
      id: Date.now() + Math.random(),
      timestamp,
      level,
      component,
      message,
      data: data ? JSON.stringify(data) : null,
      url: window.location ? window.location.href : 'unknown'
    }

    // Console output with styling
    const style = this.getConsoleStyle(level)
    this.consoleOutput(level, `%c[${component}] ${message}`, style, data)

    // Store in extension storage
    this.storeLog(logEntry)

    // Broadcast to debug dashboard if open
    this.broadcastLog(logEntry)
  },

  /**
   * Convenience methods for different log levels
   */
  error: function(component, message, data) {
    this.log(this.LEVELS.ERROR, component, message, data)
  },

  warn: function(component, message, data) {
    this.log(this.LEVELS.WARN, component, message, data)
  },

  info: function(component, message, data) {
    this.log(this.LEVELS.INFO, component, message, data)
  },

  debug: function(component, message, data) {
    this.log(this.LEVELS.DEBUG, component, message, data)
  },

  /**
   * Output to console with proper method selection
   * @param {string} level - Log level
   * @param {string} message - Formatted message
   * @param {string} style - CSS style
   * @param {any} data - Additional data
   */
  consoleOutput: function(level, message, style, data) {
    switch (level) {
      case 'error':
        console.error(message, style, data)
        break
      case 'warn':
        console.warn(message, style, data)
        break
      case 'info':
        console.info(message, style, data)
        break
      case 'debug':
        console.log(message, style, data)  // Use console.log for debug since console.debug isn't universally supported
        break
      default:
        console.log(message, style, data)
    }
  },

  /**
   * Get console styling for log level
   */
  getConsoleStyle: function(level) {
    const styles = {
      error: 'color: #ff4444; font-weight: bold;',
      warn: 'color: #ffaa00; font-weight: bold;',
      info: 'color: #4444ff; font-weight: bold;',
      debug: 'color: #888888;'
    }
    return styles[level] || styles.info
  },

  /**
   * Store log entry in Chrome storage
   */
  storeLog: function(logEntry) {
    if (typeof chrome !== 'undefined' && chrome.storage) {
      chrome.storage.local.get(['debug_logs'], (result) => {
        if (chrome.runtime.lastError) {
          console.error('Storage error:', chrome.runtime.lastError)
          return
        }

        const logs = result.debug_logs || []
        logs.push(logEntry)

        // Keep only the most recent logs
        const recentLogs = logs.slice(-this.MAX_LOGS)

        chrome.storage.local.set({ debug_logs: recentLogs }, () => {
          if (chrome.runtime.lastError) {
            console.error('Storage set error:', chrome.runtime.lastError)
          }
        })
      })
    }
  },

  /**
   * Broadcast log to debug dashboard
   */
  broadcastLog: function(logEntry) {
    if (typeof chrome !== 'undefined' && chrome.runtime) {
      chrome.runtime.sendMessage({
        type: 'DEBUG_LOG',
        logEntry: logEntry
      }).catch(() => {
        // Ignore errors if no listeners
      })
    }
  },

  /**
   * Get all stored logs
   */
  getLogs: function(callback) {
    if (typeof chrome !== 'undefined' && chrome.storage) {
      chrome.storage.local.get(['debug_logs'], (result) => {
        callback(result.debug_logs || [])
      })
    } else {
      callback([])
    }
  },

  /**
   * Clear all stored logs
   */
  clearLogs: function(callback) {
    if (typeof chrome !== 'undefined' && chrome.storage) {
      chrome.storage.local.set({ debug_logs: [] }, () => {
        this.info(this.COMPONENTS.STORAGE, 'Debug logs cleared')
        if (callback) callback()
      })
    }
  },

  /**
   * Export logs as JSON
   */
  exportLogs: function(callback) {
    this.getLogs((logs) => {
      const exportData = {
        exported_at: new Date().toISOString(),
        extension_version: chrome.runtime.getManifest().version,
        logs: logs
      }
      callback(JSON.stringify(exportData, null, 2))
    })
  },

  /**
   * Filter logs by criteria
   */
  filterLogs: function(logs, filters) {
    return logs.filter(log => {
      if (filters.level && log.level !== filters.level) return false
      if (filters.component && log.component !== filters.component) return false
      if (filters.search && !log.message.toLowerCase().includes(filters.search.toLowerCase())) return false
      if (filters.since && new Date(log.timestamp) < new Date(filters.since)) return false
      return true
    })
  },

  /**
   * Get log statistics
   */
  getLogStats: function(callback) {
    this.getLogs((logs) => {
      const stats = {
        total: logs.length,
        by_level: {},
        by_component: {},
        recent_errors: []
      }

      logs.forEach(log => {
        // Count by level
        stats.by_level[log.level] = (stats.by_level[log.level] || 0) + 1

        // Count by component
        stats.by_component[log.component] = (stats.by_component[log.component] || 0) + 1

        // Collect recent errors
        if (log.level === 'error') {
          stats.recent_errors.push(log)
        }
      })

      // Keep only last 10 errors
      stats.recent_errors = stats.recent_errors.slice(-10)

      callback(stats)
    })
  }
}

// Global error handler
if (typeof window !== 'undefined') {
  window.addEventListener('error', (event) => {
    ExtensionLogger.error(ExtensionLogger.COMPONENTS.RUNTIME, 'Unhandled error', {
      message: event.message,
      filename: event.filename,
      lineno: event.lineno,
      colno: event.colno,
      stack: event.error?.stack
    })
  })

  window.addEventListener('unhandledrejection', (event) => {
    ExtensionLogger.error(ExtensionLogger.COMPONENTS.RUNTIME, 'Unhandled promise rejection', {
      reason: event.reason,
      stack: event.reason?.stack
    })
  })
}

// Make logger available globally
if (typeof window !== 'undefined') {
  window.ExtensionLogger = ExtensionLogger
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
  module.exports = ExtensionLogger
}