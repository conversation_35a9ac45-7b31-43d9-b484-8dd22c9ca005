const path = require('path')
const CopyWebpackPlugin = require('copy-webpack-plugin')
const { CleanWebpackPlugin } = require('clean-webpack-plugin')

module.exports = (env, argv) => {
  const isProduction = argv.mode === 'production'

  return {
    entry: {
      background: './background.js',
      content: './content.js',
      popup: './popup.js',
      dashboard: './dashboard.js',
      debug: './debug.js',
      devtools: './devtools.js'
    },
    output: {
      path: path.resolve(__dirname, 'dist'),
      filename: '[name].js',
      clean: true
    },
    module: {
      rules: [
        {
          test: /\.js$/,
          exclude: /node_modules/,
          use: {
            loader: 'babel-loader',
            options: {
              presets: ['@babel/preset-env']
            }
          }
        },
        {
          test: /\.css$/,
          use: ['style-loader', 'css-loader']
        }
      ]
    },
    plugins: [
      new CleanWebpackPlugin(),
      new CopyWebpackPlugin({
        patterns: [
          {
            from: 'manifest.json',
            to: 'manifest.json'
          },
          {
            from: 'popup.html',
            to: 'popup.html'
          },
          {
            from: 'popup.css',
            to: 'popup.css'
          },
          {
            from: 'styles.css',
            to: 'styles.css'
          },
          {
            from: 'dashboard.html',
            to: 'dashboard.html'
          },
          {
            from: 'dashboard.css',
            to: 'dashboard.css'
          },
          {
            from: 'debug.html',
            to: 'debug.html'
          },
          {
            from: 'devtools.html',
            to: 'devtools.html'
          },
          {
            from: 'utils',
            to: 'utils',
            noErrorOnMissing: true
          },
          {
            from: 'src',
            to: 'src',
            noErrorOnMissing: true
          },
          {
            from: 'icons',
            to: 'icons',
            noErrorOnMissing: true
          }
        ]
      })
    ],
    resolve: {
      extensions: ['.js', '.json'],
      alias: {
        '@shared': path.resolve(__dirname, 'src/shared'),
        '@content': path.resolve(__dirname, 'src/content'),
        '@popup': path.resolve(__dirname, 'src/popup'),
        '@background': path.resolve(__dirname, 'src/background')
      }
    },
    devtool: isProduction ? false : 'cheap-module-source-map',
    optimization: {
      minimize: isProduction
    }
  }
}
